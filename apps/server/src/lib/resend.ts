import { Resend } from "resend";

if (!process.env.RESEND_API_KEY) {
	throw new Error("RESEND_API_KEY is not defined");
}
export const resend = new Resend(process.env.RESEND_API_KEY);

export const sendEmail = async ({
	to,
	subject,
	template,
}: {
	to: string;
	subject: string;
	template: React.ReactNode;
}) => {
	if (process.env.NODE_ENV !== "production") {
		console.log("Not in production, skipping email send");
		return;
	}

	const { error, data } = await resend.emails.send({
		from: "SnapBack <<EMAIL>>",
		to,
		subject,
		react: template,
	});

	return { error, data };
};
