import { ORPCError, os } from "@orpc/server";
import type { UserRole } from "prisma/generated/enums";
import type { Context } from "./context";

export const o = os.$context<Context>();

export const publicProcedure = o;

const requireAuth = o.middleware(async ({ context, next }) => {
	if (!context.session?.user) {
		throw new ORPCError("UNAUTHORIZED");
	}
	return next({
		context: {
			session: context.session,
		},
	});
});

export const protectedProcedure = publicProcedure.use(requireAuth);

const requireRole = (roles: string[]) => {
	return o.middleware(async ({ context, next }) => {
		if (!context.session?.user) {
			throw new ORPCError("UNAUTHORIZED");
		}
		if (!roles.includes(context.session.user.role as UserRole)) {
			throw new ORPCError("FORBIDDEN");
		}
		return next({
			context: {
				session: context.session,
			},
		});
	});
};

export const collaboratorProcedure = protectedProcedure.use(
	requireRole(["COLL<PERSON><PERSON><PERSON><PERSON>", "ADMIN", "SUPER_ADMIN"]),
);
export const adminProcedure = protectedProcedure.use(
	requireRole(["ADMIN", "SUPER_ADMIN"]),
);
export const superAdminProcedure = protectedProcedure.use(
	requireRole(["SUPER_ADMIN"]),
);
