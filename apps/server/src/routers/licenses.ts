import { prisma, types } from "@repo/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse } from "@/lib/pagination";

const {
	LicensePaginatedOutputSchema,
	PaginateInputSchema,
	UpdateLicenseInputSchema,
	CreateLicenseInputSchema,
	IdInputSchema,
} = types;
export const licenses = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.license.findMany();
	}),
	paginate: protectedProcedure
		.input(PaginateInputSchema)
		.output(LicensePaginatedOutputSchema)
		.handler(async ({ input }) => {
			const { search, filters } = input;

			let where: Record<string, unknown> = {};

			if (search) {
				where = {
					...where,
					OR: [
						{ customerEmail: { contains: search } },
						{ customerName: { contains: search } },
					],
					...filters,
				};
			}

			return await buildListResponse(prisma.license, input, where);
		}),

	get: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.license.findUnique({
			where: {
				id: input.id,
			},
		});
	}),

	create: protectedProcedure
		.input(CreateLicenseInputSchema)
		.handler(async ({ input }) => {
			function generateLicenseKey() {
				const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
				let result = "";
				for (let i = 0; i < 24; i++) {
					result += characters.charAt(
						Math.floor(Math.random() * characters.length),
					);
				}
				return result;
			}
			const licenseKey = generateLicenseKey();

			return await prisma.license.create({
				data: {
					customerEmail: input.customerEmail,
					customerName: input.customerName,
					licenseType: input.licenseType,
					licenseKey: licenseKey,
				},
			});
		}),

	update: protectedProcedure
		.input(UpdateLicenseInputSchema)
		.handler(async ({ input }) => {
			return await prisma.license.update({
				where: {
					id: input.id,
				},
				data: {
					licenseType: input.licenseType,
					status: input.status,
					maxDevices: input.maxDevices,
				},
			});
		}),

	delete: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.license.delete({
			where: {
				id: input.id,
			},
		});
	}),
};
