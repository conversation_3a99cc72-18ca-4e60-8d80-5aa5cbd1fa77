import { prisma, types } from "@repo/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse } from "@/lib/pagination";

const {
	TicketPaginatedOutputSchema,
	PaginateInputSchema,
	IdInputSchema,
	CreateTicketInputSchema,
	UpdateTicketInputSchema,
} = types;
export const tickets = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.supportTicket.findMany();
	}),
	paginate: protectedProcedure
		.input(PaginateInputSchema)
		.output(TicketPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.supportTicket, input);
		}),

	get: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.supportTicket.findUnique({
			where: {
				id: input.id,
			},
		});
	}),
	create: protectedProcedure
		.input(CreateTicketInputSchema)
		.handler(async ({ input }) => {
			function generateTicketId() {
				const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
				let result = "SNAP-";
				const year = new Date().getFullYear().toString().slice(-2);
				result += `${year}-`;
				for (let i = 0; i < 3; i++) {
					result += characters.charAt(
						Math.floor(Math.random() * characters.length),
					);
				}
				return result;
			}
			const ticketId = generateTicketId();
			return await prisma.supportTicket.create({
				data: {
					customerEmail: input.customerEmail,
					customerName: input.customerName,
					licenseKey: input.licenseKey,
					subject: input.subject,
					description: input.description,
					category: input.category,
					priority: input.priority,
					status: input.status,
					assignedTo: input.assignedTo,
					ticketId: ticketId,
				},
			});
		}),
	update: protectedProcedure
		.input(UpdateTicketInputSchema)
		.handler(async ({ input }) => {
			return await prisma.supportTicket.update({
				where: {
					id: input.id,
				},
				data: {
					priority: input.priority,
					status: input.status,
					assignedTo: input.assignedTo,
				},
			});
		}),

	delete: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.supportTicket.delete({
			where: {
				id: input.id,
			},
		});
	}),
};
