import { AuditLogModelSchema } from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const AuditLogBaseSchema = AuditLogModelSchema;

export type AuditLogType = z.infer<typeof AuditLogBaseSchema>;
const AuditLogListSchema = createListOutputSchema(AuditLogBaseSchema);
export const logs = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.auditLog.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(AuditLogListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.auditLog, input);
		}),

	get: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.auditLog.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};
