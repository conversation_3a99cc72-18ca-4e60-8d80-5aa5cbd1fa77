import { prisma, types, zodSchemas } from "@repo/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse } from "@/lib/pagination";

const { AuditLogPaginatedOutputSchema, PaginateInputSchema, IdInputSchema } =
	types;

export const logs = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.auditLog.findMany();
	}),
	paginate: protectedProcedure
		.input(PaginateInputSchema)
		.output(AuditLogPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.auditLog, input);
		}),

	get: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.auditLog.findUnique({
			where: {
				id: input.id,
			},
		});
	}),
};
