import { prisma, types } from "@repo/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse } from "@/lib/pagination";

const { EventPaginatedOutputSchema, PaginateInputSchema, IdInputSchema } =
	types;

export const events = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.webhookEvent.findMany();
	}),
	paginate: protectedProcedure
		.input(PaginateInputSchema)
		.output(EventPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.webhookEvent, input);
		}),

	get: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.webhookEvent.findUnique({
			where: {
				id: input.id,
			},
		});
	}),
};
