import { prisma, types } from "@repo/db";
import { z } from "zod";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse } from "@/lib/pagination";

const { RateLimitPaginatedOutputSchema, PaginateInputSchema } = types;
export const rateLimits = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.rateLimit.findMany();
	}),
	paginate: protectedProcedure
		.input(PaginateInputSchema)
		.output(RateLimitPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.rateLimit, input);
		}),

	get: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.rateLimit.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};
