import { prisma, types } from "@repo/db";
import { z } from "zod";

import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse } from "@/lib/pagination";

const { PaginateInputSchema, SessionPaginatedOutputSchema, IdInputSchema } =
	types;
export const sessions = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.session.findMany();
	}),
	paginate: protectedProcedure
		.input(PaginateInputSchema)
		.output(SessionPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.session, input);
		}),

	get: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.session.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
	delete: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.session.delete({
			where: {
				id: input.id,
			},
		});
	}),
};
