import { prisma, types } from "@repo/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse } from "@/lib/pagination";

const {
	MessagePaginatedOutputSchema,
	PaginateInputSchema,
	CreateMessageInputSchema,
	IdInputSchema,
} = types;
export const messages = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.supportMessage.findMany();
	}),
	paginate: protectedProcedure
		.input(PaginateInputSchema)
		.output(MessagePaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.supportMessage, input);
		}),

	get: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.supportMessage.findUnique({
			where: {
				id: input.id,
			},
		});
	}),
	create: protectedProcedure
		.input(CreateMessageInputSchema)
		.handler(async ({ input }) => {
			return await prisma.supportMessage.create({
				data: {
					ticketId: input.ticketId,
					message: input.message,
					isInternal: input.isInternal,
					authorEmail: input.authorEmail,
					authorId: input.authorId,
				},
			});
		}),
	delete: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.supportMessage.delete({
			where: {
				id: input.id,
			},
		});
	}),
};
