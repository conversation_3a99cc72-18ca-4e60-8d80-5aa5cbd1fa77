import { UserRole } from "prisma/generated/enums";
import { UserInputSchema, UserModelSchema } from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const UserBaseSchema = UserModelSchema.omit({
	sessions: true,
	accounts: true,
	createdLicenses: true,
	sentInvitations: true,
	receivedInvitations: true,
	auditLogsAsActor: true,
	auditLogsAsTarget: true,
	processedRefunds: true,
	assignedTickets: true,
	supportMessages: true,
});

export type UserType = z.infer<typeof UserBaseSchema>;

const UserListSchema = createListOutputSchema(UserBaseSchema);
export const users = {
	getMe: protectedProcedure.handler(({ context }) => {
		return context.session?.user;
	}),

	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.user.findMany({
			where: {
				NOT: { id: { equals: context.session?.user.id } },
			},
		});
	}),

	paginate: protectedProcedure
		.input(paginationSchema)
		.output(UserListSchema)
		.handler(async ({ input, context }) => {
			const { search, filters } = input;

			let where: Record<string, unknown> = {
				NOT: { id: { equals: context.session?.user.id } },
			};

			if (search) {
				where = {
					...where,
					OR: [
						{ name: { contains: search, mode: "insensitive" } },
						{ email: { contains: search, mode: "insensitive" } },
					],
					...filters,
				};
			}

			return await buildListResponse(prisma.user, input, where);
		}),

	get: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.user.findUnique({
				where: {
					id: input.id,
				},
			});
		}),

	create: protectedProcedure
		.input(UserInputSchema)
		.handler(async ({ input }) => {
			return await prisma.user.create({
				data: {
					email: input.email,
					name: input.name,
					role: input.role,
				},
			});
		}),
	update: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				role: z.enum(UserRole).optional(),
				isActive: z.boolean().optional(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.user.update({
				where: {
					id: input.id,
				},
				data: {
					role: input.role,
					isActive: input.isActive,
				},
			});
		}),

	delete: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.user.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
