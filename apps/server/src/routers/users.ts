import { prisma, types } from "@repo/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse } from "@/lib/pagination";

const {
	PaginateInputSchema,
	UserPaginatedOutputSchema,
	CreateUserInputSchema,
	UpdateUserInputSchema,
	IdInputSchema,
} = types;

export const users = {
	getMe: protectedProcedure.handler(({ context }) => {
		return context.session?.user;
	}),

	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.user.findMany({
			where: {
				NOT: { id: { equals: context.session?.user.id } },
			},
		});
	}),

	paginate: protectedProcedure
		.input(PaginateInputSchema)
		.output(UserPaginatedOutputSchema)
		.handler(async ({ input, context }) => {
			const { search, filters } = input;

			let where: Record<string, unknown> = {
				NOT: { id: { equals: context.session?.user.id } },
			};

			if (search) {
				where = {
					...where,
					OR: [
						{ name: { contains: search, mode: "insensitive" } },
						{ email: { contains: search, mode: "insensitive" } },
					],
					...filters,
				};
			}

			return await buildListResponse(prisma.user, input, where);
		}),

	get: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.user.findUnique({
			where: {
				id: input.id,
			},
		});
	}),

	create: protectedProcedure
		.input(CreateUserInputSchema)
		.handler(async ({ input }) => {
			return await prisma.user.create({
				data: {
					email: input.email,
					name: input.name,
					role: input.role,
				},
			});
		}),
	update: protectedProcedure
		.input(UpdateUserInputSchema)
		.handler(async ({ input }) => {
			return await prisma.user.update({
				where: {
					id: input.id,
				},
				data: {
					role: input.role,
					isActive: input.isActive,
				},
			});
		}),

	delete: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.user.delete({
			where: {
				id: input.id,
			},
		});
	}),
};
