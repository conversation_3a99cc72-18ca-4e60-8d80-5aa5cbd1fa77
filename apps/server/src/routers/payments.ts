import { prisma, types } from "@repo/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse } from "@/lib/pagination";

const {
	PaymentIntentPaginatedOutputSchema,
	PaginateInputSchema,
	IdInputSchema,
} = types;

export const payments = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.paymentIntent.findMany();
	}),
	paginate: protectedProcedure
		.input(PaginateInputSchema)
		.output(PaymentIntentPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.paymentIntent, input);
		}),

	get: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.paymentIntent.findUnique({
			where: {
				id: input.id,
			},
		});
	}),
};
