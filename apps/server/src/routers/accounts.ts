import { prisma, types } from "@repo/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse } from "@/lib/pagination";

const { AccountPaginatedOutputSchema, PaginateInputSchema, IdInputSchema } =
	types;

export const accounts = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.account.findMany();
	}),
	paginate: protectedProcedure
		.input(PaginateInputSchema)
		.output(AccountPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.account, input);
		}),

	get: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.account.findUnique({
			where: {
				id: input.id,
			},
		});
	}),
	delete: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.account.delete({
			where: {
				id: input.id,
			},
		});
	}),
};
