import { prisma, types } from "@repo/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse } from "@/lib/pagination";

const {
	DeviceExpansionPaginatedOutputSchema,
	UpdateDeviceExpansionInputSchema,
	PaginateInputSchema,
	IdInputSchema,
	CreateDeviceExpansionInputSchema,
} = types;
export const expansions = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.deviceExpansion.findMany();
	}),
	paginate: protectedProcedure
		.input(PaginateInputSchema)
		.output(DeviceExpansionPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.deviceExpansion, input);
		}),

	get: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.deviceExpansion.findUnique({
			where: {
				id: input.id,
			},
		});
	}),
	create: protectedProcedure
		.input(CreateDeviceExpansionInputSchema)
		.handler(async ({ input }) => {
			return await prisma.deviceExpansion.create({
				data: {
					licenseId: input.licenseId,
					paymentIntentId: input.paymentIntentId,
					additionalDevices: input.additionalDevices,
					amount: input.amount,
				},
			});
		}),

	update: protectedProcedure
		.input(UpdateDeviceExpansionInputSchema)
		.handler(async ({ input }) => {
			return await prisma.deviceExpansion.update({
				where: {
					id: input.id,
				},
				data: {
					status: input.status,
					additionalDevices: input.additionalDevices,
					amount: input.amount,
				},
			});
		}),
	delete: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.deviceExpansion.delete({
			where: {
				id: input.id,
			},
		});
	}),
};
