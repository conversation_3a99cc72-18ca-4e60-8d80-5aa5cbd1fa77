import { prisma, types } from "@repo/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse } from "@/lib/pagination";

const {
	RefundRequestPaginatedOutputSchema,
	PaginateInputSchema,
	CreateRefundRequestInputSchema,
	UpdateRefundRequestInputSchema,
	IdInputSchema,
} = types;

export const refunds = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.refundRequest.findMany();
	}),
	paginate: protectedProcedure
		.input(PaginateInputSchema)
		.output(RefundRequestPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.refundRequest, input);
		}),

	get: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.refundRequest.findUnique({
			where: {
				id: input.id,
			},
		});
	}),

	create: protectedProcedure
		.input(CreateRefundRequestInputSchema)
		.handler(async ({ input }) => {
			return await prisma.refundRequest.create({
				data: {
					licenseId: input.licenseId,
					requestedBy: input.requestedBy,
					reason: input.reason,
				},
			});
		}),

	update: protectedProcedure
		.input(UpdateRefundRequestInputSchema)
		.handler(async ({ input }) => {
			return await prisma.refundRequest.update({
				where: {
					id: input.id,
				},
				data: {
					status: input.status,
					approvedAmount: input.approvedAmount,
					stripeRefundIds: input.stripeRefundIds,
					adminNotes: input.adminNotes,
					processedBy: input.processedBy,
				},
			});
		}),

	delete: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.refundRequest.delete({
			where: {
				id: input.id,
			},
		});
	}),
};
