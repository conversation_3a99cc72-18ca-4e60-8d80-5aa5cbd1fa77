import { prisma, types } from "@repo/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse } from "@/lib/pagination";

const {
	PaginateInputSchema,
	InvitationPaginatedOutputSchema,
	CreateInvitationInputSchema,
	UpdateInvitationInputSchema,
	IdInputSchema,
} = types;

export const invitations = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.userInvitation.findMany();
	}),
	paginate: protectedProcedure
		.input(PaginateInputSchema)
		.output(InvitationPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.userInvitation, input);
		}),

	get: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.userInvitation.findUnique({
			where: {
				id: input.id,
			},
		});
	}),

	create: protectedProcedure
		.input(CreateInvitationInputSchema)
		.handler(async ({ input }) => {
			return await prisma.userInvitation.create({
				data: {
					email: input.email,
					role: input.role,
					sentBy: input.sentBy,
					token: input.token,
					expiresAt: input.expiresAt,
				},
			});
		}),

	update: protectedProcedure
		.input(UpdateInvitationInputSchema)
		.handler(async ({ input }) => {
			return await prisma.userInvitation.update({
				where: {
					id: input.id,
				},
				data: {
					status: input.status,
				},
			});
		}),

	delete: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.userInvitation.delete({
			where: {
				id: input.id,
			},
		});
	}),
};
