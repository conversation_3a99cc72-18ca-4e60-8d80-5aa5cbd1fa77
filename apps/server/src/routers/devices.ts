import { prisma, types } from "@repo/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse } from "@/lib/pagination";

const {
	CreateDeviceInputSchema,
	UpdateDeviceInputSchema,
	DevicePaginatedOutputSchema,
	PaginateInputSchema,
	IdInputSchema,
} = types;
export const devices = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.device.findMany();
	}),
	paginate: protectedProcedure
		.input(PaginateInputSchema)
		.output(DevicePaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.device, input);
		}),

	get: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.device.findUnique({
			where: {
				id: input.id,
			},
		});
	}),

	create: protectedProcedure
		.input(CreateDeviceInputSchema)
		.handler(async ({ input }) => {
			// TODO: Hash device ID
			const salt = crypto.randomUUID();
			const deviceHash = crypto.randomUUID();
			return await prisma.device.create({
				data: {
					licenseId: input.licenseId,
					appVersion: input.appVersion,
					deviceHash,
					salt,
					deviceName: input.deviceName,
					deviceType: input.deviceType,
					deviceModel: input.deviceModel,
					operatingSystem: input.operatingSystem,
					architecture: input.architecture,
					screenResolution: input.screenResolution,
					totalMemory: input.totalMemory,
					userNickname: input.userNickname,
					location: input.location,
					notes: input.notes,
				},
			});
		}),

	update: protectedProcedure
		.input(UpdateDeviceInputSchema)
		.handler(async ({ input }) => {
			return await prisma.device.update({
				where: {
					id: input.id,
				},
				data: {
					deviceName: input.deviceName,
					deviceType: input.deviceType,
					deviceModel: input.deviceModel,
					operatingSystem: input.operatingSystem,
					architecture: input.architecture,
					screenResolution: input.screenResolution,
					totalMemory: input.totalMemory,
					userNickname: input.userNickname,
					location: input.location,
					notes: input.notes,
				},
			});
		}),
	delete: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.device.delete({
			where: {
				id: input.id,
			},
		});
	}),
};
