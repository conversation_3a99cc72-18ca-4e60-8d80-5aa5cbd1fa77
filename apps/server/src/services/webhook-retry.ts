import prisma from "@/db";
import { stripe } from "@/lib/stripe";
import { webhookProcessor } from "./webhook-processor";

/**
 * Webhook retry service for handling failed webhook events
 */
export class WebhookRetryService {
	private readonly MAX_RETRIES = 3;
	private readonly RETRY_DELAYS = [5000, 30000, 300000]; // 5s, 30s, 5min

	/**
	 * Retry failed webhook events
	 * This should be called periodically (e.g., via cron job)
	 */
	async retryFailedEvents(): Promise<void> {
		console.log("Starting webhook retry process...");

		const failedEvents = await prisma.webhookEvent.findMany({
			where: {
				processed: false,
				retryCount: { lt: this.MAX_RETRIES },
				// Only retry events that are at least 5 minutes old
				createdAt: { lt: new Date(Date.now() - 5 * 60 * 1000) },
			},
			orderBy: { createdAt: "asc" },
			take: 10, // Process 10 events at a time
		});

		if (failedEvents.length === 0) {
			console.log("No failed webhook events to retry");
			return;
		}

		console.log(`Found ${failedEvents.length} failed webhook events to retry`);

		for (const webhookEvent of failedEvents) {
			await this.retryEvent(webhookEvent);
		}

		console.log("Webhook retry process completed");
	}

	/**
	 * Retry a specific webhook event
	 */
	private async retryEvent(webhookEvent: any): Promise<void> {
		const shouldRetry = this.shouldRetryEvent(webhookEvent);

		if (!shouldRetry) {
			console.log(
				`Skipping retry for event ${webhookEvent.stripeEventId} - too soon or max retries reached`,
			);
			return;
		}

		console.log(
			`Retrying webhook event: ${webhookEvent.stripeEventId} (attempt ${webhookEvent.retryCount + 1})`,
		);

		try {
			// Fetch the event from Stripe to get fresh data
			const stripeEvent = await stripe.events.retrieve(
				webhookEvent.stripeEventId,
			);

			// Process the event
			await webhookProcessor.processEvent(stripeEvent);

			console.log(
				`Successfully retried webhook event: ${webhookEvent.stripeEventId}`,
			);
		} catch (error) {
			console.error(
				`Retry failed for webhook event ${webhookEvent.stripeEventId}:`,
				error,
			);

			// Update retry count and error message
			await prisma.webhookEvent.update({
				where: { id: webhookEvent.id },
				data: {
					retryCount: { increment: 1 },
					errorMessage: error instanceof Error ? error.message : String(error),
				},
			});

			// If we've reached max retries, mark as permanently failed
			if (webhookEvent.retryCount + 1 >= this.MAX_RETRIES) {
				console.error(
					`Max retries reached for webhook event ${webhookEvent.stripeEventId}, marking as permanently failed`,
				);
				await this.markAsPermanentlyFailed(webhookEvent.id);
			}
		}
	}

	/**
	 * Check if an event should be retried based on retry count and timing
	 */
	private shouldRetryEvent(webhookEvent: any): boolean {
		if (webhookEvent.retryCount >= this.MAX_RETRIES) {
			return false;
		}

		// Check if enough time has passed since last retry
		const timeSinceCreation =
			Date.now() - new Date(webhookEvent.createdAt).getTime();
		const requiredDelay =
			this.RETRY_DELAYS[webhookEvent.retryCount] ||
			this.RETRY_DELAYS[this.RETRY_DELAYS.length - 1];

		return timeSinceCreation >= requiredDelay;
	}

	/**
	 * Mark a webhook event as permanently failed
	 */
	private async markAsPermanentlyFailed(webhookEventId: string): Promise<void> {
		await prisma.webhookEvent.update({
			where: { id: webhookEventId },
			data: {
				errorMessage: "Max retries exceeded - permanently failed",
			},
		});

		// You could also send an alert to administrators here
		console.error(
			`Webhook event ${webhookEventId} marked as permanently failed`,
		);
	}

	/**
	 * Get webhook processing statistics
	 */
	async getWebhookStats(): Promise<{
		total: number;
		processed: number;
		failed: number;
		pending: number;
		permanentlyFailed: number;
	}> {
		const [total, processed, failed, permanentlyFailed] = await Promise.all([
			prisma.webhookEvent.count(),
			prisma.webhookEvent.count({ where: { processed: true } }),
			prisma.webhookEvent.count({
				where: {
					processed: false,
					retryCount: { lt: this.MAX_RETRIES },
				},
			}),
			prisma.webhookEvent.count({
				where: {
					processed: false,
					retryCount: { gte: this.MAX_RETRIES },
				},
			}),
		]);

		const pending = total - processed - failed - permanentlyFailed;

		return {
			total,
			processed,
			failed,
			pending,
			permanentlyFailed,
		};
	}

	/**
	 * Clean up old webhook events (older than 30 days)
	 */
	async cleanupOldEvents(): Promise<number> {
		const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

		const result = await prisma.webhookEvent.deleteMany({
			where: {
				processed: true,
				createdAt: { lt: thirtyDaysAgo },
			},
		});

		console.log(`Cleaned up ${result.count} old webhook events`);
		return result.count;
	}

	/**
	 * Manually retry a specific webhook event by ID
	 */
	async manualRetry(webhookEventId: string): Promise<boolean> {
		const webhookEvent = await prisma.webhookEvent.findUnique({
			where: { id: webhookEventId },
		});

		if (!webhookEvent) {
			throw new Error(`Webhook event not found: ${webhookEventId}`);
		}

		if (webhookEvent.processed) {
			throw new Error(`Webhook event already processed: ${webhookEventId}`);
		}

		try {
			// Fetch the event from Stripe
			const stripeEvent = await stripe.events.retrieve(
				webhookEvent.stripeEventId,
			);

			// Process the event
			await webhookProcessor.processEvent(stripeEvent);

			console.log(
				`Successfully manually retried webhook event: ${webhookEvent.stripeEventId}`,
			);
			return true;
		} catch (error) {
			console.error(
				`Manual retry failed for webhook event ${webhookEvent.stripeEventId}:`,
				error,
			);

			// Update retry count and error message
			await prisma.webhookEvent.update({
				where: { id: webhookEvent.id },
				data: {
					retryCount: { increment: 1 },
					errorMessage: error instanceof Error ? error.message : String(error),
				},
			});

			return false;
		}
	}
}

// Export singleton instance
export const webhookRetryService = new WebhookRetryService();
