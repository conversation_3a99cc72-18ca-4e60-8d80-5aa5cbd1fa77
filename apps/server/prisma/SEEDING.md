# Database Seeding Guide

This guide explains how to use the database seeding system to populate your Snapback License System database with realistic test data.

## Overview

We provide two seeding options:


1. **Comprehensive Seed** (`seed.ts`) - Full dataset with realistic volumes

Both seed files use the [Falso.js](https://ngneat.github.io/falso/) library to generate realistic fake data.

## Prerequisites

- Database must be running (use `bun db:start` to start PostgreSQL)
- Prisma schema must be pushed to database (use `bun db:push`)

## Quick Start

### From Project Root

```bash

# Run comprehensive seed (full dataset)
bun db:seed
```

### From Server Directory

```bash
cd apps/server



# Run comprehensive seed  
bun run db:seed
```

## Basic Seed (`seed-basic.ts`)

Perfect for development and basic testing scenarios.

### What it creates:

- **1 Admin User**
  - Email: `<EMAIL>`
  - Role: `SUPER_ADMIN`
  - Status: Active

- **3 Payment Intents**
  - $29.99, $49.99, $99.99 amounts
  - All successful payments
  - Test Stripe IDs

- **3 Licenses**
  - 1 TRIAL license (1 device, expires)
  - 1 PRO license (2 devices, no expiry)
  - 1 ENTERPRISE license (5 devices, no expiry)
  - All active and delivered

- **3 Devices**
  - One device per license
  - Different device types (Desktop, Laptop, Mobile)
  - Realistic device metadata

- **1 Support Ticket**
  - Open ticket about license activation
  - Assigned to admin user
  - With one response message

- **1 Audit Log Entry**
  - License creation event
  - Linked to admin user

### Test Credentials

- **Admin Login**: `<EMAIL>`
- **Customer Emails**: 
  - `<EMAIL>` (TRIAL license)
  - `<EMAIL>` (PRO license)  
  - `<EMAIL>` (ENTERPRISE license)

## Comprehensive Seed (`seed.ts`)

Generates a full dataset suitable for testing all application features and performance.

### What it creates:

- **5 Admin Users**
  - 1 Super Admin + 4 other roles
  - Realistic names and emails
  - Various activity states

- **30 Payment Intents**
  - Range of amounts ($29.99 - $199.99)
  - Various payment statuses
  - Different payment types
  - Realistic Stripe IDs

- **25 Licenses**
  - Mix of TRIAL, PRO, ENTERPRISE
  - Various statuses (Active, Expired, etc.)
  - Different device limits
  - Some with refunds/expiry dates

- **~34 Devices** (varies)
  - Multiple devices per license
  - Realistic device metadata
  - Various operating systems
  - Different device types and models

- **8 Device Expansions**
  - Additional device purchases
  - Various processing states

- **6 Refund Requests**
  - Different refund statuses
  - Admin processing notes
  - Stripe refund IDs

- **15 Support Tickets**
  - Various categories and priorities
  - Different statuses
  - Some assigned to admins

- **~42 Support Messages** (varies)
  - Multiple messages per ticket
  - Mix of customer and admin responses
  - Some internal notes

- **50 Audit Log Entries**
  - Various system actions
  - User activity tracking
  - Security events

- **10 Rate Limit Entries**
  - API rate limiting data
  - Various identifiers and actions

- **20 Webhook Events**
  - Stripe webhook processing
  - Success and failure cases

- **8 User Invitations**
  - Various invitation states
  - Different user roles

## Data Characteristics

### Realistic Data Generation

Both seed files generate realistic data using Falso.js:

- **Names**: Real-looking first and last names
- **Emails**: Valid email formats with realistic domains
- **Dates**: Appropriate past, recent, and future dates
- **IDs**: Proper format for Stripe IDs, license keys, etc.
- **Device Info**: Realistic device models, OS versions, specs
- **Text Content**: Meaningful phrases for descriptions and messages

### Relationships

All data maintains proper foreign key relationships:

- Licenses link to payment intents and admin users
- Devices belong to licenses
- Support tickets reference licenses and customers
- Audit logs track user actions on specific resources
- Messages belong to support tickets

### Data Volumes

The comprehensive seed creates realistic data volumes for testing:

- Enough data to test pagination
- Various edge cases (expired licenses, failed payments)
- Multiple relationships per entity
- Realistic distribution of statuses and types

## Customization

### Modifying Data Volumes

Edit the loop counters in the seed files:

```typescript
// In seed.ts - change these numbers
for (let i = 0; i < 30; i++) { // Payment intents
for (let i = 0; i < 25; i++) { // Licenses  
for (let i = 0; i < 15; i++) { // Support tickets
```

### Adding Custom Data

You can extend the seed files to add specific test scenarios:

```typescript
// Add a specific test license
const testLicense = await prisma.license.create({
  data: {
    licenseKey: "TEST-1234-5678-9012",
    licenseType: "PRO",
    customerEmail: "<EMAIL>",
    // ... other fields
  },
});
```

### Environment-Specific Seeds

Consider creating environment-specific seed files:

- `seed-development.ts` - For local development
- `seed-staging.ts` - For staging environment
- `seed-demo.ts` - For demo purposes

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   ```
   Error: Can't reach database server
   ```
   **Solution**: Start the database with `bun db:start`

2. **Schema Out of Sync**
   ```
   Error: Table doesn't exist
   ```
   **Solution**: Push schema with `bun db:push`

3. **Constraint Violations**
   ```
   Error: Unique constraint failed
   ```
   **Solution**: The seed clears existing data first, but if interrupted, run `bun db:reset` and try again

### Clearing Data

To start fresh:

```bash
# Reset database and run migrations
bun db:reset

# Or just clear and reseed
bun db:seed:basic  # This clears data first
```

## Best Practices

1. **Use Basic Seed for Development** - Faster and sufficient for most development tasks

2. **Use Comprehensive Seed for Testing** - Better for testing pagination, search, and edge cases

3. **Run Seeds in CI/CD** - Automate seeding in test environments

4. **Version Control Seeds** - Keep seed files in version control to ensure consistent test data

5. **Document Custom Seeds** - If you create custom seed files, document what they create

## Integration with Development Workflow

### Recommended Workflow

1. Start development session:
   ```bash
   bun db:start        # Start database
   bun db:push         # Ensure schema is current
   bun dev             # Start development server
   ```



2. Test with full dataset:
   ```bash
   bun db:seed         # Use comprehensive seed
   ```

This seeding system provides a solid foundation for development and testing of the Snapback License System!
