{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "verbatimModuleSyntax": true, "strict": true, "skipLibCheck": true, "baseUrl": "./", "paths": {"@/*": ["./src/*"], "prisma": ["node_modules/prisma"], "transactional": ["../packages/transactional/index.ts"]}, "outDir": "./dist", "types": ["bun"], "composite": true, "jsx": "react-jsx", "jsxImportSource": "react"}}