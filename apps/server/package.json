{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "bun run --hot src/index.ts", "start": "bun run dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "webhook:retry": "bun run src/scripts/webhook-retry-cron.ts", "stripe:listen": "stripe listen --forward-to http://localhost:3000/webhooks/stripe"}, "dependencies": {"@better-auth/expo": "^1.3.9", "@ngneat/falso": "^8.0.2", "@orpc/client": "^1.8.6", "@orpc/openapi": "^1.8.6", "@orpc/server": "^1.8.6", "@orpc/zod": "^1.8.6", "@prisma/client": "^6.15.0", "@prisma/extension-accelerate": "^2.0.2", "better-auth": "^1.3.9", "dotenv": "^17.2.1", "hono": "^4.9.7", "prisma-zod-generator": "^1.20.2", "resend": "^6.1.0", "stripe": "^18.5.0", "zod": "^4.0.2"}, "devDependencies": {"@types/bun": "^1.2.6", "@types/jest": "^30.0.0", "bun-types": "^1.2.22", "jest": "^30.1.3", "prisma": "^6.15.0", "tsdown": "^0.14.1", "typescript": "^5.8.2", "transactional": "workspace:*", "@snapback/db": "workspace:*"}}