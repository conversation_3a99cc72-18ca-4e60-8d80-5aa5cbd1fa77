{"name": "web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port=3001", "build": "vite build", "serve": "vite preview", "start": "vite", "check-types": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@orpc/client": "^1.8.6", "@orpc/tanstack-query": "^1.8.6", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@tabler/icons-react": "^3.35.0", "@tailwindcss/vite": "^4.0.15", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-form": "^1.12.3", "@tanstack/react-query": "^5.85.5", "@tanstack/react-router": "^1.114.25", "@tanstack/react-table": "^8.21.3", "better-auth": "^1.3.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.544.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "2.15.4", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.2.5", "use-debounce": "^10.0.6", "vaul": "^1.1.2", "zod": "^4.1.8"}, "devDependencies": {"@tanstack/react-query-devtools": "^5.85.5", "@tanstack/react-router-devtools": "^1.114.27", "@tanstack/router-plugin": "^1.114.27", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.13.13", "@types/react": "~19.1.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "postcss": "^8.5.3", "tailwindcss": "^4.0.15", "typescript": "^5.8.3", "vite": "^6.2.2"}}