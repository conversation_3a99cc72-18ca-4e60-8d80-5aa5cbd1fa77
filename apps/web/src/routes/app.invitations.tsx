import {
	IconCircleCheckFilled,
	IconCircleXFilled,
	IconClock,
	IconClockFilled,
} from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import type { Column, Row, Table } from "@tanstack/react-table";
import { Award } from "lucide-react";
import { useMemo } from "react";
import CustomTable from "@/components/custom-table";
import { DragHandle } from "@/components/drag-handle";
import TableActions from "@/components/table-actions";
import TableShowHideColumns from "@/components/table-show-hide-columns";
import SortableHeader from "@/components/table-sortable-header";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import useTable from "@/hooks/use-table";
import { formatDate } from "@/utils/format-utils";
import { orpc } from "@/utils/orpc";
import type { InvitationType } from "../../../server/src/routers/invitations";

export const Route = createFileRoute("/app/invitations")({
	component: InvitationsPage,
});

function InvitationsPage() {
	const {
		page,
		limit,
		globalFilter,
		rowSelection,
		sorting,
		handlePageSizeChange,
		handleGlobalFilterChange,
		handlePageChange,
		setRowSelection,
		setSorting,
		search,
	} = useTable();
	const invitations = useQuery(
		orpc.invitations.paginate.queryOptions({
			input: {
				page,
				limit,
				sortBy: sorting[0]?.id,
				sortOrder: sorting[0]?.desc ? "desc" : "asc",
				search: globalFilter ? search.toLowerCase() : globalFilter,
			},
		}),
	);

	const columns = useMemo(
		() => [
			{
				id: "drag",
				header: () => null,
				cell: ({ row }: { row: Row<InvitationType> }) => (
					<DragHandle id={row.original.id} />
				),
			},
			{
				id: "select",
				header: ({ table }: { table: Table<InvitationType> }) => (
					<div className="flex items-center justify-center">
						<Checkbox
							checked={
								table.getIsAllPageRowsSelected() ||
								(table.getIsSomePageRowsSelected() && "indeterminate")
							}
							onCheckedChange={(value) =>
								table.toggleAllPageRowsSelected(!!value)
							}
							aria-label="Select all"
						/>
					</div>
				),
				cell: ({ row }: { row: Row<InvitationType> }) => (
					<div className="flex items-center justify-center">
						<Checkbox
							checked={row.getIsSelected()}
							onCheckedChange={(value) => row.toggleSelected(!!value)}
							aria-label="Select row"
						/>
					</div>
				),
				enableSorting: false,
				enableHiding: false,
			},
			{
				accessorKey: "email",
				enableSorting: true,
				enableHiding: false,
				header: ({ column }: { column: Column<InvitationType> }) => (
					<SortableHeader column={column} label="Email" />
				),
			},
			{
				accessorKey: "role",
				enableSorting: true,
				header: ({ column }: { column: Column<InvitationType> }) => (
					<SortableHeader column={column} label="Role" />
				),
				cell: ({ row }: { row: Row<InvitationType> }) => (
					<Badge variant="outline" className="px-1.5 text-muted-foreground">
						<Award />
						{row.original.role}
					</Badge>
				),
			},
			{
				accessorKey: "status",
				enableSorting: true,
				header: ({ column }: { column: Column<InvitationType> }) => (
					<SortableHeader column={column} label="Status" />
				),
				cell: ({ row }: { row: Row<InvitationType> }) => (
					<Badge variant="outline" className="px-1.5 text-muted-foreground">
						{/* // we can add icons here at least 3 */}
						{row.original.status === "ACCEPTED" ? (
							<IconCircleCheckFilled className="fill-green-500 dark:fill-green-400" />
						) : row.original.status === "PENDING" ? (
							<IconClockFilled className="fill-yellow-500 dark:fill-yellow-400" />
						) : (
							<IconCircleXFilled className="fill-red-500 dark:fill-red-400" />
						)}

						{row.original.status}
					</Badge>
				),
			},
			{
				accessorKey: "sentAt",
				enableSorting: true,
				header: ({ column }: { column: Column<InvitationType> }) => (
					<SortableHeader column={column} label="Sent At" />
				),
				cell: ({ row }: { row: Row<InvitationType> }) => (
					<span>{formatDate(row.original.sentAt)}</span>
				),
			},
			{
				accessorKey: "expiresAt",
				enableSorting: true,
				header: ({ column }: { column: Column<InvitationType> }) => (
					<SortableHeader column={column} label="Expires At" />
				),
				cell: ({ row }: { row: Row<InvitationType> }) => (
					<span>{formatDate(row.original.expiresAt)}</span>
				),
			},
			{
				accessorKey: "acceptedAt",
				enableSorting: true,
				header: ({ column }: { column: Column<InvitationType> }) => (
					<SortableHeader column={column} label="Accepted At" />
				),
				cell: ({ row }: { row: Row<InvitationType> }) => (
					<span>
						{row.original.acceptedAt ? formatDate(row.original.acceptedAt) : ""}
					</span>
				),
			},
			{
				id: "actions",
				header: ({ table }: { table: Table<InvitationType> }) => (
					<TableShowHideColumns table={table} />
				),
				cell: () => (
					<TableActions
						onEdit={() => console.log("Edit")}
						onDelete={() => console.log("Delete")}
					/>
				),
			},
		],
		[],
	);

	return (
		<div className="px-4 py-4">
			<h1 className="mb-4 font-bold text-2xl">Invitations</h1>
			<CustomTable
				isLoading={invitations.isPending}
				initialData={invitations.data}
				columns={columns}
				onPageChange={handlePageChange}
				onPageSizeChange={handlePageSizeChange}
				onRowSelectionChange={setRowSelection}
				rowSelection={rowSelection}
				sorting={sorting}
				onSortingChange={setSorting}
				globalFilter={globalFilter}
				globalFilterPlaceholder="Search invitations..."
				onGlobalFilterChange={handleGlobalFilterChange}
				onAddClick={() => console.log("Add")}
				addButtonText="Add Invitation"
			/>
		</div>
	);
}
