import {
	IconCircleCheckFilled,
	IconCircleXFilled,
	IconClockFilled,
} from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import type { Column, Row, Table } from "@tanstack/react-table";
import { useMemo } from "react";
import CustomTable from "@/components/custom-table";
import { DragHandle } from "@/components/drag-handle";
import TableShowHideColumns from "@/components/table-show-hide-columns";
import SortableHeader from "@/components/table-sortable-header";
import { Badge } from "@/components/ui/badge";
import useTable from "@/hooks/use-table";
import { formatCurrency, uppercase } from "@/utils/format-utils";
import { orpc } from "@/utils/orpc";
import type { UserPaymentType } from "../../../server/src/routers/payments";

export const Route = createFileRoute("/app/payments")({
	component: PaymentsPage,
});

function PaymentsPage() {
	const {
		page,
		limit,
		globalFilter,
		rowSelection,
		sorting,
		handlePageSizeChange,
		handleGlobalFilterChange,
		handlePageChange,
		setRowSelection,
		setSorting,
		search,
	} = useTable();
	const payments = useQuery(
		orpc.payments.paginate.queryOptions({
			input: {
				page,
				limit,
				sortBy: sorting[0]?.id,
				sortOrder: sorting[0]?.desc ? "desc" : "asc",
				search: globalFilter ? search.toLowerCase() : globalFilter,
			},
		}),
	);
	const columns = useMemo(
		() => [
			{
				id: "drag",
				header: () => null,
				cell: ({ row }: { row: Row<UserPaymentType> }) => (
					<DragHandle id={row.original.id} />
				),
			},
			{
				accessorKey: "stripePaymentIntentId",
				enableSorting: true,
				enableHiding: false,
				header: ({ column }: { column: Column<UserPaymentType> }) => (
					<SortableHeader column={column} label="Stripe Payment Intent ID" />
				),
				cell: ({ row }: { row: Row<UserPaymentType> }) => (
					<span className="rounded-sm bg-muted p-1 font-mono">
						{row.original.stripePaymentIntentId}
					</span>
				),
			},
			{
				accessorKey: "stripeCheckoutSessionId",
				enableSorting: true,
				header: ({ column }: { column: Column<UserPaymentType> }) => (
					<SortableHeader column={column} label="Stripe Checkout Session ID" />
				),
				cell: ({ row }: { row: Row<UserPaymentType> }) =>
					row.original.stripeCheckoutSessionId ? (
						<span className="rounded-sm bg-muted p-1 font-mono">
							{row.original.stripeCheckoutSessionId}
						</span>
					) : null,
			},
			{
				accessorKey: "amount",
				enableSorting: true,
				header: ({ column }: { column: Column<UserPaymentType> }) => (
					<SortableHeader column={column} label="Amount" />
				),
				cell: ({ row }: { row: Row<UserPaymentType> }) => (
					<span>{formatCurrency(row.original.amount)}</span>
				),
			},
			{
				accessorKey: "currency",
				enableSorting: true,
				header: ({ column }: { column: Column<UserPaymentType> }) => (
					<SortableHeader column={column} label="Currency" />
				),
				cell: ({ row }: { row: Row<UserPaymentType> }) => (
					<span>{uppercase(row.original.currency)}</span>
				),
			},
			{
				accessorKey: "status",
				enableSorting: true,
				header: ({ column }: { column: Column<UserPaymentType> }) => (
					<SortableHeader column={column} label="Status" />
				),
				cell: ({ row }: { row: Row<UserPaymentType> }) => (
					<Badge variant="outline" className="px-1.5 text-muted-foreground">
						{row.original.status === "SUCCEEDED" ||
						row.original.status === "PROCESSING" ? (
							<IconCircleCheckFilled className="fill-green-500 dark:fill-green-400" />
						) : row.original.status === "FAILED" ||
							row.original.status === "CANCELLED" ||
							row.original.status === "REFUNDED" ? (
							<IconCircleXFilled className="fill-red-500 dark:fill-red-400" />
						) : row.original.status === "PENDING" ? (
							<IconClockFilled className="fill-yellow-500 dark:fill-yellow-400" />
						) : null}
						{row.original.status}
					</Badge>
				),
			},
			{
				accessorKey: "paymentType",
				enableSorting: true,
				header: ({ column }: { column: Column<UserPaymentType> }) => (
					<SortableHeader column={column} label="Payment Type" />
				),
				cell: ({ row }: { row: Row<UserPaymentType> }) => (
					<Badge variant="outline" className="px-1.5 text-muted-foreground">
						{row.original.paymentType}
					</Badge>
				),
			},
			{
				id: "actions",
				header: ({ table }: { table: Table<UserPaymentType> }) => (
					<TableShowHideColumns table={table} />
				),
			},
		],
		[],
	);

	return (
		<div className="px-4 py-4">
			<h1 className="mb-4 font-bold text-2xl">Payments</h1>
			<CustomTable
				isLoading={payments.isPending}
				initialData={payments.data}
				columns={columns}
				onPageChange={handlePageChange}
				onPageSizeChange={handlePageSizeChange}
				onRowSelectionChange={setRowSelection}
				rowSelection={rowSelection}
				sorting={sorting}
				onSortingChange={setSorting}
				globalFilter={globalFilter}
				globalFilterPlaceholder="Search payments..."
				onGlobalFilterChange={handleGlobalFilterChange}
				onAddClick={() => console.log("Add")}
				addButtonText="Add Payment"
			/>
		</div>
	);
}
