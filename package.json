{"name": "snapback-license-system", "private": true, "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check --write .", "prepare": "husky", "dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F server dev", "db:push": "turbo -F server db:push", "db:studio": "turbo -F server db:studio", "db:generate": "turbo -F server db:generate", "db:migrate": "turbo -F server db:migrate", "db:reset": "turbo -F server db:reset", "db:start": "turbo -F server db:start", "db:watch": "turbo -F server db:watch", "db:stop": "turbo -F server db:stop", "db:down": "turbo -F server db:down", "db:seed": "turbo -F server db:seed", "stripe:listen": "turbo -F server stripe:listen"}, "dependencies": {"react-hook-form": "^7.62.0"}, "devDependencies": {"turbo": "^2.5.4", "@biomejs/biome": "^2.2.0", "husky": "^9.1.7", "lint-staged": "^16.1.2"}, "lint-staged": {"*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}": ["biome check --write ."]}, "packageManager": "bun@1.2.21"}