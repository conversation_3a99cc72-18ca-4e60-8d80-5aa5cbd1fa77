{"name": "@snapback/db", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:start": "docker compose up -d", "db:watch": "docker compose up", "db:stop": "docker compose stop", "db:down": "docker compose down", "db:seed": "bun run prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.16.2"}, "devDependencies": {"prisma": "^6.16.2", "typescript": "^5.9.2", "zod": "^4.1.11"}}