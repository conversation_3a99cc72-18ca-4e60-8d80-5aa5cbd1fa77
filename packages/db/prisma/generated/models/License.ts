
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `License` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import type * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model License
 * 
 */
export type LicenseModel = runtime.Types.Result.DefaultSelection<Prisma.$LicensePayload>

export type AggregateLicense = {
  _count: LicenseCountAggregateOutputType | null
  _avg: LicenseAvgAggregateOutputType | null
  _sum: LicenseSumAggregateOutputType | null
  _min: LicenseMinAggregateOutputType | null
  _max: LicenseMaxAggregateOutputType | null
}

export type LicenseAvgAggregateOutputType = {
  maxDevices: number | null
  usedDevices: number | null
  totalPaidAmount: number | null
  refundAmount: number | null
}

export type LicenseSumAggregateOutputType = {
  maxDevices: number | null
  usedDevices: number | null
  totalPaidAmount: number | null
  refundAmount: number | null
}

export type LicenseMinAggregateOutputType = {
  id: string | null
  licenseKey: string | null
  licenseType: $Enums.LicenseType | null
  status: $Enums.LicenseStatus | null
  maxDevices: number | null
  usedDevices: number | null
  createdAt: Date | null
  updatedAt: Date | null
  expiresAt: Date | null
  activatedAt: Date | null
  customerEmail: string | null
  customerName: string | null
  createdBy: string | null
  paymentIntentId: string | null
  totalPaidAmount: number | null
  refundedAt: Date | null
  refundReason: string | null
  refundAmount: number | null
  emailSentAt: Date | null
  emailDeliveryStatus: string | null
}

export type LicenseMaxAggregateOutputType = {
  id: string | null
  licenseKey: string | null
  licenseType: $Enums.LicenseType | null
  status: $Enums.LicenseStatus | null
  maxDevices: number | null
  usedDevices: number | null
  createdAt: Date | null
  updatedAt: Date | null
  expiresAt: Date | null
  activatedAt: Date | null
  customerEmail: string | null
  customerName: string | null
  createdBy: string | null
  paymentIntentId: string | null
  totalPaidAmount: number | null
  refundedAt: Date | null
  refundReason: string | null
  refundAmount: number | null
  emailSentAt: Date | null
  emailDeliveryStatus: string | null
}

export type LicenseCountAggregateOutputType = {
  id: number
  licenseKey: number
  licenseType: number
  status: number
  maxDevices: number
  usedDevices: number
  createdAt: number
  updatedAt: number
  expiresAt: number
  activatedAt: number
  customerEmail: number
  customerName: number
  createdBy: number
  paymentIntentId: number
  totalPaidAmount: number
  refundedAt: number
  refundReason: number
  refundAmount: number
  emailSentAt: number
  emailDeliveryStatus: number
  _all: number
}


export type LicenseAvgAggregateInputType = {
  maxDevices?: true
  usedDevices?: true
  totalPaidAmount?: true
  refundAmount?: true
}

export type LicenseSumAggregateInputType = {
  maxDevices?: true
  usedDevices?: true
  totalPaidAmount?: true
  refundAmount?: true
}

export type LicenseMinAggregateInputType = {
  id?: true
  licenseKey?: true
  licenseType?: true
  status?: true
  maxDevices?: true
  usedDevices?: true
  createdAt?: true
  updatedAt?: true
  expiresAt?: true
  activatedAt?: true
  customerEmail?: true
  customerName?: true
  createdBy?: true
  paymentIntentId?: true
  totalPaidAmount?: true
  refundedAt?: true
  refundReason?: true
  refundAmount?: true
  emailSentAt?: true
  emailDeliveryStatus?: true
}

export type LicenseMaxAggregateInputType = {
  id?: true
  licenseKey?: true
  licenseType?: true
  status?: true
  maxDevices?: true
  usedDevices?: true
  createdAt?: true
  updatedAt?: true
  expiresAt?: true
  activatedAt?: true
  customerEmail?: true
  customerName?: true
  createdBy?: true
  paymentIntentId?: true
  totalPaidAmount?: true
  refundedAt?: true
  refundReason?: true
  refundAmount?: true
  emailSentAt?: true
  emailDeliveryStatus?: true
}

export type LicenseCountAggregateInputType = {
  id?: true
  licenseKey?: true
  licenseType?: true
  status?: true
  maxDevices?: true
  usedDevices?: true
  createdAt?: true
  updatedAt?: true
  expiresAt?: true
  activatedAt?: true
  customerEmail?: true
  customerName?: true
  createdBy?: true
  paymentIntentId?: true
  totalPaidAmount?: true
  refundedAt?: true
  refundReason?: true
  refundAmount?: true
  emailSentAt?: true
  emailDeliveryStatus?: true
  _all?: true
}

export type LicenseAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which License to aggregate.
   */
  where?: Prisma.LicenseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Licenses to fetch.
   */
  orderBy?: Prisma.LicenseOrderByWithRelationInput | Prisma.LicenseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.LicenseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Licenses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Licenses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Licenses
  **/
  _count?: true | LicenseCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: LicenseAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: LicenseSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: LicenseMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: LicenseMaxAggregateInputType
}

export type GetLicenseAggregateType<T extends LicenseAggregateArgs> = {
      [P in keyof T & keyof AggregateLicense]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateLicense[P]>
    : Prisma.GetScalarType<T[P], AggregateLicense[P]>
}




export type LicenseGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.LicenseWhereInput
  orderBy?: Prisma.LicenseOrderByWithAggregationInput | Prisma.LicenseOrderByWithAggregationInput[]
  by: Prisma.LicenseScalarFieldEnum[] | Prisma.LicenseScalarFieldEnum
  having?: Prisma.LicenseScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: LicenseCountAggregateInputType | true
  _avg?: LicenseAvgAggregateInputType
  _sum?: LicenseSumAggregateInputType
  _min?: LicenseMinAggregateInputType
  _max?: LicenseMaxAggregateInputType
}

export type LicenseGroupByOutputType = {
  id: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status: $Enums.LicenseStatus
  maxDevices: number
  usedDevices: number
  createdAt: Date
  updatedAt: Date
  expiresAt: Date | null
  activatedAt: Date | null
  customerEmail: string
  customerName: string | null
  createdBy: string | null
  paymentIntentId: string | null
  totalPaidAmount: number
  refundedAt: Date | null
  refundReason: string | null
  refundAmount: number | null
  emailSentAt: Date | null
  emailDeliveryStatus: string | null
  _count: LicenseCountAggregateOutputType | null
  _avg: LicenseAvgAggregateOutputType | null
  _sum: LicenseSumAggregateOutputType | null
  _min: LicenseMinAggregateOutputType | null
  _max: LicenseMaxAggregateOutputType | null
}

type GetLicenseGroupByPayload<T extends LicenseGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<LicenseGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof LicenseGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], LicenseGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], LicenseGroupByOutputType[P]>
      }
    >
  >



export type LicenseWhereInput = {
  AND?: Prisma.LicenseWhereInput | Prisma.LicenseWhereInput[]
  OR?: Prisma.LicenseWhereInput[]
  NOT?: Prisma.LicenseWhereInput | Prisma.LicenseWhereInput[]
  id?: Prisma.StringFilter<"License"> | string
  licenseKey?: Prisma.StringFilter<"License"> | string
  licenseType?: Prisma.EnumLicenseTypeFilter<"License"> | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFilter<"License"> | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFilter<"License"> | number
  usedDevices?: Prisma.IntFilter<"License"> | number
  createdAt?: Prisma.DateTimeFilter<"License"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"License"> | Date | string
  expiresAt?: Prisma.DateTimeNullableFilter<"License"> | Date | string | null
  activatedAt?: Prisma.DateTimeNullableFilter<"License"> | Date | string | null
  customerEmail?: Prisma.StringFilter<"License"> | string
  customerName?: Prisma.StringNullableFilter<"License"> | string | null
  createdBy?: Prisma.StringNullableFilter<"License"> | string | null
  paymentIntentId?: Prisma.StringNullableFilter<"License"> | string | null
  totalPaidAmount?: Prisma.IntFilter<"License"> | number
  refundedAt?: Prisma.DateTimeNullableFilter<"License"> | Date | string | null
  refundReason?: Prisma.StringNullableFilter<"License"> | string | null
  refundAmount?: Prisma.IntNullableFilter<"License"> | number | null
  emailSentAt?: Prisma.DateTimeNullableFilter<"License"> | Date | string | null
  emailDeliveryStatus?: Prisma.StringNullableFilter<"License"> | string | null
  createdByUser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.UserWhereInput> | null
  paymentIntent?: Prisma.XOR<Prisma.PaymentIntentNullableScalarRelationFilter, Prisma.PaymentIntentWhereInput> | null
  devices?: Prisma.DeviceListRelationFilter
  deviceExpansions?: Prisma.DeviceExpansionListRelationFilter
  refundRequests?: Prisma.RefundRequestListRelationFilter
}

export type LicenseOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  licenseKey?: Prisma.SortOrder
  licenseType?: Prisma.SortOrder
  status?: Prisma.SortOrder
  maxDevices?: Prisma.SortOrder
  usedDevices?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrderInput | Prisma.SortOrder
  activatedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrderInput | Prisma.SortOrder
  createdBy?: Prisma.SortOrderInput | Prisma.SortOrder
  paymentIntentId?: Prisma.SortOrderInput | Prisma.SortOrder
  totalPaidAmount?: Prisma.SortOrder
  refundedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  refundReason?: Prisma.SortOrderInput | Prisma.SortOrder
  refundAmount?: Prisma.SortOrderInput | Prisma.SortOrder
  emailSentAt?: Prisma.SortOrderInput | Prisma.SortOrder
  emailDeliveryStatus?: Prisma.SortOrderInput | Prisma.SortOrder
  createdByUser?: Prisma.UserOrderByWithRelationInput
  paymentIntent?: Prisma.PaymentIntentOrderByWithRelationInput
  devices?: Prisma.DeviceOrderByRelationAggregateInput
  deviceExpansions?: Prisma.DeviceExpansionOrderByRelationAggregateInput
  refundRequests?: Prisma.RefundRequestOrderByRelationAggregateInput
}

export type LicenseWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  licenseKey?: string
  AND?: Prisma.LicenseWhereInput | Prisma.LicenseWhereInput[]
  OR?: Prisma.LicenseWhereInput[]
  NOT?: Prisma.LicenseWhereInput | Prisma.LicenseWhereInput[]
  licenseType?: Prisma.EnumLicenseTypeFilter<"License"> | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFilter<"License"> | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFilter<"License"> | number
  usedDevices?: Prisma.IntFilter<"License"> | number
  createdAt?: Prisma.DateTimeFilter<"License"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"License"> | Date | string
  expiresAt?: Prisma.DateTimeNullableFilter<"License"> | Date | string | null
  activatedAt?: Prisma.DateTimeNullableFilter<"License"> | Date | string | null
  customerEmail?: Prisma.StringFilter<"License"> | string
  customerName?: Prisma.StringNullableFilter<"License"> | string | null
  createdBy?: Prisma.StringNullableFilter<"License"> | string | null
  paymentIntentId?: Prisma.StringNullableFilter<"License"> | string | null
  totalPaidAmount?: Prisma.IntFilter<"License"> | number
  refundedAt?: Prisma.DateTimeNullableFilter<"License"> | Date | string | null
  refundReason?: Prisma.StringNullableFilter<"License"> | string | null
  refundAmount?: Prisma.IntNullableFilter<"License"> | number | null
  emailSentAt?: Prisma.DateTimeNullableFilter<"License"> | Date | string | null
  emailDeliveryStatus?: Prisma.StringNullableFilter<"License"> | string | null
  createdByUser?: Prisma.XOR<Prisma.UserNullableScalarRelationFilter, Prisma.UserWhereInput> | null
  paymentIntent?: Prisma.XOR<Prisma.PaymentIntentNullableScalarRelationFilter, Prisma.PaymentIntentWhereInput> | null
  devices?: Prisma.DeviceListRelationFilter
  deviceExpansions?: Prisma.DeviceExpansionListRelationFilter
  refundRequests?: Prisma.RefundRequestListRelationFilter
}, "id" | "licenseKey">

export type LicenseOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  licenseKey?: Prisma.SortOrder
  licenseType?: Prisma.SortOrder
  status?: Prisma.SortOrder
  maxDevices?: Prisma.SortOrder
  usedDevices?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrderInput | Prisma.SortOrder
  activatedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrderInput | Prisma.SortOrder
  createdBy?: Prisma.SortOrderInput | Prisma.SortOrder
  paymentIntentId?: Prisma.SortOrderInput | Prisma.SortOrder
  totalPaidAmount?: Prisma.SortOrder
  refundedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  refundReason?: Prisma.SortOrderInput | Prisma.SortOrder
  refundAmount?: Prisma.SortOrderInput | Prisma.SortOrder
  emailSentAt?: Prisma.SortOrderInput | Prisma.SortOrder
  emailDeliveryStatus?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.LicenseCountOrderByAggregateInput
  _avg?: Prisma.LicenseAvgOrderByAggregateInput
  _max?: Prisma.LicenseMaxOrderByAggregateInput
  _min?: Prisma.LicenseMinOrderByAggregateInput
  _sum?: Prisma.LicenseSumOrderByAggregateInput
}

export type LicenseScalarWhereWithAggregatesInput = {
  AND?: Prisma.LicenseScalarWhereWithAggregatesInput | Prisma.LicenseScalarWhereWithAggregatesInput[]
  OR?: Prisma.LicenseScalarWhereWithAggregatesInput[]
  NOT?: Prisma.LicenseScalarWhereWithAggregatesInput | Prisma.LicenseScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"License"> | string
  licenseKey?: Prisma.StringWithAggregatesFilter<"License"> | string
  licenseType?: Prisma.EnumLicenseTypeWithAggregatesFilter<"License"> | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusWithAggregatesFilter<"License"> | $Enums.LicenseStatus
  maxDevices?: Prisma.IntWithAggregatesFilter<"License"> | number
  usedDevices?: Prisma.IntWithAggregatesFilter<"License"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"License"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"License"> | Date | string
  expiresAt?: Prisma.DateTimeNullableWithAggregatesFilter<"License"> | Date | string | null
  activatedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"License"> | Date | string | null
  customerEmail?: Prisma.StringWithAggregatesFilter<"License"> | string
  customerName?: Prisma.StringNullableWithAggregatesFilter<"License"> | string | null
  createdBy?: Prisma.StringNullableWithAggregatesFilter<"License"> | string | null
  paymentIntentId?: Prisma.StringNullableWithAggregatesFilter<"License"> | string | null
  totalPaidAmount?: Prisma.IntWithAggregatesFilter<"License"> | number
  refundedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"License"> | Date | string | null
  refundReason?: Prisma.StringNullableWithAggregatesFilter<"License"> | string | null
  refundAmount?: Prisma.IntNullableWithAggregatesFilter<"License"> | number | null
  emailSentAt?: Prisma.DateTimeNullableWithAggregatesFilter<"License"> | Date | string | null
  emailDeliveryStatus?: Prisma.StringNullableWithAggregatesFilter<"License"> | string | null
}

export type LicenseCreateInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
  createdByUser?: Prisma.UserCreateNestedOneWithoutCreatedLicensesInput
  paymentIntent?: Prisma.PaymentIntentCreateNestedOneWithoutLicensesInput
  devices?: Prisma.DeviceCreateNestedManyWithoutLicenseInput
  deviceExpansions?: Prisma.DeviceExpansionCreateNestedManyWithoutLicenseInput
  refundRequests?: Prisma.RefundRequestCreateNestedManyWithoutLicenseInput
}

export type LicenseUncheckedCreateInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  createdBy?: string | null
  paymentIntentId?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
  devices?: Prisma.DeviceUncheckedCreateNestedManyWithoutLicenseInput
  deviceExpansions?: Prisma.DeviceExpansionUncheckedCreateNestedManyWithoutLicenseInput
  refundRequests?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutLicenseInput
}

export type LicenseUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdByUser?: Prisma.UserUpdateOneWithoutCreatedLicensesNestedInput
  paymentIntent?: Prisma.PaymentIntentUpdateOneWithoutLicensesNestedInput
  devices?: Prisma.DeviceUpdateManyWithoutLicenseNestedInput
  deviceExpansions?: Prisma.DeviceExpansionUpdateManyWithoutLicenseNestedInput
  refundRequests?: Prisma.RefundRequestUpdateManyWithoutLicenseNestedInput
}

export type LicenseUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentIntentId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  devices?: Prisma.DeviceUncheckedUpdateManyWithoutLicenseNestedInput
  deviceExpansions?: Prisma.DeviceExpansionUncheckedUpdateManyWithoutLicenseNestedInput
  refundRequests?: Prisma.RefundRequestUncheckedUpdateManyWithoutLicenseNestedInput
}

export type LicenseCreateManyInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  createdBy?: string | null
  paymentIntentId?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
}

export type LicenseUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type LicenseUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentIntentId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type LicenseListRelationFilter = {
  every?: Prisma.LicenseWhereInput
  some?: Prisma.LicenseWhereInput
  none?: Prisma.LicenseWhereInput
}

export type LicenseOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type LicenseCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  licenseKey?: Prisma.SortOrder
  licenseType?: Prisma.SortOrder
  status?: Prisma.SortOrder
  maxDevices?: Prisma.SortOrder
  usedDevices?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  activatedAt?: Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrder
  createdBy?: Prisma.SortOrder
  paymentIntentId?: Prisma.SortOrder
  totalPaidAmount?: Prisma.SortOrder
  refundedAt?: Prisma.SortOrder
  refundReason?: Prisma.SortOrder
  refundAmount?: Prisma.SortOrder
  emailSentAt?: Prisma.SortOrder
  emailDeliveryStatus?: Prisma.SortOrder
}

export type LicenseAvgOrderByAggregateInput = {
  maxDevices?: Prisma.SortOrder
  usedDevices?: Prisma.SortOrder
  totalPaidAmount?: Prisma.SortOrder
  refundAmount?: Prisma.SortOrder
}

export type LicenseMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  licenseKey?: Prisma.SortOrder
  licenseType?: Prisma.SortOrder
  status?: Prisma.SortOrder
  maxDevices?: Prisma.SortOrder
  usedDevices?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  activatedAt?: Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrder
  createdBy?: Prisma.SortOrder
  paymentIntentId?: Prisma.SortOrder
  totalPaidAmount?: Prisma.SortOrder
  refundedAt?: Prisma.SortOrder
  refundReason?: Prisma.SortOrder
  refundAmount?: Prisma.SortOrder
  emailSentAt?: Prisma.SortOrder
  emailDeliveryStatus?: Prisma.SortOrder
}

export type LicenseMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  licenseKey?: Prisma.SortOrder
  licenseType?: Prisma.SortOrder
  status?: Prisma.SortOrder
  maxDevices?: Prisma.SortOrder
  usedDevices?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  activatedAt?: Prisma.SortOrder
  customerEmail?: Prisma.SortOrder
  customerName?: Prisma.SortOrder
  createdBy?: Prisma.SortOrder
  paymentIntentId?: Prisma.SortOrder
  totalPaidAmount?: Prisma.SortOrder
  refundedAt?: Prisma.SortOrder
  refundReason?: Prisma.SortOrder
  refundAmount?: Prisma.SortOrder
  emailSentAt?: Prisma.SortOrder
  emailDeliveryStatus?: Prisma.SortOrder
}

export type LicenseSumOrderByAggregateInput = {
  maxDevices?: Prisma.SortOrder
  usedDevices?: Prisma.SortOrder
  totalPaidAmount?: Prisma.SortOrder
  refundAmount?: Prisma.SortOrder
}

export type LicenseScalarRelationFilter = {
  is?: Prisma.LicenseWhereInput
  isNot?: Prisma.LicenseWhereInput
}

export type LicenseCreateNestedManyWithoutCreatedByUserInput = {
  create?: Prisma.XOR<Prisma.LicenseCreateWithoutCreatedByUserInput, Prisma.LicenseUncheckedCreateWithoutCreatedByUserInput> | Prisma.LicenseCreateWithoutCreatedByUserInput[] | Prisma.LicenseUncheckedCreateWithoutCreatedByUserInput[]
  connectOrCreate?: Prisma.LicenseCreateOrConnectWithoutCreatedByUserInput | Prisma.LicenseCreateOrConnectWithoutCreatedByUserInput[]
  createMany?: Prisma.LicenseCreateManyCreatedByUserInputEnvelope
  connect?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
}

export type LicenseUncheckedCreateNestedManyWithoutCreatedByUserInput = {
  create?: Prisma.XOR<Prisma.LicenseCreateWithoutCreatedByUserInput, Prisma.LicenseUncheckedCreateWithoutCreatedByUserInput> | Prisma.LicenseCreateWithoutCreatedByUserInput[] | Prisma.LicenseUncheckedCreateWithoutCreatedByUserInput[]
  connectOrCreate?: Prisma.LicenseCreateOrConnectWithoutCreatedByUserInput | Prisma.LicenseCreateOrConnectWithoutCreatedByUserInput[]
  createMany?: Prisma.LicenseCreateManyCreatedByUserInputEnvelope
  connect?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
}

export type LicenseUpdateManyWithoutCreatedByUserNestedInput = {
  create?: Prisma.XOR<Prisma.LicenseCreateWithoutCreatedByUserInput, Prisma.LicenseUncheckedCreateWithoutCreatedByUserInput> | Prisma.LicenseCreateWithoutCreatedByUserInput[] | Prisma.LicenseUncheckedCreateWithoutCreatedByUserInput[]
  connectOrCreate?: Prisma.LicenseCreateOrConnectWithoutCreatedByUserInput | Prisma.LicenseCreateOrConnectWithoutCreatedByUserInput[]
  upsert?: Prisma.LicenseUpsertWithWhereUniqueWithoutCreatedByUserInput | Prisma.LicenseUpsertWithWhereUniqueWithoutCreatedByUserInput[]
  createMany?: Prisma.LicenseCreateManyCreatedByUserInputEnvelope
  set?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  disconnect?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  delete?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  connect?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  update?: Prisma.LicenseUpdateWithWhereUniqueWithoutCreatedByUserInput | Prisma.LicenseUpdateWithWhereUniqueWithoutCreatedByUserInput[]
  updateMany?: Prisma.LicenseUpdateManyWithWhereWithoutCreatedByUserInput | Prisma.LicenseUpdateManyWithWhereWithoutCreatedByUserInput[]
  deleteMany?: Prisma.LicenseScalarWhereInput | Prisma.LicenseScalarWhereInput[]
}

export type LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInput = {
  create?: Prisma.XOR<Prisma.LicenseCreateWithoutCreatedByUserInput, Prisma.LicenseUncheckedCreateWithoutCreatedByUserInput> | Prisma.LicenseCreateWithoutCreatedByUserInput[] | Prisma.LicenseUncheckedCreateWithoutCreatedByUserInput[]
  connectOrCreate?: Prisma.LicenseCreateOrConnectWithoutCreatedByUserInput | Prisma.LicenseCreateOrConnectWithoutCreatedByUserInput[]
  upsert?: Prisma.LicenseUpsertWithWhereUniqueWithoutCreatedByUserInput | Prisma.LicenseUpsertWithWhereUniqueWithoutCreatedByUserInput[]
  createMany?: Prisma.LicenseCreateManyCreatedByUserInputEnvelope
  set?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  disconnect?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  delete?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  connect?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  update?: Prisma.LicenseUpdateWithWhereUniqueWithoutCreatedByUserInput | Prisma.LicenseUpdateWithWhereUniqueWithoutCreatedByUserInput[]
  updateMany?: Prisma.LicenseUpdateManyWithWhereWithoutCreatedByUserInput | Prisma.LicenseUpdateManyWithWhereWithoutCreatedByUserInput[]
  deleteMany?: Prisma.LicenseScalarWhereInput | Prisma.LicenseScalarWhereInput[]
}

export type LicenseCreateNestedManyWithoutPaymentIntentInput = {
  create?: Prisma.XOR<Prisma.LicenseCreateWithoutPaymentIntentInput, Prisma.LicenseUncheckedCreateWithoutPaymentIntentInput> | Prisma.LicenseCreateWithoutPaymentIntentInput[] | Prisma.LicenseUncheckedCreateWithoutPaymentIntentInput[]
  connectOrCreate?: Prisma.LicenseCreateOrConnectWithoutPaymentIntentInput | Prisma.LicenseCreateOrConnectWithoutPaymentIntentInput[]
  createMany?: Prisma.LicenseCreateManyPaymentIntentInputEnvelope
  connect?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
}

export type LicenseUncheckedCreateNestedManyWithoutPaymentIntentInput = {
  create?: Prisma.XOR<Prisma.LicenseCreateWithoutPaymentIntentInput, Prisma.LicenseUncheckedCreateWithoutPaymentIntentInput> | Prisma.LicenseCreateWithoutPaymentIntentInput[] | Prisma.LicenseUncheckedCreateWithoutPaymentIntentInput[]
  connectOrCreate?: Prisma.LicenseCreateOrConnectWithoutPaymentIntentInput | Prisma.LicenseCreateOrConnectWithoutPaymentIntentInput[]
  createMany?: Prisma.LicenseCreateManyPaymentIntentInputEnvelope
  connect?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
}

export type LicenseUpdateManyWithoutPaymentIntentNestedInput = {
  create?: Prisma.XOR<Prisma.LicenseCreateWithoutPaymentIntentInput, Prisma.LicenseUncheckedCreateWithoutPaymentIntentInput> | Prisma.LicenseCreateWithoutPaymentIntentInput[] | Prisma.LicenseUncheckedCreateWithoutPaymentIntentInput[]
  connectOrCreate?: Prisma.LicenseCreateOrConnectWithoutPaymentIntentInput | Prisma.LicenseCreateOrConnectWithoutPaymentIntentInput[]
  upsert?: Prisma.LicenseUpsertWithWhereUniqueWithoutPaymentIntentInput | Prisma.LicenseUpsertWithWhereUniqueWithoutPaymentIntentInput[]
  createMany?: Prisma.LicenseCreateManyPaymentIntentInputEnvelope
  set?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  disconnect?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  delete?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  connect?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  update?: Prisma.LicenseUpdateWithWhereUniqueWithoutPaymentIntentInput | Prisma.LicenseUpdateWithWhereUniqueWithoutPaymentIntentInput[]
  updateMany?: Prisma.LicenseUpdateManyWithWhereWithoutPaymentIntentInput | Prisma.LicenseUpdateManyWithWhereWithoutPaymentIntentInput[]
  deleteMany?: Prisma.LicenseScalarWhereInput | Prisma.LicenseScalarWhereInput[]
}

export type LicenseUncheckedUpdateManyWithoutPaymentIntentNestedInput = {
  create?: Prisma.XOR<Prisma.LicenseCreateWithoutPaymentIntentInput, Prisma.LicenseUncheckedCreateWithoutPaymentIntentInput> | Prisma.LicenseCreateWithoutPaymentIntentInput[] | Prisma.LicenseUncheckedCreateWithoutPaymentIntentInput[]
  connectOrCreate?: Prisma.LicenseCreateOrConnectWithoutPaymentIntentInput | Prisma.LicenseCreateOrConnectWithoutPaymentIntentInput[]
  upsert?: Prisma.LicenseUpsertWithWhereUniqueWithoutPaymentIntentInput | Prisma.LicenseUpsertWithWhereUniqueWithoutPaymentIntentInput[]
  createMany?: Prisma.LicenseCreateManyPaymentIntentInputEnvelope
  set?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  disconnect?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  delete?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  connect?: Prisma.LicenseWhereUniqueInput | Prisma.LicenseWhereUniqueInput[]
  update?: Prisma.LicenseUpdateWithWhereUniqueWithoutPaymentIntentInput | Prisma.LicenseUpdateWithWhereUniqueWithoutPaymentIntentInput[]
  updateMany?: Prisma.LicenseUpdateManyWithWhereWithoutPaymentIntentInput | Prisma.LicenseUpdateManyWithWhereWithoutPaymentIntentInput[]
  deleteMany?: Prisma.LicenseScalarWhereInput | Prisma.LicenseScalarWhereInput[]
}

export type EnumLicenseTypeFieldUpdateOperationsInput = {
  set?: $Enums.LicenseType
}

export type EnumLicenseStatusFieldUpdateOperationsInput = {
  set?: $Enums.LicenseStatus
}

export type NullableIntFieldUpdateOperationsInput = {
  set?: number | null
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type LicenseCreateNestedOneWithoutDevicesInput = {
  create?: Prisma.XOR<Prisma.LicenseCreateWithoutDevicesInput, Prisma.LicenseUncheckedCreateWithoutDevicesInput>
  connectOrCreate?: Prisma.LicenseCreateOrConnectWithoutDevicesInput
  connect?: Prisma.LicenseWhereUniqueInput
}

export type LicenseUpdateOneRequiredWithoutDevicesNestedInput = {
  create?: Prisma.XOR<Prisma.LicenseCreateWithoutDevicesInput, Prisma.LicenseUncheckedCreateWithoutDevicesInput>
  connectOrCreate?: Prisma.LicenseCreateOrConnectWithoutDevicesInput
  upsert?: Prisma.LicenseUpsertWithoutDevicesInput
  connect?: Prisma.LicenseWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.LicenseUpdateToOneWithWhereWithoutDevicesInput, Prisma.LicenseUpdateWithoutDevicesInput>, Prisma.LicenseUncheckedUpdateWithoutDevicesInput>
}

export type LicenseCreateNestedOneWithoutDeviceExpansionsInput = {
  create?: Prisma.XOR<Prisma.LicenseCreateWithoutDeviceExpansionsInput, Prisma.LicenseUncheckedCreateWithoutDeviceExpansionsInput>
  connectOrCreate?: Prisma.LicenseCreateOrConnectWithoutDeviceExpansionsInput
  connect?: Prisma.LicenseWhereUniqueInput
}

export type LicenseUpdateOneRequiredWithoutDeviceExpansionsNestedInput = {
  create?: Prisma.XOR<Prisma.LicenseCreateWithoutDeviceExpansionsInput, Prisma.LicenseUncheckedCreateWithoutDeviceExpansionsInput>
  connectOrCreate?: Prisma.LicenseCreateOrConnectWithoutDeviceExpansionsInput
  upsert?: Prisma.LicenseUpsertWithoutDeviceExpansionsInput
  connect?: Prisma.LicenseWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.LicenseUpdateToOneWithWhereWithoutDeviceExpansionsInput, Prisma.LicenseUpdateWithoutDeviceExpansionsInput>, Prisma.LicenseUncheckedUpdateWithoutDeviceExpansionsInput>
}

export type LicenseCreateNestedOneWithoutRefundRequestsInput = {
  create?: Prisma.XOR<Prisma.LicenseCreateWithoutRefundRequestsInput, Prisma.LicenseUncheckedCreateWithoutRefundRequestsInput>
  connectOrCreate?: Prisma.LicenseCreateOrConnectWithoutRefundRequestsInput
  connect?: Prisma.LicenseWhereUniqueInput
}

export type LicenseUpdateOneRequiredWithoutRefundRequestsNestedInput = {
  create?: Prisma.XOR<Prisma.LicenseCreateWithoutRefundRequestsInput, Prisma.LicenseUncheckedCreateWithoutRefundRequestsInput>
  connectOrCreate?: Prisma.LicenseCreateOrConnectWithoutRefundRequestsInput
  upsert?: Prisma.LicenseUpsertWithoutRefundRequestsInput
  connect?: Prisma.LicenseWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.LicenseUpdateToOneWithWhereWithoutRefundRequestsInput, Prisma.LicenseUpdateWithoutRefundRequestsInput>, Prisma.LicenseUncheckedUpdateWithoutRefundRequestsInput>
}

export type LicenseCreateWithoutCreatedByUserInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
  paymentIntent?: Prisma.PaymentIntentCreateNestedOneWithoutLicensesInput
  devices?: Prisma.DeviceCreateNestedManyWithoutLicenseInput
  deviceExpansions?: Prisma.DeviceExpansionCreateNestedManyWithoutLicenseInput
  refundRequests?: Prisma.RefundRequestCreateNestedManyWithoutLicenseInput
}

export type LicenseUncheckedCreateWithoutCreatedByUserInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  paymentIntentId?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
  devices?: Prisma.DeviceUncheckedCreateNestedManyWithoutLicenseInput
  deviceExpansions?: Prisma.DeviceExpansionUncheckedCreateNestedManyWithoutLicenseInput
  refundRequests?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutLicenseInput
}

export type LicenseCreateOrConnectWithoutCreatedByUserInput = {
  where: Prisma.LicenseWhereUniqueInput
  create: Prisma.XOR<Prisma.LicenseCreateWithoutCreatedByUserInput, Prisma.LicenseUncheckedCreateWithoutCreatedByUserInput>
}

export type LicenseCreateManyCreatedByUserInputEnvelope = {
  data: Prisma.LicenseCreateManyCreatedByUserInput | Prisma.LicenseCreateManyCreatedByUserInput[]
  skipDuplicates?: boolean
}

export type LicenseUpsertWithWhereUniqueWithoutCreatedByUserInput = {
  where: Prisma.LicenseWhereUniqueInput
  update: Prisma.XOR<Prisma.LicenseUpdateWithoutCreatedByUserInput, Prisma.LicenseUncheckedUpdateWithoutCreatedByUserInput>
  create: Prisma.XOR<Prisma.LicenseCreateWithoutCreatedByUserInput, Prisma.LicenseUncheckedCreateWithoutCreatedByUserInput>
}

export type LicenseUpdateWithWhereUniqueWithoutCreatedByUserInput = {
  where: Prisma.LicenseWhereUniqueInput
  data: Prisma.XOR<Prisma.LicenseUpdateWithoutCreatedByUserInput, Prisma.LicenseUncheckedUpdateWithoutCreatedByUserInput>
}

export type LicenseUpdateManyWithWhereWithoutCreatedByUserInput = {
  where: Prisma.LicenseScalarWhereInput
  data: Prisma.XOR<Prisma.LicenseUpdateManyMutationInput, Prisma.LicenseUncheckedUpdateManyWithoutCreatedByUserInput>
}

export type LicenseScalarWhereInput = {
  AND?: Prisma.LicenseScalarWhereInput | Prisma.LicenseScalarWhereInput[]
  OR?: Prisma.LicenseScalarWhereInput[]
  NOT?: Prisma.LicenseScalarWhereInput | Prisma.LicenseScalarWhereInput[]
  id?: Prisma.StringFilter<"License"> | string
  licenseKey?: Prisma.StringFilter<"License"> | string
  licenseType?: Prisma.EnumLicenseTypeFilter<"License"> | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFilter<"License"> | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFilter<"License"> | number
  usedDevices?: Prisma.IntFilter<"License"> | number
  createdAt?: Prisma.DateTimeFilter<"License"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"License"> | Date | string
  expiresAt?: Prisma.DateTimeNullableFilter<"License"> | Date | string | null
  activatedAt?: Prisma.DateTimeNullableFilter<"License"> | Date | string | null
  customerEmail?: Prisma.StringFilter<"License"> | string
  customerName?: Prisma.StringNullableFilter<"License"> | string | null
  createdBy?: Prisma.StringNullableFilter<"License"> | string | null
  paymentIntentId?: Prisma.StringNullableFilter<"License"> | string | null
  totalPaidAmount?: Prisma.IntFilter<"License"> | number
  refundedAt?: Prisma.DateTimeNullableFilter<"License"> | Date | string | null
  refundReason?: Prisma.StringNullableFilter<"License"> | string | null
  refundAmount?: Prisma.IntNullableFilter<"License"> | number | null
  emailSentAt?: Prisma.DateTimeNullableFilter<"License"> | Date | string | null
  emailDeliveryStatus?: Prisma.StringNullableFilter<"License"> | string | null
}

export type LicenseCreateWithoutPaymentIntentInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
  createdByUser?: Prisma.UserCreateNestedOneWithoutCreatedLicensesInput
  devices?: Prisma.DeviceCreateNestedManyWithoutLicenseInput
  deviceExpansions?: Prisma.DeviceExpansionCreateNestedManyWithoutLicenseInput
  refundRequests?: Prisma.RefundRequestCreateNestedManyWithoutLicenseInput
}

export type LicenseUncheckedCreateWithoutPaymentIntentInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  createdBy?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
  devices?: Prisma.DeviceUncheckedCreateNestedManyWithoutLicenseInput
  deviceExpansions?: Prisma.DeviceExpansionUncheckedCreateNestedManyWithoutLicenseInput
  refundRequests?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutLicenseInput
}

export type LicenseCreateOrConnectWithoutPaymentIntentInput = {
  where: Prisma.LicenseWhereUniqueInput
  create: Prisma.XOR<Prisma.LicenseCreateWithoutPaymentIntentInput, Prisma.LicenseUncheckedCreateWithoutPaymentIntentInput>
}

export type LicenseCreateManyPaymentIntentInputEnvelope = {
  data: Prisma.LicenseCreateManyPaymentIntentInput | Prisma.LicenseCreateManyPaymentIntentInput[]
  skipDuplicates?: boolean
}

export type LicenseUpsertWithWhereUniqueWithoutPaymentIntentInput = {
  where: Prisma.LicenseWhereUniqueInput
  update: Prisma.XOR<Prisma.LicenseUpdateWithoutPaymentIntentInput, Prisma.LicenseUncheckedUpdateWithoutPaymentIntentInput>
  create: Prisma.XOR<Prisma.LicenseCreateWithoutPaymentIntentInput, Prisma.LicenseUncheckedCreateWithoutPaymentIntentInput>
}

export type LicenseUpdateWithWhereUniqueWithoutPaymentIntentInput = {
  where: Prisma.LicenseWhereUniqueInput
  data: Prisma.XOR<Prisma.LicenseUpdateWithoutPaymentIntentInput, Prisma.LicenseUncheckedUpdateWithoutPaymentIntentInput>
}

export type LicenseUpdateManyWithWhereWithoutPaymentIntentInput = {
  where: Prisma.LicenseScalarWhereInput
  data: Prisma.XOR<Prisma.LicenseUpdateManyMutationInput, Prisma.LicenseUncheckedUpdateManyWithoutPaymentIntentInput>
}

export type LicenseCreateWithoutDevicesInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
  createdByUser?: Prisma.UserCreateNestedOneWithoutCreatedLicensesInput
  paymentIntent?: Prisma.PaymentIntentCreateNestedOneWithoutLicensesInput
  deviceExpansions?: Prisma.DeviceExpansionCreateNestedManyWithoutLicenseInput
  refundRequests?: Prisma.RefundRequestCreateNestedManyWithoutLicenseInput
}

export type LicenseUncheckedCreateWithoutDevicesInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  createdBy?: string | null
  paymentIntentId?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
  deviceExpansions?: Prisma.DeviceExpansionUncheckedCreateNestedManyWithoutLicenseInput
  refundRequests?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutLicenseInput
}

export type LicenseCreateOrConnectWithoutDevicesInput = {
  where: Prisma.LicenseWhereUniqueInput
  create: Prisma.XOR<Prisma.LicenseCreateWithoutDevicesInput, Prisma.LicenseUncheckedCreateWithoutDevicesInput>
}

export type LicenseUpsertWithoutDevicesInput = {
  update: Prisma.XOR<Prisma.LicenseUpdateWithoutDevicesInput, Prisma.LicenseUncheckedUpdateWithoutDevicesInput>
  create: Prisma.XOR<Prisma.LicenseCreateWithoutDevicesInput, Prisma.LicenseUncheckedCreateWithoutDevicesInput>
  where?: Prisma.LicenseWhereInput
}

export type LicenseUpdateToOneWithWhereWithoutDevicesInput = {
  where?: Prisma.LicenseWhereInput
  data: Prisma.XOR<Prisma.LicenseUpdateWithoutDevicesInput, Prisma.LicenseUncheckedUpdateWithoutDevicesInput>
}

export type LicenseUpdateWithoutDevicesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdByUser?: Prisma.UserUpdateOneWithoutCreatedLicensesNestedInput
  paymentIntent?: Prisma.PaymentIntentUpdateOneWithoutLicensesNestedInput
  deviceExpansions?: Prisma.DeviceExpansionUpdateManyWithoutLicenseNestedInput
  refundRequests?: Prisma.RefundRequestUpdateManyWithoutLicenseNestedInput
}

export type LicenseUncheckedUpdateWithoutDevicesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentIntentId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  deviceExpansions?: Prisma.DeviceExpansionUncheckedUpdateManyWithoutLicenseNestedInput
  refundRequests?: Prisma.RefundRequestUncheckedUpdateManyWithoutLicenseNestedInput
}

export type LicenseCreateWithoutDeviceExpansionsInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
  createdByUser?: Prisma.UserCreateNestedOneWithoutCreatedLicensesInput
  paymentIntent?: Prisma.PaymentIntentCreateNestedOneWithoutLicensesInput
  devices?: Prisma.DeviceCreateNestedManyWithoutLicenseInput
  refundRequests?: Prisma.RefundRequestCreateNestedManyWithoutLicenseInput
}

export type LicenseUncheckedCreateWithoutDeviceExpansionsInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  createdBy?: string | null
  paymentIntentId?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
  devices?: Prisma.DeviceUncheckedCreateNestedManyWithoutLicenseInput
  refundRequests?: Prisma.RefundRequestUncheckedCreateNestedManyWithoutLicenseInput
}

export type LicenseCreateOrConnectWithoutDeviceExpansionsInput = {
  where: Prisma.LicenseWhereUniqueInput
  create: Prisma.XOR<Prisma.LicenseCreateWithoutDeviceExpansionsInput, Prisma.LicenseUncheckedCreateWithoutDeviceExpansionsInput>
}

export type LicenseUpsertWithoutDeviceExpansionsInput = {
  update: Prisma.XOR<Prisma.LicenseUpdateWithoutDeviceExpansionsInput, Prisma.LicenseUncheckedUpdateWithoutDeviceExpansionsInput>
  create: Prisma.XOR<Prisma.LicenseCreateWithoutDeviceExpansionsInput, Prisma.LicenseUncheckedCreateWithoutDeviceExpansionsInput>
  where?: Prisma.LicenseWhereInput
}

export type LicenseUpdateToOneWithWhereWithoutDeviceExpansionsInput = {
  where?: Prisma.LicenseWhereInput
  data: Prisma.XOR<Prisma.LicenseUpdateWithoutDeviceExpansionsInput, Prisma.LicenseUncheckedUpdateWithoutDeviceExpansionsInput>
}

export type LicenseUpdateWithoutDeviceExpansionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdByUser?: Prisma.UserUpdateOneWithoutCreatedLicensesNestedInput
  paymentIntent?: Prisma.PaymentIntentUpdateOneWithoutLicensesNestedInput
  devices?: Prisma.DeviceUpdateManyWithoutLicenseNestedInput
  refundRequests?: Prisma.RefundRequestUpdateManyWithoutLicenseNestedInput
}

export type LicenseUncheckedUpdateWithoutDeviceExpansionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentIntentId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  devices?: Prisma.DeviceUncheckedUpdateManyWithoutLicenseNestedInput
  refundRequests?: Prisma.RefundRequestUncheckedUpdateManyWithoutLicenseNestedInput
}

export type LicenseCreateWithoutRefundRequestsInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
  createdByUser?: Prisma.UserCreateNestedOneWithoutCreatedLicensesInput
  paymentIntent?: Prisma.PaymentIntentCreateNestedOneWithoutLicensesInput
  devices?: Prisma.DeviceCreateNestedManyWithoutLicenseInput
  deviceExpansions?: Prisma.DeviceExpansionCreateNestedManyWithoutLicenseInput
}

export type LicenseUncheckedCreateWithoutRefundRequestsInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  createdBy?: string | null
  paymentIntentId?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
  devices?: Prisma.DeviceUncheckedCreateNestedManyWithoutLicenseInput
  deviceExpansions?: Prisma.DeviceExpansionUncheckedCreateNestedManyWithoutLicenseInput
}

export type LicenseCreateOrConnectWithoutRefundRequestsInput = {
  where: Prisma.LicenseWhereUniqueInput
  create: Prisma.XOR<Prisma.LicenseCreateWithoutRefundRequestsInput, Prisma.LicenseUncheckedCreateWithoutRefundRequestsInput>
}

export type LicenseUpsertWithoutRefundRequestsInput = {
  update: Prisma.XOR<Prisma.LicenseUpdateWithoutRefundRequestsInput, Prisma.LicenseUncheckedUpdateWithoutRefundRequestsInput>
  create: Prisma.XOR<Prisma.LicenseCreateWithoutRefundRequestsInput, Prisma.LicenseUncheckedCreateWithoutRefundRequestsInput>
  where?: Prisma.LicenseWhereInput
}

export type LicenseUpdateToOneWithWhereWithoutRefundRequestsInput = {
  where?: Prisma.LicenseWhereInput
  data: Prisma.XOR<Prisma.LicenseUpdateWithoutRefundRequestsInput, Prisma.LicenseUncheckedUpdateWithoutRefundRequestsInput>
}

export type LicenseUpdateWithoutRefundRequestsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdByUser?: Prisma.UserUpdateOneWithoutCreatedLicensesNestedInput
  paymentIntent?: Prisma.PaymentIntentUpdateOneWithoutLicensesNestedInput
  devices?: Prisma.DeviceUpdateManyWithoutLicenseNestedInput
  deviceExpansions?: Prisma.DeviceExpansionUpdateManyWithoutLicenseNestedInput
}

export type LicenseUncheckedUpdateWithoutRefundRequestsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentIntentId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  devices?: Prisma.DeviceUncheckedUpdateManyWithoutLicenseNestedInput
  deviceExpansions?: Prisma.DeviceExpansionUncheckedUpdateManyWithoutLicenseNestedInput
}

export type LicenseCreateManyCreatedByUserInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  paymentIntentId?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
}

export type LicenseUpdateWithoutCreatedByUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentIntent?: Prisma.PaymentIntentUpdateOneWithoutLicensesNestedInput
  devices?: Prisma.DeviceUpdateManyWithoutLicenseNestedInput
  deviceExpansions?: Prisma.DeviceExpansionUpdateManyWithoutLicenseNestedInput
  refundRequests?: Prisma.RefundRequestUpdateManyWithoutLicenseNestedInput
}

export type LicenseUncheckedUpdateWithoutCreatedByUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentIntentId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  devices?: Prisma.DeviceUncheckedUpdateManyWithoutLicenseNestedInput
  deviceExpansions?: Prisma.DeviceExpansionUncheckedUpdateManyWithoutLicenseNestedInput
  refundRequests?: Prisma.RefundRequestUncheckedUpdateManyWithoutLicenseNestedInput
}

export type LicenseUncheckedUpdateManyWithoutCreatedByUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  paymentIntentId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type LicenseCreateManyPaymentIntentInput = {
  id?: string
  licenseKey: string
  licenseType: $Enums.LicenseType
  status?: $Enums.LicenseStatus
  maxDevices?: number
  usedDevices?: number
  createdAt?: Date | string
  updatedAt?: Date | string
  expiresAt?: Date | string | null
  activatedAt?: Date | string | null
  customerEmail: string
  customerName?: string | null
  createdBy?: string | null
  totalPaidAmount?: number
  refundedAt?: Date | string | null
  refundReason?: string | null
  refundAmount?: number | null
  emailSentAt?: Date | string | null
  emailDeliveryStatus?: string | null
}

export type LicenseUpdateWithoutPaymentIntentInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdByUser?: Prisma.UserUpdateOneWithoutCreatedLicensesNestedInput
  devices?: Prisma.DeviceUpdateManyWithoutLicenseNestedInput
  deviceExpansions?: Prisma.DeviceExpansionUpdateManyWithoutLicenseNestedInput
  refundRequests?: Prisma.RefundRequestUpdateManyWithoutLicenseNestedInput
}

export type LicenseUncheckedUpdateWithoutPaymentIntentInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  devices?: Prisma.DeviceUncheckedUpdateManyWithoutLicenseNestedInput
  deviceExpansions?: Prisma.DeviceExpansionUncheckedUpdateManyWithoutLicenseNestedInput
  refundRequests?: Prisma.RefundRequestUncheckedUpdateManyWithoutLicenseNestedInput
}

export type LicenseUncheckedUpdateManyWithoutPaymentIntentInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  licenseKey?: Prisma.StringFieldUpdateOperationsInput | string
  licenseType?: Prisma.EnumLicenseTypeFieldUpdateOperationsInput | $Enums.LicenseType
  status?: Prisma.EnumLicenseStatusFieldUpdateOperationsInput | $Enums.LicenseStatus
  maxDevices?: Prisma.IntFieldUpdateOperationsInput | number
  usedDevices?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  activatedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  customerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  customerName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  totalPaidAmount?: Prisma.IntFieldUpdateOperationsInput | number
  refundedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  refundReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  refundAmount?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  emailSentAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  emailDeliveryStatus?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}


/**
 * Count Type LicenseCountOutputType
 */

export type LicenseCountOutputType = {
  devices: number
  deviceExpansions: number
  refundRequests: number
}

export type LicenseCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  devices?: boolean | LicenseCountOutputTypeCountDevicesArgs
  deviceExpansions?: boolean | LicenseCountOutputTypeCountDeviceExpansionsArgs
  refundRequests?: boolean | LicenseCountOutputTypeCountRefundRequestsArgs
}

/**
 * LicenseCountOutputType without action
 */
export type LicenseCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LicenseCountOutputType
   */
  select?: Prisma.LicenseCountOutputTypeSelect<ExtArgs> | null
}

/**
 * LicenseCountOutputType without action
 */
export type LicenseCountOutputTypeCountDevicesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.DeviceWhereInput
}

/**
 * LicenseCountOutputType without action
 */
export type LicenseCountOutputTypeCountDeviceExpansionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.DeviceExpansionWhereInput
}

/**
 * LicenseCountOutputType without action
 */
export type LicenseCountOutputTypeCountRefundRequestsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.RefundRequestWhereInput
}


export type LicenseSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  licenseKey?: boolean
  licenseType?: boolean
  status?: boolean
  maxDevices?: boolean
  usedDevices?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  expiresAt?: boolean
  activatedAt?: boolean
  customerEmail?: boolean
  customerName?: boolean
  createdBy?: boolean
  paymentIntentId?: boolean
  totalPaidAmount?: boolean
  refundedAt?: boolean
  refundReason?: boolean
  refundAmount?: boolean
  emailSentAt?: boolean
  emailDeliveryStatus?: boolean
  createdByUser?: boolean | Prisma.License$createdByUserArgs<ExtArgs>
  paymentIntent?: boolean | Prisma.License$paymentIntentArgs<ExtArgs>
  devices?: boolean | Prisma.License$devicesArgs<ExtArgs>
  deviceExpansions?: boolean | Prisma.License$deviceExpansionsArgs<ExtArgs>
  refundRequests?: boolean | Prisma.License$refundRequestsArgs<ExtArgs>
  _count?: boolean | Prisma.LicenseCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["license"]>

export type LicenseSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  licenseKey?: boolean
  licenseType?: boolean
  status?: boolean
  maxDevices?: boolean
  usedDevices?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  expiresAt?: boolean
  activatedAt?: boolean
  customerEmail?: boolean
  customerName?: boolean
  createdBy?: boolean
  paymentIntentId?: boolean
  totalPaidAmount?: boolean
  refundedAt?: boolean
  refundReason?: boolean
  refundAmount?: boolean
  emailSentAt?: boolean
  emailDeliveryStatus?: boolean
  createdByUser?: boolean | Prisma.License$createdByUserArgs<ExtArgs>
  paymentIntent?: boolean | Prisma.License$paymentIntentArgs<ExtArgs>
}, ExtArgs["result"]["license"]>

export type LicenseSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  licenseKey?: boolean
  licenseType?: boolean
  status?: boolean
  maxDevices?: boolean
  usedDevices?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  expiresAt?: boolean
  activatedAt?: boolean
  customerEmail?: boolean
  customerName?: boolean
  createdBy?: boolean
  paymentIntentId?: boolean
  totalPaidAmount?: boolean
  refundedAt?: boolean
  refundReason?: boolean
  refundAmount?: boolean
  emailSentAt?: boolean
  emailDeliveryStatus?: boolean
  createdByUser?: boolean | Prisma.License$createdByUserArgs<ExtArgs>
  paymentIntent?: boolean | Prisma.License$paymentIntentArgs<ExtArgs>
}, ExtArgs["result"]["license"]>

export type LicenseSelectScalar = {
  id?: boolean
  licenseKey?: boolean
  licenseType?: boolean
  status?: boolean
  maxDevices?: boolean
  usedDevices?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  expiresAt?: boolean
  activatedAt?: boolean
  customerEmail?: boolean
  customerName?: boolean
  createdBy?: boolean
  paymentIntentId?: boolean
  totalPaidAmount?: boolean
  refundedAt?: boolean
  refundReason?: boolean
  refundAmount?: boolean
  emailSentAt?: boolean
  emailDeliveryStatus?: boolean
}

export type LicenseOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "licenseKey" | "licenseType" | "status" | "maxDevices" | "usedDevices" | "createdAt" | "updatedAt" | "expiresAt" | "activatedAt" | "customerEmail" | "customerName" | "createdBy" | "paymentIntentId" | "totalPaidAmount" | "refundedAt" | "refundReason" | "refundAmount" | "emailSentAt" | "emailDeliveryStatus", ExtArgs["result"]["license"]>
export type LicenseInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  createdByUser?: boolean | Prisma.License$createdByUserArgs<ExtArgs>
  paymentIntent?: boolean | Prisma.License$paymentIntentArgs<ExtArgs>
  devices?: boolean | Prisma.License$devicesArgs<ExtArgs>
  deviceExpansions?: boolean | Prisma.License$deviceExpansionsArgs<ExtArgs>
  refundRequests?: boolean | Prisma.License$refundRequestsArgs<ExtArgs>
  _count?: boolean | Prisma.LicenseCountOutputTypeDefaultArgs<ExtArgs>
}
export type LicenseIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  createdByUser?: boolean | Prisma.License$createdByUserArgs<ExtArgs>
  paymentIntent?: boolean | Prisma.License$paymentIntentArgs<ExtArgs>
}
export type LicenseIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  createdByUser?: boolean | Prisma.License$createdByUserArgs<ExtArgs>
  paymentIntent?: boolean | Prisma.License$paymentIntentArgs<ExtArgs>
}

export type $LicensePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "License"
  objects: {
    createdByUser: Prisma.$UserPayload<ExtArgs> | null
    paymentIntent: Prisma.$PaymentIntentPayload<ExtArgs> | null
    devices: Prisma.$DevicePayload<ExtArgs>[]
    deviceExpansions: Prisma.$DeviceExpansionPayload<ExtArgs>[]
    refundRequests: Prisma.$RefundRequestPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    licenseKey: string
    licenseType: $Enums.LicenseType
    status: $Enums.LicenseStatus
    maxDevices: number
    usedDevices: number
    createdAt: Date
    updatedAt: Date
    expiresAt: Date | null
    activatedAt: Date | null
    customerEmail: string
    customerName: string | null
    createdBy: string | null
    paymentIntentId: string | null
    totalPaidAmount: number
    refundedAt: Date | null
    refundReason: string | null
    refundAmount: number | null
    emailSentAt: Date | null
    emailDeliveryStatus: string | null
  }, ExtArgs["result"]["license"]>
  composites: {}
}

export type LicenseGetPayload<S extends boolean | null | undefined | LicenseDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$LicensePayload, S>

export type LicenseCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<LicenseFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: LicenseCountAggregateInputType | true
  }

export interface LicenseDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['License'], meta: { name: 'License' } }
  /**
   * Find zero or one License that matches the filter.
   * @param {LicenseFindUniqueArgs} args - Arguments to find a License
   * @example
   * // Get one License
   * const license = await prisma.license.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends LicenseFindUniqueArgs>(args: Prisma.SelectSubset<T, LicenseFindUniqueArgs<ExtArgs>>): Prisma.Prisma__LicenseClient<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one License that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {LicenseFindUniqueOrThrowArgs} args - Arguments to find a License
   * @example
   * // Get one License
   * const license = await prisma.license.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends LicenseFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, LicenseFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__LicenseClient<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first License that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LicenseFindFirstArgs} args - Arguments to find a License
   * @example
   * // Get one License
   * const license = await prisma.license.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends LicenseFindFirstArgs>(args?: Prisma.SelectSubset<T, LicenseFindFirstArgs<ExtArgs>>): Prisma.Prisma__LicenseClient<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first License that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LicenseFindFirstOrThrowArgs} args - Arguments to find a License
   * @example
   * // Get one License
   * const license = await prisma.license.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends LicenseFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, LicenseFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__LicenseClient<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Licenses that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LicenseFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Licenses
   * const licenses = await prisma.license.findMany()
   * 
   * // Get first 10 Licenses
   * const licenses = await prisma.license.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const licenseWithIdOnly = await prisma.license.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends LicenseFindManyArgs>(args?: Prisma.SelectSubset<T, LicenseFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a License.
   * @param {LicenseCreateArgs} args - Arguments to create a License.
   * @example
   * // Create one License
   * const License = await prisma.license.create({
   *   data: {
   *     // ... data to create a License
   *   }
   * })
   * 
   */
  create<T extends LicenseCreateArgs>(args: Prisma.SelectSubset<T, LicenseCreateArgs<ExtArgs>>): Prisma.Prisma__LicenseClient<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Licenses.
   * @param {LicenseCreateManyArgs} args - Arguments to create many Licenses.
   * @example
   * // Create many Licenses
   * const license = await prisma.license.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends LicenseCreateManyArgs>(args?: Prisma.SelectSubset<T, LicenseCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Licenses and returns the data saved in the database.
   * @param {LicenseCreateManyAndReturnArgs} args - Arguments to create many Licenses.
   * @example
   * // Create many Licenses
   * const license = await prisma.license.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Licenses and only return the `id`
   * const licenseWithIdOnly = await prisma.license.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends LicenseCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, LicenseCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a License.
   * @param {LicenseDeleteArgs} args - Arguments to delete one License.
   * @example
   * // Delete one License
   * const License = await prisma.license.delete({
   *   where: {
   *     // ... filter to delete one License
   *   }
   * })
   * 
   */
  delete<T extends LicenseDeleteArgs>(args: Prisma.SelectSubset<T, LicenseDeleteArgs<ExtArgs>>): Prisma.Prisma__LicenseClient<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one License.
   * @param {LicenseUpdateArgs} args - Arguments to update one License.
   * @example
   * // Update one License
   * const license = await prisma.license.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends LicenseUpdateArgs>(args: Prisma.SelectSubset<T, LicenseUpdateArgs<ExtArgs>>): Prisma.Prisma__LicenseClient<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Licenses.
   * @param {LicenseDeleteManyArgs} args - Arguments to filter Licenses to delete.
   * @example
   * // Delete a few Licenses
   * const { count } = await prisma.license.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends LicenseDeleteManyArgs>(args?: Prisma.SelectSubset<T, LicenseDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Licenses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LicenseUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Licenses
   * const license = await prisma.license.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends LicenseUpdateManyArgs>(args: Prisma.SelectSubset<T, LicenseUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Licenses and returns the data updated in the database.
   * @param {LicenseUpdateManyAndReturnArgs} args - Arguments to update many Licenses.
   * @example
   * // Update many Licenses
   * const license = await prisma.license.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Licenses and only return the `id`
   * const licenseWithIdOnly = await prisma.license.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends LicenseUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, LicenseUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one License.
   * @param {LicenseUpsertArgs} args - Arguments to update or create a License.
   * @example
   * // Update or create a License
   * const license = await prisma.license.upsert({
   *   create: {
   *     // ... data to create a License
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the License we want to update
   *   }
   * })
   */
  upsert<T extends LicenseUpsertArgs>(args: Prisma.SelectSubset<T, LicenseUpsertArgs<ExtArgs>>): Prisma.Prisma__LicenseClient<runtime.Types.Result.GetResult<Prisma.$LicensePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Licenses.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LicenseCountArgs} args - Arguments to filter Licenses to count.
   * @example
   * // Count the number of Licenses
   * const count = await prisma.license.count({
   *   where: {
   *     // ... the filter for the Licenses we want to count
   *   }
   * })
  **/
  count<T extends LicenseCountArgs>(
    args?: Prisma.Subset<T, LicenseCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], LicenseCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a License.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LicenseAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends LicenseAggregateArgs>(args: Prisma.Subset<T, LicenseAggregateArgs>): Prisma.PrismaPromise<GetLicenseAggregateType<T>>

  /**
   * Group by License.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LicenseGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends LicenseGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: LicenseGroupByArgs['orderBy'] }
      : { orderBy?: LicenseGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, LicenseGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetLicenseGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the License model
 */
readonly fields: LicenseFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for License.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__LicenseClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  createdByUser<T extends Prisma.License$createdByUserArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.License$createdByUserArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  paymentIntent<T extends Prisma.License$paymentIntentArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.License$paymentIntentArgs<ExtArgs>>): Prisma.Prisma__PaymentIntentClient<runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  devices<T extends Prisma.License$devicesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.License$devicesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$DevicePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  deviceExpansions<T extends Prisma.License$deviceExpansionsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.License$deviceExpansionsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$DeviceExpansionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  refundRequests<T extends Prisma.License$refundRequestsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.License$refundRequestsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RefundRequestPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the License model
 */
export interface LicenseFieldRefs {
  readonly id: Prisma.FieldRef<"License", 'String'>
  readonly licenseKey: Prisma.FieldRef<"License", 'String'>
  readonly licenseType: Prisma.FieldRef<"License", 'LicenseType'>
  readonly status: Prisma.FieldRef<"License", 'LicenseStatus'>
  readonly maxDevices: Prisma.FieldRef<"License", 'Int'>
  readonly usedDevices: Prisma.FieldRef<"License", 'Int'>
  readonly createdAt: Prisma.FieldRef<"License", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"License", 'DateTime'>
  readonly expiresAt: Prisma.FieldRef<"License", 'DateTime'>
  readonly activatedAt: Prisma.FieldRef<"License", 'DateTime'>
  readonly customerEmail: Prisma.FieldRef<"License", 'String'>
  readonly customerName: Prisma.FieldRef<"License", 'String'>
  readonly createdBy: Prisma.FieldRef<"License", 'String'>
  readonly paymentIntentId: Prisma.FieldRef<"License", 'String'>
  readonly totalPaidAmount: Prisma.FieldRef<"License", 'Int'>
  readonly refundedAt: Prisma.FieldRef<"License", 'DateTime'>
  readonly refundReason: Prisma.FieldRef<"License", 'String'>
  readonly refundAmount: Prisma.FieldRef<"License", 'Int'>
  readonly emailSentAt: Prisma.FieldRef<"License", 'DateTime'>
  readonly emailDeliveryStatus: Prisma.FieldRef<"License", 'String'>
}
    

// Custom InputTypes
/**
 * License findUnique
 */
export type LicenseFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the License
   */
  select?: Prisma.LicenseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the License
   */
  omit?: Prisma.LicenseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LicenseInclude<ExtArgs> | null
  /**
   * Filter, which License to fetch.
   */
  where: Prisma.LicenseWhereUniqueInput
}

/**
 * License findUniqueOrThrow
 */
export type LicenseFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the License
   */
  select?: Prisma.LicenseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the License
   */
  omit?: Prisma.LicenseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LicenseInclude<ExtArgs> | null
  /**
   * Filter, which License to fetch.
   */
  where: Prisma.LicenseWhereUniqueInput
}

/**
 * License findFirst
 */
export type LicenseFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the License
   */
  select?: Prisma.LicenseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the License
   */
  omit?: Prisma.LicenseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LicenseInclude<ExtArgs> | null
  /**
   * Filter, which License to fetch.
   */
  where?: Prisma.LicenseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Licenses to fetch.
   */
  orderBy?: Prisma.LicenseOrderByWithRelationInput | Prisma.LicenseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Licenses.
   */
  cursor?: Prisma.LicenseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Licenses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Licenses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Licenses.
   */
  distinct?: Prisma.LicenseScalarFieldEnum | Prisma.LicenseScalarFieldEnum[]
}

/**
 * License findFirstOrThrow
 */
export type LicenseFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the License
   */
  select?: Prisma.LicenseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the License
   */
  omit?: Prisma.LicenseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LicenseInclude<ExtArgs> | null
  /**
   * Filter, which License to fetch.
   */
  where?: Prisma.LicenseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Licenses to fetch.
   */
  orderBy?: Prisma.LicenseOrderByWithRelationInput | Prisma.LicenseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Licenses.
   */
  cursor?: Prisma.LicenseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Licenses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Licenses.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Licenses.
   */
  distinct?: Prisma.LicenseScalarFieldEnum | Prisma.LicenseScalarFieldEnum[]
}

/**
 * License findMany
 */
export type LicenseFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the License
   */
  select?: Prisma.LicenseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the License
   */
  omit?: Prisma.LicenseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LicenseInclude<ExtArgs> | null
  /**
   * Filter, which Licenses to fetch.
   */
  where?: Prisma.LicenseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Licenses to fetch.
   */
  orderBy?: Prisma.LicenseOrderByWithRelationInput | Prisma.LicenseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Licenses.
   */
  cursor?: Prisma.LicenseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Licenses from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Licenses.
   */
  skip?: number
  distinct?: Prisma.LicenseScalarFieldEnum | Prisma.LicenseScalarFieldEnum[]
}

/**
 * License create
 */
export type LicenseCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the License
   */
  select?: Prisma.LicenseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the License
   */
  omit?: Prisma.LicenseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LicenseInclude<ExtArgs> | null
  /**
   * The data needed to create a License.
   */
  data: Prisma.XOR<Prisma.LicenseCreateInput, Prisma.LicenseUncheckedCreateInput>
}

/**
 * License createMany
 */
export type LicenseCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Licenses.
   */
  data: Prisma.LicenseCreateManyInput | Prisma.LicenseCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * License createManyAndReturn
 */
export type LicenseCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the License
   */
  select?: Prisma.LicenseSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the License
   */
  omit?: Prisma.LicenseOmit<ExtArgs> | null
  /**
   * The data used to create many Licenses.
   */
  data: Prisma.LicenseCreateManyInput | Prisma.LicenseCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LicenseIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * License update
 */
export type LicenseUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the License
   */
  select?: Prisma.LicenseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the License
   */
  omit?: Prisma.LicenseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LicenseInclude<ExtArgs> | null
  /**
   * The data needed to update a License.
   */
  data: Prisma.XOR<Prisma.LicenseUpdateInput, Prisma.LicenseUncheckedUpdateInput>
  /**
   * Choose, which License to update.
   */
  where: Prisma.LicenseWhereUniqueInput
}

/**
 * License updateMany
 */
export type LicenseUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Licenses.
   */
  data: Prisma.XOR<Prisma.LicenseUpdateManyMutationInput, Prisma.LicenseUncheckedUpdateManyInput>
  /**
   * Filter which Licenses to update
   */
  where?: Prisma.LicenseWhereInput
  /**
   * Limit how many Licenses to update.
   */
  limit?: number
}

/**
 * License updateManyAndReturn
 */
export type LicenseUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the License
   */
  select?: Prisma.LicenseSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the License
   */
  omit?: Prisma.LicenseOmit<ExtArgs> | null
  /**
   * The data used to update Licenses.
   */
  data: Prisma.XOR<Prisma.LicenseUpdateManyMutationInput, Prisma.LicenseUncheckedUpdateManyInput>
  /**
   * Filter which Licenses to update
   */
  where?: Prisma.LicenseWhereInput
  /**
   * Limit how many Licenses to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LicenseIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * License upsert
 */
export type LicenseUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the License
   */
  select?: Prisma.LicenseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the License
   */
  omit?: Prisma.LicenseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LicenseInclude<ExtArgs> | null
  /**
   * The filter to search for the License to update in case it exists.
   */
  where: Prisma.LicenseWhereUniqueInput
  /**
   * In case the License found by the `where` argument doesn't exist, create a new License with this data.
   */
  create: Prisma.XOR<Prisma.LicenseCreateInput, Prisma.LicenseUncheckedCreateInput>
  /**
   * In case the License was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.LicenseUpdateInput, Prisma.LicenseUncheckedUpdateInput>
}

/**
 * License delete
 */
export type LicenseDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the License
   */
  select?: Prisma.LicenseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the License
   */
  omit?: Prisma.LicenseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LicenseInclude<ExtArgs> | null
  /**
   * Filter which License to delete.
   */
  where: Prisma.LicenseWhereUniqueInput
}

/**
 * License deleteMany
 */
export type LicenseDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Licenses to delete
   */
  where?: Prisma.LicenseWhereInput
  /**
   * Limit how many Licenses to delete.
   */
  limit?: number
}

/**
 * License.createdByUser
 */
export type License$createdByUserArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  where?: Prisma.UserWhereInput
}

/**
 * License.paymentIntent
 */
export type License$paymentIntentArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntent
   */
  select?: Prisma.PaymentIntentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PaymentIntent
   */
  omit?: Prisma.PaymentIntentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PaymentIntentInclude<ExtArgs> | null
  where?: Prisma.PaymentIntentWhereInput
}

/**
 * License.devices
 */
export type License$devicesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Device
   */
  select?: Prisma.DeviceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Device
   */
  omit?: Prisma.DeviceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceInclude<ExtArgs> | null
  where?: Prisma.DeviceWhereInput
  orderBy?: Prisma.DeviceOrderByWithRelationInput | Prisma.DeviceOrderByWithRelationInput[]
  cursor?: Prisma.DeviceWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.DeviceScalarFieldEnum | Prisma.DeviceScalarFieldEnum[]
}

/**
 * License.deviceExpansions
 */
export type License$deviceExpansionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the DeviceExpansion
   */
  select?: Prisma.DeviceExpansionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the DeviceExpansion
   */
  omit?: Prisma.DeviceExpansionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.DeviceExpansionInclude<ExtArgs> | null
  where?: Prisma.DeviceExpansionWhereInput
  orderBy?: Prisma.DeviceExpansionOrderByWithRelationInput | Prisma.DeviceExpansionOrderByWithRelationInput[]
  cursor?: Prisma.DeviceExpansionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.DeviceExpansionScalarFieldEnum | Prisma.DeviceExpansionScalarFieldEnum[]
}

/**
 * License.refundRequests
 */
export type License$refundRequestsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RefundRequest
   */
  select?: Prisma.RefundRequestSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RefundRequest
   */
  omit?: Prisma.RefundRequestOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.RefundRequestInclude<ExtArgs> | null
  where?: Prisma.RefundRequestWhereInput
  orderBy?: Prisma.RefundRequestOrderByWithRelationInput | Prisma.RefundRequestOrderByWithRelationInput[]
  cursor?: Prisma.RefundRequestWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.RefundRequestScalarFieldEnum | Prisma.RefundRequestScalarFieldEnum[]
}

/**
 * License without action
 */
export type LicenseDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the License
   */
  select?: Prisma.LicenseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the License
   */
  omit?: Prisma.LicenseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LicenseInclude<ExtArgs> | null
}
