
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `WebhookEvent` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import type * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model WebhookEvent
 * 
 */
export type WebhookEventModel = runtime.Types.Result.DefaultSelection<Prisma.$WebhookEventPayload>

export type AggregateWebhookEvent = {
  _count: WebhookEventCountAggregateOutputType | null
  _avg: WebhookEventAvgAggregateOutputType | null
  _sum: WebhookEventSumAggregateOutputType | null
  _min: WebhookEventMinAggregateOutputType | null
  _max: WebhookEventMaxAggregateOutputType | null
}

export type WebhookEventAvgAggregateOutputType = {
  retryCount: number | null
}

export type WebhookEventSumAggregateOutputType = {
  retryCount: number | null
}

export type WebhookEventMinAggregateOutputType = {
  id: string | null
  stripeEventId: string | null
  eventType: string | null
  processed: boolean | null
  processedAt: Date | null
  errorMessage: string | null
  retryCount: number | null
  createdAt: Date | null
  paymentIntentId: string | null
}

export type WebhookEventMaxAggregateOutputType = {
  id: string | null
  stripeEventId: string | null
  eventType: string | null
  processed: boolean | null
  processedAt: Date | null
  errorMessage: string | null
  retryCount: number | null
  createdAt: Date | null
  paymentIntentId: string | null
}

export type WebhookEventCountAggregateOutputType = {
  id: number
  stripeEventId: number
  eventType: number
  processed: number
  processedAt: number
  errorMessage: number
  retryCount: number
  createdAt: number
  paymentIntentId: number
  _all: number
}


export type WebhookEventAvgAggregateInputType = {
  retryCount?: true
}

export type WebhookEventSumAggregateInputType = {
  retryCount?: true
}

export type WebhookEventMinAggregateInputType = {
  id?: true
  stripeEventId?: true
  eventType?: true
  processed?: true
  processedAt?: true
  errorMessage?: true
  retryCount?: true
  createdAt?: true
  paymentIntentId?: true
}

export type WebhookEventMaxAggregateInputType = {
  id?: true
  stripeEventId?: true
  eventType?: true
  processed?: true
  processedAt?: true
  errorMessage?: true
  retryCount?: true
  createdAt?: true
  paymentIntentId?: true
}

export type WebhookEventCountAggregateInputType = {
  id?: true
  stripeEventId?: true
  eventType?: true
  processed?: true
  processedAt?: true
  errorMessage?: true
  retryCount?: true
  createdAt?: true
  paymentIntentId?: true
  _all?: true
}

export type WebhookEventAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which WebhookEvent to aggregate.
   */
  where?: Prisma.WebhookEventWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of WebhookEvents to fetch.
   */
  orderBy?: Prisma.WebhookEventOrderByWithRelationInput | Prisma.WebhookEventOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.WebhookEventWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` WebhookEvents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` WebhookEvents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned WebhookEvents
  **/
  _count?: true | WebhookEventCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: WebhookEventAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: WebhookEventSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: WebhookEventMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: WebhookEventMaxAggregateInputType
}

export type GetWebhookEventAggregateType<T extends WebhookEventAggregateArgs> = {
      [P in keyof T & keyof AggregateWebhookEvent]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateWebhookEvent[P]>
    : Prisma.GetScalarType<T[P], AggregateWebhookEvent[P]>
}




export type WebhookEventGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.WebhookEventWhereInput
  orderBy?: Prisma.WebhookEventOrderByWithAggregationInput | Prisma.WebhookEventOrderByWithAggregationInput[]
  by: Prisma.WebhookEventScalarFieldEnum[] | Prisma.WebhookEventScalarFieldEnum
  having?: Prisma.WebhookEventScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: WebhookEventCountAggregateInputType | true
  _avg?: WebhookEventAvgAggregateInputType
  _sum?: WebhookEventSumAggregateInputType
  _min?: WebhookEventMinAggregateInputType
  _max?: WebhookEventMaxAggregateInputType
}

export type WebhookEventGroupByOutputType = {
  id: string
  stripeEventId: string
  eventType: string
  processed: boolean
  processedAt: Date | null
  errorMessage: string | null
  retryCount: number
  createdAt: Date
  paymentIntentId: string | null
  _count: WebhookEventCountAggregateOutputType | null
  _avg: WebhookEventAvgAggregateOutputType | null
  _sum: WebhookEventSumAggregateOutputType | null
  _min: WebhookEventMinAggregateOutputType | null
  _max: WebhookEventMaxAggregateOutputType | null
}

type GetWebhookEventGroupByPayload<T extends WebhookEventGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<WebhookEventGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof WebhookEventGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], WebhookEventGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], WebhookEventGroupByOutputType[P]>
      }
    >
  >



export type WebhookEventWhereInput = {
  AND?: Prisma.WebhookEventWhereInput | Prisma.WebhookEventWhereInput[]
  OR?: Prisma.WebhookEventWhereInput[]
  NOT?: Prisma.WebhookEventWhereInput | Prisma.WebhookEventWhereInput[]
  id?: Prisma.StringFilter<"WebhookEvent"> | string
  stripeEventId?: Prisma.StringFilter<"WebhookEvent"> | string
  eventType?: Prisma.StringFilter<"WebhookEvent"> | string
  processed?: Prisma.BoolFilter<"WebhookEvent"> | boolean
  processedAt?: Prisma.DateTimeNullableFilter<"WebhookEvent"> | Date | string | null
  errorMessage?: Prisma.StringNullableFilter<"WebhookEvent"> | string | null
  retryCount?: Prisma.IntFilter<"WebhookEvent"> | number
  createdAt?: Prisma.DateTimeFilter<"WebhookEvent"> | Date | string
  paymentIntentId?: Prisma.StringNullableFilter<"WebhookEvent"> | string | null
  paymentIntent?: Prisma.XOR<Prisma.PaymentIntentNullableScalarRelationFilter, Prisma.PaymentIntentWhereInput> | null
}

export type WebhookEventOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  stripeEventId?: Prisma.SortOrder
  eventType?: Prisma.SortOrder
  processed?: Prisma.SortOrder
  processedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  errorMessage?: Prisma.SortOrderInput | Prisma.SortOrder
  retryCount?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  paymentIntentId?: Prisma.SortOrderInput | Prisma.SortOrder
  paymentIntent?: Prisma.PaymentIntentOrderByWithRelationInput
}

export type WebhookEventWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  stripeEventId?: string
  AND?: Prisma.WebhookEventWhereInput | Prisma.WebhookEventWhereInput[]
  OR?: Prisma.WebhookEventWhereInput[]
  NOT?: Prisma.WebhookEventWhereInput | Prisma.WebhookEventWhereInput[]
  eventType?: Prisma.StringFilter<"WebhookEvent"> | string
  processed?: Prisma.BoolFilter<"WebhookEvent"> | boolean
  processedAt?: Prisma.DateTimeNullableFilter<"WebhookEvent"> | Date | string | null
  errorMessage?: Prisma.StringNullableFilter<"WebhookEvent"> | string | null
  retryCount?: Prisma.IntFilter<"WebhookEvent"> | number
  createdAt?: Prisma.DateTimeFilter<"WebhookEvent"> | Date | string
  paymentIntentId?: Prisma.StringNullableFilter<"WebhookEvent"> | string | null
  paymentIntent?: Prisma.XOR<Prisma.PaymentIntentNullableScalarRelationFilter, Prisma.PaymentIntentWhereInput> | null
}, "id" | "stripeEventId">

export type WebhookEventOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  stripeEventId?: Prisma.SortOrder
  eventType?: Prisma.SortOrder
  processed?: Prisma.SortOrder
  processedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  errorMessage?: Prisma.SortOrderInput | Prisma.SortOrder
  retryCount?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  paymentIntentId?: Prisma.SortOrderInput | Prisma.SortOrder
  _count?: Prisma.WebhookEventCountOrderByAggregateInput
  _avg?: Prisma.WebhookEventAvgOrderByAggregateInput
  _max?: Prisma.WebhookEventMaxOrderByAggregateInput
  _min?: Prisma.WebhookEventMinOrderByAggregateInput
  _sum?: Prisma.WebhookEventSumOrderByAggregateInput
}

export type WebhookEventScalarWhereWithAggregatesInput = {
  AND?: Prisma.WebhookEventScalarWhereWithAggregatesInput | Prisma.WebhookEventScalarWhereWithAggregatesInput[]
  OR?: Prisma.WebhookEventScalarWhereWithAggregatesInput[]
  NOT?: Prisma.WebhookEventScalarWhereWithAggregatesInput | Prisma.WebhookEventScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"WebhookEvent"> | string
  stripeEventId?: Prisma.StringWithAggregatesFilter<"WebhookEvent"> | string
  eventType?: Prisma.StringWithAggregatesFilter<"WebhookEvent"> | string
  processed?: Prisma.BoolWithAggregatesFilter<"WebhookEvent"> | boolean
  processedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"WebhookEvent"> | Date | string | null
  errorMessage?: Prisma.StringNullableWithAggregatesFilter<"WebhookEvent"> | string | null
  retryCount?: Prisma.IntWithAggregatesFilter<"WebhookEvent"> | number
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"WebhookEvent"> | Date | string
  paymentIntentId?: Prisma.StringNullableWithAggregatesFilter<"WebhookEvent"> | string | null
}

export type WebhookEventCreateInput = {
  id?: string
  stripeEventId: string
  eventType: string
  processed?: boolean
  processedAt?: Date | string | null
  errorMessage?: string | null
  retryCount?: number
  createdAt?: Date | string
  paymentIntent?: Prisma.PaymentIntentCreateNestedOneWithoutWebhookEventsInput
}

export type WebhookEventUncheckedCreateInput = {
  id?: string
  stripeEventId: string
  eventType: string
  processed?: boolean
  processedAt?: Date | string | null
  errorMessage?: string | null
  retryCount?: number
  createdAt?: Date | string
  paymentIntentId?: string | null
}

export type WebhookEventUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripeEventId?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.StringFieldUpdateOperationsInput | string
  processed?: Prisma.BoolFieldUpdateOperationsInput | boolean
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  errorMessage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  retryCount?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  paymentIntent?: Prisma.PaymentIntentUpdateOneWithoutWebhookEventsNestedInput
}

export type WebhookEventUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripeEventId?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.StringFieldUpdateOperationsInput | string
  processed?: Prisma.BoolFieldUpdateOperationsInput | boolean
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  errorMessage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  retryCount?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  paymentIntentId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type WebhookEventCreateManyInput = {
  id?: string
  stripeEventId: string
  eventType: string
  processed?: boolean
  processedAt?: Date | string | null
  errorMessage?: string | null
  retryCount?: number
  createdAt?: Date | string
  paymentIntentId?: string | null
}

export type WebhookEventUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripeEventId?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.StringFieldUpdateOperationsInput | string
  processed?: Prisma.BoolFieldUpdateOperationsInput | boolean
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  errorMessage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  retryCount?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type WebhookEventUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripeEventId?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.StringFieldUpdateOperationsInput | string
  processed?: Prisma.BoolFieldUpdateOperationsInput | boolean
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  errorMessage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  retryCount?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  paymentIntentId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type WebhookEventListRelationFilter = {
  every?: Prisma.WebhookEventWhereInput
  some?: Prisma.WebhookEventWhereInput
  none?: Prisma.WebhookEventWhereInput
}

export type WebhookEventOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type WebhookEventCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  stripeEventId?: Prisma.SortOrder
  eventType?: Prisma.SortOrder
  processed?: Prisma.SortOrder
  processedAt?: Prisma.SortOrder
  errorMessage?: Prisma.SortOrder
  retryCount?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  paymentIntentId?: Prisma.SortOrder
}

export type WebhookEventAvgOrderByAggregateInput = {
  retryCount?: Prisma.SortOrder
}

export type WebhookEventMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  stripeEventId?: Prisma.SortOrder
  eventType?: Prisma.SortOrder
  processed?: Prisma.SortOrder
  processedAt?: Prisma.SortOrder
  errorMessage?: Prisma.SortOrder
  retryCount?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  paymentIntentId?: Prisma.SortOrder
}

export type WebhookEventMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  stripeEventId?: Prisma.SortOrder
  eventType?: Prisma.SortOrder
  processed?: Prisma.SortOrder
  processedAt?: Prisma.SortOrder
  errorMessage?: Prisma.SortOrder
  retryCount?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  paymentIntentId?: Prisma.SortOrder
}

export type WebhookEventSumOrderByAggregateInput = {
  retryCount?: Prisma.SortOrder
}

export type WebhookEventCreateNestedManyWithoutPaymentIntentInput = {
  create?: Prisma.XOR<Prisma.WebhookEventCreateWithoutPaymentIntentInput, Prisma.WebhookEventUncheckedCreateWithoutPaymentIntentInput> | Prisma.WebhookEventCreateWithoutPaymentIntentInput[] | Prisma.WebhookEventUncheckedCreateWithoutPaymentIntentInput[]
  connectOrCreate?: Prisma.WebhookEventCreateOrConnectWithoutPaymentIntentInput | Prisma.WebhookEventCreateOrConnectWithoutPaymentIntentInput[]
  createMany?: Prisma.WebhookEventCreateManyPaymentIntentInputEnvelope
  connect?: Prisma.WebhookEventWhereUniqueInput | Prisma.WebhookEventWhereUniqueInput[]
}

export type WebhookEventUncheckedCreateNestedManyWithoutPaymentIntentInput = {
  create?: Prisma.XOR<Prisma.WebhookEventCreateWithoutPaymentIntentInput, Prisma.WebhookEventUncheckedCreateWithoutPaymentIntentInput> | Prisma.WebhookEventCreateWithoutPaymentIntentInput[] | Prisma.WebhookEventUncheckedCreateWithoutPaymentIntentInput[]
  connectOrCreate?: Prisma.WebhookEventCreateOrConnectWithoutPaymentIntentInput | Prisma.WebhookEventCreateOrConnectWithoutPaymentIntentInput[]
  createMany?: Prisma.WebhookEventCreateManyPaymentIntentInputEnvelope
  connect?: Prisma.WebhookEventWhereUniqueInput | Prisma.WebhookEventWhereUniqueInput[]
}

export type WebhookEventUpdateManyWithoutPaymentIntentNestedInput = {
  create?: Prisma.XOR<Prisma.WebhookEventCreateWithoutPaymentIntentInput, Prisma.WebhookEventUncheckedCreateWithoutPaymentIntentInput> | Prisma.WebhookEventCreateWithoutPaymentIntentInput[] | Prisma.WebhookEventUncheckedCreateWithoutPaymentIntentInput[]
  connectOrCreate?: Prisma.WebhookEventCreateOrConnectWithoutPaymentIntentInput | Prisma.WebhookEventCreateOrConnectWithoutPaymentIntentInput[]
  upsert?: Prisma.WebhookEventUpsertWithWhereUniqueWithoutPaymentIntentInput | Prisma.WebhookEventUpsertWithWhereUniqueWithoutPaymentIntentInput[]
  createMany?: Prisma.WebhookEventCreateManyPaymentIntentInputEnvelope
  set?: Prisma.WebhookEventWhereUniqueInput | Prisma.WebhookEventWhereUniqueInput[]
  disconnect?: Prisma.WebhookEventWhereUniqueInput | Prisma.WebhookEventWhereUniqueInput[]
  delete?: Prisma.WebhookEventWhereUniqueInput | Prisma.WebhookEventWhereUniqueInput[]
  connect?: Prisma.WebhookEventWhereUniqueInput | Prisma.WebhookEventWhereUniqueInput[]
  update?: Prisma.WebhookEventUpdateWithWhereUniqueWithoutPaymentIntentInput | Prisma.WebhookEventUpdateWithWhereUniqueWithoutPaymentIntentInput[]
  updateMany?: Prisma.WebhookEventUpdateManyWithWhereWithoutPaymentIntentInput | Prisma.WebhookEventUpdateManyWithWhereWithoutPaymentIntentInput[]
  deleteMany?: Prisma.WebhookEventScalarWhereInput | Prisma.WebhookEventScalarWhereInput[]
}

export type WebhookEventUncheckedUpdateManyWithoutPaymentIntentNestedInput = {
  create?: Prisma.XOR<Prisma.WebhookEventCreateWithoutPaymentIntentInput, Prisma.WebhookEventUncheckedCreateWithoutPaymentIntentInput> | Prisma.WebhookEventCreateWithoutPaymentIntentInput[] | Prisma.WebhookEventUncheckedCreateWithoutPaymentIntentInput[]
  connectOrCreate?: Prisma.WebhookEventCreateOrConnectWithoutPaymentIntentInput | Prisma.WebhookEventCreateOrConnectWithoutPaymentIntentInput[]
  upsert?: Prisma.WebhookEventUpsertWithWhereUniqueWithoutPaymentIntentInput | Prisma.WebhookEventUpsertWithWhereUniqueWithoutPaymentIntentInput[]
  createMany?: Prisma.WebhookEventCreateManyPaymentIntentInputEnvelope
  set?: Prisma.WebhookEventWhereUniqueInput | Prisma.WebhookEventWhereUniqueInput[]
  disconnect?: Prisma.WebhookEventWhereUniqueInput | Prisma.WebhookEventWhereUniqueInput[]
  delete?: Prisma.WebhookEventWhereUniqueInput | Prisma.WebhookEventWhereUniqueInput[]
  connect?: Prisma.WebhookEventWhereUniqueInput | Prisma.WebhookEventWhereUniqueInput[]
  update?: Prisma.WebhookEventUpdateWithWhereUniqueWithoutPaymentIntentInput | Prisma.WebhookEventUpdateWithWhereUniqueWithoutPaymentIntentInput[]
  updateMany?: Prisma.WebhookEventUpdateManyWithWhereWithoutPaymentIntentInput | Prisma.WebhookEventUpdateManyWithWhereWithoutPaymentIntentInput[]
  deleteMany?: Prisma.WebhookEventScalarWhereInput | Prisma.WebhookEventScalarWhereInput[]
}

export type WebhookEventCreateWithoutPaymentIntentInput = {
  id?: string
  stripeEventId: string
  eventType: string
  processed?: boolean
  processedAt?: Date | string | null
  errorMessage?: string | null
  retryCount?: number
  createdAt?: Date | string
}

export type WebhookEventUncheckedCreateWithoutPaymentIntentInput = {
  id?: string
  stripeEventId: string
  eventType: string
  processed?: boolean
  processedAt?: Date | string | null
  errorMessage?: string | null
  retryCount?: number
  createdAt?: Date | string
}

export type WebhookEventCreateOrConnectWithoutPaymentIntentInput = {
  where: Prisma.WebhookEventWhereUniqueInput
  create: Prisma.XOR<Prisma.WebhookEventCreateWithoutPaymentIntentInput, Prisma.WebhookEventUncheckedCreateWithoutPaymentIntentInput>
}

export type WebhookEventCreateManyPaymentIntentInputEnvelope = {
  data: Prisma.WebhookEventCreateManyPaymentIntentInput | Prisma.WebhookEventCreateManyPaymentIntentInput[]
  skipDuplicates?: boolean
}

export type WebhookEventUpsertWithWhereUniqueWithoutPaymentIntentInput = {
  where: Prisma.WebhookEventWhereUniqueInput
  update: Prisma.XOR<Prisma.WebhookEventUpdateWithoutPaymentIntentInput, Prisma.WebhookEventUncheckedUpdateWithoutPaymentIntentInput>
  create: Prisma.XOR<Prisma.WebhookEventCreateWithoutPaymentIntentInput, Prisma.WebhookEventUncheckedCreateWithoutPaymentIntentInput>
}

export type WebhookEventUpdateWithWhereUniqueWithoutPaymentIntentInput = {
  where: Prisma.WebhookEventWhereUniqueInput
  data: Prisma.XOR<Prisma.WebhookEventUpdateWithoutPaymentIntentInput, Prisma.WebhookEventUncheckedUpdateWithoutPaymentIntentInput>
}

export type WebhookEventUpdateManyWithWhereWithoutPaymentIntentInput = {
  where: Prisma.WebhookEventScalarWhereInput
  data: Prisma.XOR<Prisma.WebhookEventUpdateManyMutationInput, Prisma.WebhookEventUncheckedUpdateManyWithoutPaymentIntentInput>
}

export type WebhookEventScalarWhereInput = {
  AND?: Prisma.WebhookEventScalarWhereInput | Prisma.WebhookEventScalarWhereInput[]
  OR?: Prisma.WebhookEventScalarWhereInput[]
  NOT?: Prisma.WebhookEventScalarWhereInput | Prisma.WebhookEventScalarWhereInput[]
  id?: Prisma.StringFilter<"WebhookEvent"> | string
  stripeEventId?: Prisma.StringFilter<"WebhookEvent"> | string
  eventType?: Prisma.StringFilter<"WebhookEvent"> | string
  processed?: Prisma.BoolFilter<"WebhookEvent"> | boolean
  processedAt?: Prisma.DateTimeNullableFilter<"WebhookEvent"> | Date | string | null
  errorMessage?: Prisma.StringNullableFilter<"WebhookEvent"> | string | null
  retryCount?: Prisma.IntFilter<"WebhookEvent"> | number
  createdAt?: Prisma.DateTimeFilter<"WebhookEvent"> | Date | string
  paymentIntentId?: Prisma.StringNullableFilter<"WebhookEvent"> | string | null
}

export type WebhookEventCreateManyPaymentIntentInput = {
  id?: string
  stripeEventId: string
  eventType: string
  processed?: boolean
  processedAt?: Date | string | null
  errorMessage?: string | null
  retryCount?: number
  createdAt?: Date | string
}

export type WebhookEventUpdateWithoutPaymentIntentInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripeEventId?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.StringFieldUpdateOperationsInput | string
  processed?: Prisma.BoolFieldUpdateOperationsInput | boolean
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  errorMessage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  retryCount?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type WebhookEventUncheckedUpdateWithoutPaymentIntentInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripeEventId?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.StringFieldUpdateOperationsInput | string
  processed?: Prisma.BoolFieldUpdateOperationsInput | boolean
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  errorMessage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  retryCount?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type WebhookEventUncheckedUpdateManyWithoutPaymentIntentInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  stripeEventId?: Prisma.StringFieldUpdateOperationsInput | string
  eventType?: Prisma.StringFieldUpdateOperationsInput | string
  processed?: Prisma.BoolFieldUpdateOperationsInput | boolean
  processedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  errorMessage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  retryCount?: Prisma.IntFieldUpdateOperationsInput | number
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type WebhookEventSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  stripeEventId?: boolean
  eventType?: boolean
  processed?: boolean
  processedAt?: boolean
  errorMessage?: boolean
  retryCount?: boolean
  createdAt?: boolean
  paymentIntentId?: boolean
  paymentIntent?: boolean | Prisma.WebhookEvent$paymentIntentArgs<ExtArgs>
}, ExtArgs["result"]["webhookEvent"]>

export type WebhookEventSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  stripeEventId?: boolean
  eventType?: boolean
  processed?: boolean
  processedAt?: boolean
  errorMessage?: boolean
  retryCount?: boolean
  createdAt?: boolean
  paymentIntentId?: boolean
  paymentIntent?: boolean | Prisma.WebhookEvent$paymentIntentArgs<ExtArgs>
}, ExtArgs["result"]["webhookEvent"]>

export type WebhookEventSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  stripeEventId?: boolean
  eventType?: boolean
  processed?: boolean
  processedAt?: boolean
  errorMessage?: boolean
  retryCount?: boolean
  createdAt?: boolean
  paymentIntentId?: boolean
  paymentIntent?: boolean | Prisma.WebhookEvent$paymentIntentArgs<ExtArgs>
}, ExtArgs["result"]["webhookEvent"]>

export type WebhookEventSelectScalar = {
  id?: boolean
  stripeEventId?: boolean
  eventType?: boolean
  processed?: boolean
  processedAt?: boolean
  errorMessage?: boolean
  retryCount?: boolean
  createdAt?: boolean
  paymentIntentId?: boolean
}

export type WebhookEventOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "stripeEventId" | "eventType" | "processed" | "processedAt" | "errorMessage" | "retryCount" | "createdAt" | "paymentIntentId", ExtArgs["result"]["webhookEvent"]>
export type WebhookEventInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  paymentIntent?: boolean | Prisma.WebhookEvent$paymentIntentArgs<ExtArgs>
}
export type WebhookEventIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  paymentIntent?: boolean | Prisma.WebhookEvent$paymentIntentArgs<ExtArgs>
}
export type WebhookEventIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  paymentIntent?: boolean | Prisma.WebhookEvent$paymentIntentArgs<ExtArgs>
}

export type $WebhookEventPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "WebhookEvent"
  objects: {
    paymentIntent: Prisma.$PaymentIntentPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    stripeEventId: string
    eventType: string
    processed: boolean
    processedAt: Date | null
    errorMessage: string | null
    retryCount: number
    createdAt: Date
    paymentIntentId: string | null
  }, ExtArgs["result"]["webhookEvent"]>
  composites: {}
}

export type WebhookEventGetPayload<S extends boolean | null | undefined | WebhookEventDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$WebhookEventPayload, S>

export type WebhookEventCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<WebhookEventFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: WebhookEventCountAggregateInputType | true
  }

export interface WebhookEventDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['WebhookEvent'], meta: { name: 'WebhookEvent' } }
  /**
   * Find zero or one WebhookEvent that matches the filter.
   * @param {WebhookEventFindUniqueArgs} args - Arguments to find a WebhookEvent
   * @example
   * // Get one WebhookEvent
   * const webhookEvent = await prisma.webhookEvent.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends WebhookEventFindUniqueArgs>(args: Prisma.SelectSubset<T, WebhookEventFindUniqueArgs<ExtArgs>>): Prisma.Prisma__WebhookEventClient<runtime.Types.Result.GetResult<Prisma.$WebhookEventPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one WebhookEvent that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {WebhookEventFindUniqueOrThrowArgs} args - Arguments to find a WebhookEvent
   * @example
   * // Get one WebhookEvent
   * const webhookEvent = await prisma.webhookEvent.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends WebhookEventFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, WebhookEventFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__WebhookEventClient<runtime.Types.Result.GetResult<Prisma.$WebhookEventPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first WebhookEvent that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {WebhookEventFindFirstArgs} args - Arguments to find a WebhookEvent
   * @example
   * // Get one WebhookEvent
   * const webhookEvent = await prisma.webhookEvent.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends WebhookEventFindFirstArgs>(args?: Prisma.SelectSubset<T, WebhookEventFindFirstArgs<ExtArgs>>): Prisma.Prisma__WebhookEventClient<runtime.Types.Result.GetResult<Prisma.$WebhookEventPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first WebhookEvent that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {WebhookEventFindFirstOrThrowArgs} args - Arguments to find a WebhookEvent
   * @example
   * // Get one WebhookEvent
   * const webhookEvent = await prisma.webhookEvent.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends WebhookEventFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, WebhookEventFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__WebhookEventClient<runtime.Types.Result.GetResult<Prisma.$WebhookEventPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more WebhookEvents that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {WebhookEventFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all WebhookEvents
   * const webhookEvents = await prisma.webhookEvent.findMany()
   * 
   * // Get first 10 WebhookEvents
   * const webhookEvents = await prisma.webhookEvent.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const webhookEventWithIdOnly = await prisma.webhookEvent.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends WebhookEventFindManyArgs>(args?: Prisma.SelectSubset<T, WebhookEventFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$WebhookEventPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a WebhookEvent.
   * @param {WebhookEventCreateArgs} args - Arguments to create a WebhookEvent.
   * @example
   * // Create one WebhookEvent
   * const WebhookEvent = await prisma.webhookEvent.create({
   *   data: {
   *     // ... data to create a WebhookEvent
   *   }
   * })
   * 
   */
  create<T extends WebhookEventCreateArgs>(args: Prisma.SelectSubset<T, WebhookEventCreateArgs<ExtArgs>>): Prisma.Prisma__WebhookEventClient<runtime.Types.Result.GetResult<Prisma.$WebhookEventPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many WebhookEvents.
   * @param {WebhookEventCreateManyArgs} args - Arguments to create many WebhookEvents.
   * @example
   * // Create many WebhookEvents
   * const webhookEvent = await prisma.webhookEvent.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends WebhookEventCreateManyArgs>(args?: Prisma.SelectSubset<T, WebhookEventCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many WebhookEvents and returns the data saved in the database.
   * @param {WebhookEventCreateManyAndReturnArgs} args - Arguments to create many WebhookEvents.
   * @example
   * // Create many WebhookEvents
   * const webhookEvent = await prisma.webhookEvent.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many WebhookEvents and only return the `id`
   * const webhookEventWithIdOnly = await prisma.webhookEvent.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends WebhookEventCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, WebhookEventCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$WebhookEventPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a WebhookEvent.
   * @param {WebhookEventDeleteArgs} args - Arguments to delete one WebhookEvent.
   * @example
   * // Delete one WebhookEvent
   * const WebhookEvent = await prisma.webhookEvent.delete({
   *   where: {
   *     // ... filter to delete one WebhookEvent
   *   }
   * })
   * 
   */
  delete<T extends WebhookEventDeleteArgs>(args: Prisma.SelectSubset<T, WebhookEventDeleteArgs<ExtArgs>>): Prisma.Prisma__WebhookEventClient<runtime.Types.Result.GetResult<Prisma.$WebhookEventPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one WebhookEvent.
   * @param {WebhookEventUpdateArgs} args - Arguments to update one WebhookEvent.
   * @example
   * // Update one WebhookEvent
   * const webhookEvent = await prisma.webhookEvent.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends WebhookEventUpdateArgs>(args: Prisma.SelectSubset<T, WebhookEventUpdateArgs<ExtArgs>>): Prisma.Prisma__WebhookEventClient<runtime.Types.Result.GetResult<Prisma.$WebhookEventPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more WebhookEvents.
   * @param {WebhookEventDeleteManyArgs} args - Arguments to filter WebhookEvents to delete.
   * @example
   * // Delete a few WebhookEvents
   * const { count } = await prisma.webhookEvent.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends WebhookEventDeleteManyArgs>(args?: Prisma.SelectSubset<T, WebhookEventDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more WebhookEvents.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {WebhookEventUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many WebhookEvents
   * const webhookEvent = await prisma.webhookEvent.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends WebhookEventUpdateManyArgs>(args: Prisma.SelectSubset<T, WebhookEventUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more WebhookEvents and returns the data updated in the database.
   * @param {WebhookEventUpdateManyAndReturnArgs} args - Arguments to update many WebhookEvents.
   * @example
   * // Update many WebhookEvents
   * const webhookEvent = await prisma.webhookEvent.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more WebhookEvents and only return the `id`
   * const webhookEventWithIdOnly = await prisma.webhookEvent.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends WebhookEventUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, WebhookEventUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$WebhookEventPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one WebhookEvent.
   * @param {WebhookEventUpsertArgs} args - Arguments to update or create a WebhookEvent.
   * @example
   * // Update or create a WebhookEvent
   * const webhookEvent = await prisma.webhookEvent.upsert({
   *   create: {
   *     // ... data to create a WebhookEvent
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the WebhookEvent we want to update
   *   }
   * })
   */
  upsert<T extends WebhookEventUpsertArgs>(args: Prisma.SelectSubset<T, WebhookEventUpsertArgs<ExtArgs>>): Prisma.Prisma__WebhookEventClient<runtime.Types.Result.GetResult<Prisma.$WebhookEventPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of WebhookEvents.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {WebhookEventCountArgs} args - Arguments to filter WebhookEvents to count.
   * @example
   * // Count the number of WebhookEvents
   * const count = await prisma.webhookEvent.count({
   *   where: {
   *     // ... the filter for the WebhookEvents we want to count
   *   }
   * })
  **/
  count<T extends WebhookEventCountArgs>(
    args?: Prisma.Subset<T, WebhookEventCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], WebhookEventCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a WebhookEvent.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {WebhookEventAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends WebhookEventAggregateArgs>(args: Prisma.Subset<T, WebhookEventAggregateArgs>): Prisma.PrismaPromise<GetWebhookEventAggregateType<T>>

  /**
   * Group by WebhookEvent.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {WebhookEventGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends WebhookEventGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: WebhookEventGroupByArgs['orderBy'] }
      : { orderBy?: WebhookEventGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, WebhookEventGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetWebhookEventGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the WebhookEvent model
 */
readonly fields: WebhookEventFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for WebhookEvent.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__WebhookEventClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  paymentIntent<T extends Prisma.WebhookEvent$paymentIntentArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.WebhookEvent$paymentIntentArgs<ExtArgs>>): Prisma.Prisma__PaymentIntentClient<runtime.Types.Result.GetResult<Prisma.$PaymentIntentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the WebhookEvent model
 */
export interface WebhookEventFieldRefs {
  readonly id: Prisma.FieldRef<"WebhookEvent", 'String'>
  readonly stripeEventId: Prisma.FieldRef<"WebhookEvent", 'String'>
  readonly eventType: Prisma.FieldRef<"WebhookEvent", 'String'>
  readonly processed: Prisma.FieldRef<"WebhookEvent", 'Boolean'>
  readonly processedAt: Prisma.FieldRef<"WebhookEvent", 'DateTime'>
  readonly errorMessage: Prisma.FieldRef<"WebhookEvent", 'String'>
  readonly retryCount: Prisma.FieldRef<"WebhookEvent", 'Int'>
  readonly createdAt: Prisma.FieldRef<"WebhookEvent", 'DateTime'>
  readonly paymentIntentId: Prisma.FieldRef<"WebhookEvent", 'String'>
}
    

// Custom InputTypes
/**
 * WebhookEvent findUnique
 */
export type WebhookEventFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the WebhookEvent
   */
  select?: Prisma.WebhookEventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the WebhookEvent
   */
  omit?: Prisma.WebhookEventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.WebhookEventInclude<ExtArgs> | null
  /**
   * Filter, which WebhookEvent to fetch.
   */
  where: Prisma.WebhookEventWhereUniqueInput
}

/**
 * WebhookEvent findUniqueOrThrow
 */
export type WebhookEventFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the WebhookEvent
   */
  select?: Prisma.WebhookEventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the WebhookEvent
   */
  omit?: Prisma.WebhookEventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.WebhookEventInclude<ExtArgs> | null
  /**
   * Filter, which WebhookEvent to fetch.
   */
  where: Prisma.WebhookEventWhereUniqueInput
}

/**
 * WebhookEvent findFirst
 */
export type WebhookEventFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the WebhookEvent
   */
  select?: Prisma.WebhookEventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the WebhookEvent
   */
  omit?: Prisma.WebhookEventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.WebhookEventInclude<ExtArgs> | null
  /**
   * Filter, which WebhookEvent to fetch.
   */
  where?: Prisma.WebhookEventWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of WebhookEvents to fetch.
   */
  orderBy?: Prisma.WebhookEventOrderByWithRelationInput | Prisma.WebhookEventOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for WebhookEvents.
   */
  cursor?: Prisma.WebhookEventWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` WebhookEvents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` WebhookEvents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of WebhookEvents.
   */
  distinct?: Prisma.WebhookEventScalarFieldEnum | Prisma.WebhookEventScalarFieldEnum[]
}

/**
 * WebhookEvent findFirstOrThrow
 */
export type WebhookEventFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the WebhookEvent
   */
  select?: Prisma.WebhookEventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the WebhookEvent
   */
  omit?: Prisma.WebhookEventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.WebhookEventInclude<ExtArgs> | null
  /**
   * Filter, which WebhookEvent to fetch.
   */
  where?: Prisma.WebhookEventWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of WebhookEvents to fetch.
   */
  orderBy?: Prisma.WebhookEventOrderByWithRelationInput | Prisma.WebhookEventOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for WebhookEvents.
   */
  cursor?: Prisma.WebhookEventWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` WebhookEvents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` WebhookEvents.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of WebhookEvents.
   */
  distinct?: Prisma.WebhookEventScalarFieldEnum | Prisma.WebhookEventScalarFieldEnum[]
}

/**
 * WebhookEvent findMany
 */
export type WebhookEventFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the WebhookEvent
   */
  select?: Prisma.WebhookEventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the WebhookEvent
   */
  omit?: Prisma.WebhookEventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.WebhookEventInclude<ExtArgs> | null
  /**
   * Filter, which WebhookEvents to fetch.
   */
  where?: Prisma.WebhookEventWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of WebhookEvents to fetch.
   */
  orderBy?: Prisma.WebhookEventOrderByWithRelationInput | Prisma.WebhookEventOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing WebhookEvents.
   */
  cursor?: Prisma.WebhookEventWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` WebhookEvents from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` WebhookEvents.
   */
  skip?: number
  distinct?: Prisma.WebhookEventScalarFieldEnum | Prisma.WebhookEventScalarFieldEnum[]
}

/**
 * WebhookEvent create
 */
export type WebhookEventCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the WebhookEvent
   */
  select?: Prisma.WebhookEventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the WebhookEvent
   */
  omit?: Prisma.WebhookEventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.WebhookEventInclude<ExtArgs> | null
  /**
   * The data needed to create a WebhookEvent.
   */
  data: Prisma.XOR<Prisma.WebhookEventCreateInput, Prisma.WebhookEventUncheckedCreateInput>
}

/**
 * WebhookEvent createMany
 */
export type WebhookEventCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many WebhookEvents.
   */
  data: Prisma.WebhookEventCreateManyInput | Prisma.WebhookEventCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * WebhookEvent createManyAndReturn
 */
export type WebhookEventCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the WebhookEvent
   */
  select?: Prisma.WebhookEventSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the WebhookEvent
   */
  omit?: Prisma.WebhookEventOmit<ExtArgs> | null
  /**
   * The data used to create many WebhookEvents.
   */
  data: Prisma.WebhookEventCreateManyInput | Prisma.WebhookEventCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.WebhookEventIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * WebhookEvent update
 */
export type WebhookEventUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the WebhookEvent
   */
  select?: Prisma.WebhookEventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the WebhookEvent
   */
  omit?: Prisma.WebhookEventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.WebhookEventInclude<ExtArgs> | null
  /**
   * The data needed to update a WebhookEvent.
   */
  data: Prisma.XOR<Prisma.WebhookEventUpdateInput, Prisma.WebhookEventUncheckedUpdateInput>
  /**
   * Choose, which WebhookEvent to update.
   */
  where: Prisma.WebhookEventWhereUniqueInput
}

/**
 * WebhookEvent updateMany
 */
export type WebhookEventUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update WebhookEvents.
   */
  data: Prisma.XOR<Prisma.WebhookEventUpdateManyMutationInput, Prisma.WebhookEventUncheckedUpdateManyInput>
  /**
   * Filter which WebhookEvents to update
   */
  where?: Prisma.WebhookEventWhereInput
  /**
   * Limit how many WebhookEvents to update.
   */
  limit?: number
}

/**
 * WebhookEvent updateManyAndReturn
 */
export type WebhookEventUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the WebhookEvent
   */
  select?: Prisma.WebhookEventSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the WebhookEvent
   */
  omit?: Prisma.WebhookEventOmit<ExtArgs> | null
  /**
   * The data used to update WebhookEvents.
   */
  data: Prisma.XOR<Prisma.WebhookEventUpdateManyMutationInput, Prisma.WebhookEventUncheckedUpdateManyInput>
  /**
   * Filter which WebhookEvents to update
   */
  where?: Prisma.WebhookEventWhereInput
  /**
   * Limit how many WebhookEvents to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.WebhookEventIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * WebhookEvent upsert
 */
export type WebhookEventUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the WebhookEvent
   */
  select?: Prisma.WebhookEventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the WebhookEvent
   */
  omit?: Prisma.WebhookEventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.WebhookEventInclude<ExtArgs> | null
  /**
   * The filter to search for the WebhookEvent to update in case it exists.
   */
  where: Prisma.WebhookEventWhereUniqueInput
  /**
   * In case the WebhookEvent found by the `where` argument doesn't exist, create a new WebhookEvent with this data.
   */
  create: Prisma.XOR<Prisma.WebhookEventCreateInput, Prisma.WebhookEventUncheckedCreateInput>
  /**
   * In case the WebhookEvent was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.WebhookEventUpdateInput, Prisma.WebhookEventUncheckedUpdateInput>
}

/**
 * WebhookEvent delete
 */
export type WebhookEventDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the WebhookEvent
   */
  select?: Prisma.WebhookEventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the WebhookEvent
   */
  omit?: Prisma.WebhookEventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.WebhookEventInclude<ExtArgs> | null
  /**
   * Filter which WebhookEvent to delete.
   */
  where: Prisma.WebhookEventWhereUniqueInput
}

/**
 * WebhookEvent deleteMany
 */
export type WebhookEventDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which WebhookEvents to delete
   */
  where?: Prisma.WebhookEventWhereInput
  /**
   * Limit how many WebhookEvents to delete.
   */
  limit?: number
}

/**
 * WebhookEvent.paymentIntent
 */
export type WebhookEvent$paymentIntentArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the PaymentIntent
   */
  select?: Prisma.PaymentIntentSelect<ExtArgs> | null
  /**
   * Omit specific fields from the PaymentIntent
   */
  omit?: Prisma.PaymentIntentOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PaymentIntentInclude<ExtArgs> | null
  where?: Prisma.PaymentIntentWhereInput
}

/**
 * WebhookEvent without action
 */
export type WebhookEventDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the WebhookEvent
   */
  select?: Prisma.WebhookEventSelect<ExtArgs> | null
  /**
   * Omit specific fields from the WebhookEvent
   */
  omit?: Prisma.WebhookEventOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.WebhookEventInclude<ExtArgs> | null
}
