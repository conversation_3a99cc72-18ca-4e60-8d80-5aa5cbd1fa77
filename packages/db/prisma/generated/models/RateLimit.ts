
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * This file exports the `RateLimit` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import type * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model RateLimit
 * 
 */
export type RateLimitModel = runtime.Types.Result.DefaultSelection<Prisma.$RateLimitPayload>

export type AggregateRateLimit = {
  _count: RateLimitCountAggregateOutputType | null
  _avg: RateLimitAvgAggregateOutputType | null
  _sum: RateLimitSumAggregateOutputType | null
  _min: RateLimitMinAggregateOutputType | null
  _max: RateLimitMaxAggregateOutputType | null
}

export type RateLimitAvgAggregateOutputType = {
  count: number | null
}

export type RateLimitSumAggregateOutputType = {
  count: number | null
}

export type RateLimitMinAggregateOutputType = {
  id: string | null
  identifier: string | null
  action: string | null
  count: number | null
  windowStart: Date | null
  expiresAt: Date | null
}

export type RateLimitMaxAggregateOutputType = {
  id: string | null
  identifier: string | null
  action: string | null
  count: number | null
  windowStart: Date | null
  expiresAt: Date | null
}

export type RateLimitCountAggregateOutputType = {
  id: number
  identifier: number
  action: number
  count: number
  windowStart: number
  expiresAt: number
  _all: number
}


export type RateLimitAvgAggregateInputType = {
  count?: true
}

export type RateLimitSumAggregateInputType = {
  count?: true
}

export type RateLimitMinAggregateInputType = {
  id?: true
  identifier?: true
  action?: true
  count?: true
  windowStart?: true
  expiresAt?: true
}

export type RateLimitMaxAggregateInputType = {
  id?: true
  identifier?: true
  action?: true
  count?: true
  windowStart?: true
  expiresAt?: true
}

export type RateLimitCountAggregateInputType = {
  id?: true
  identifier?: true
  action?: true
  count?: true
  windowStart?: true
  expiresAt?: true
  _all?: true
}

export type RateLimitAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which RateLimit to aggregate.
   */
  where?: Prisma.RateLimitWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RateLimits to fetch.
   */
  orderBy?: Prisma.RateLimitOrderByWithRelationInput | Prisma.RateLimitOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.RateLimitWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RateLimits from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RateLimits.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned RateLimits
  **/
  _count?: true | RateLimitCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: RateLimitAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: RateLimitSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: RateLimitMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: RateLimitMaxAggregateInputType
}

export type GetRateLimitAggregateType<T extends RateLimitAggregateArgs> = {
      [P in keyof T & keyof AggregateRateLimit]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateRateLimit[P]>
    : Prisma.GetScalarType<T[P], AggregateRateLimit[P]>
}




export type RateLimitGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.RateLimitWhereInput
  orderBy?: Prisma.RateLimitOrderByWithAggregationInput | Prisma.RateLimitOrderByWithAggregationInput[]
  by: Prisma.RateLimitScalarFieldEnum[] | Prisma.RateLimitScalarFieldEnum
  having?: Prisma.RateLimitScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: RateLimitCountAggregateInputType | true
  _avg?: RateLimitAvgAggregateInputType
  _sum?: RateLimitSumAggregateInputType
  _min?: RateLimitMinAggregateInputType
  _max?: RateLimitMaxAggregateInputType
}

export type RateLimitGroupByOutputType = {
  id: string
  identifier: string
  action: string
  count: number
  windowStart: Date
  expiresAt: Date
  _count: RateLimitCountAggregateOutputType | null
  _avg: RateLimitAvgAggregateOutputType | null
  _sum: RateLimitSumAggregateOutputType | null
  _min: RateLimitMinAggregateOutputType | null
  _max: RateLimitMaxAggregateOutputType | null
}

type GetRateLimitGroupByPayload<T extends RateLimitGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<RateLimitGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof RateLimitGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], RateLimitGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], RateLimitGroupByOutputType[P]>
      }
    >
  >



export type RateLimitWhereInput = {
  AND?: Prisma.RateLimitWhereInput | Prisma.RateLimitWhereInput[]
  OR?: Prisma.RateLimitWhereInput[]
  NOT?: Prisma.RateLimitWhereInput | Prisma.RateLimitWhereInput[]
  id?: Prisma.StringFilter<"RateLimit"> | string
  identifier?: Prisma.StringFilter<"RateLimit"> | string
  action?: Prisma.StringFilter<"RateLimit"> | string
  count?: Prisma.IntFilter<"RateLimit"> | number
  windowStart?: Prisma.DateTimeFilter<"RateLimit"> | Date | string
  expiresAt?: Prisma.DateTimeFilter<"RateLimit"> | Date | string
}

export type RateLimitOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  action?: Prisma.SortOrder
  count?: Prisma.SortOrder
  windowStart?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
}

export type RateLimitWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  identifier_action?: Prisma.RateLimitIdentifierActionCompoundUniqueInput
  AND?: Prisma.RateLimitWhereInput | Prisma.RateLimitWhereInput[]
  OR?: Prisma.RateLimitWhereInput[]
  NOT?: Prisma.RateLimitWhereInput | Prisma.RateLimitWhereInput[]
  identifier?: Prisma.StringFilter<"RateLimit"> | string
  action?: Prisma.StringFilter<"RateLimit"> | string
  count?: Prisma.IntFilter<"RateLimit"> | number
  windowStart?: Prisma.DateTimeFilter<"RateLimit"> | Date | string
  expiresAt?: Prisma.DateTimeFilter<"RateLimit"> | Date | string
}, "id" | "identifier_action">

export type RateLimitOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  action?: Prisma.SortOrder
  count?: Prisma.SortOrder
  windowStart?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  _count?: Prisma.RateLimitCountOrderByAggregateInput
  _avg?: Prisma.RateLimitAvgOrderByAggregateInput
  _max?: Prisma.RateLimitMaxOrderByAggregateInput
  _min?: Prisma.RateLimitMinOrderByAggregateInput
  _sum?: Prisma.RateLimitSumOrderByAggregateInput
}

export type RateLimitScalarWhereWithAggregatesInput = {
  AND?: Prisma.RateLimitScalarWhereWithAggregatesInput | Prisma.RateLimitScalarWhereWithAggregatesInput[]
  OR?: Prisma.RateLimitScalarWhereWithAggregatesInput[]
  NOT?: Prisma.RateLimitScalarWhereWithAggregatesInput | Prisma.RateLimitScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"RateLimit"> | string
  identifier?: Prisma.StringWithAggregatesFilter<"RateLimit"> | string
  action?: Prisma.StringWithAggregatesFilter<"RateLimit"> | string
  count?: Prisma.IntWithAggregatesFilter<"RateLimit"> | number
  windowStart?: Prisma.DateTimeWithAggregatesFilter<"RateLimit"> | Date | string
  expiresAt?: Prisma.DateTimeWithAggregatesFilter<"RateLimit"> | Date | string
}

export type RateLimitCreateInput = {
  id?: string
  identifier: string
  action: string
  count?: number
  windowStart?: Date | string
  expiresAt: Date | string
}

export type RateLimitUncheckedCreateInput = {
  id?: string
  identifier: string
  action: string
  count?: number
  windowStart?: Date | string
  expiresAt: Date | string
}

export type RateLimitUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  count?: Prisma.IntFieldUpdateOperationsInput | number
  windowStart?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RateLimitUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  count?: Prisma.IntFieldUpdateOperationsInput | number
  windowStart?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RateLimitCreateManyInput = {
  id?: string
  identifier: string
  action: string
  count?: number
  windowStart?: Date | string
  expiresAt: Date | string
}

export type RateLimitUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  count?: Prisma.IntFieldUpdateOperationsInput | number
  windowStart?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RateLimitUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
  action?: Prisma.StringFieldUpdateOperationsInput | string
  count?: Prisma.IntFieldUpdateOperationsInput | number
  windowStart?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type RateLimitIdentifierActionCompoundUniqueInput = {
  identifier: string
  action: string
}

export type RateLimitCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  action?: Prisma.SortOrder
  count?: Prisma.SortOrder
  windowStart?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
}

export type RateLimitAvgOrderByAggregateInput = {
  count?: Prisma.SortOrder
}

export type RateLimitMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  action?: Prisma.SortOrder
  count?: Prisma.SortOrder
  windowStart?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
}

export type RateLimitMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  action?: Prisma.SortOrder
  count?: Prisma.SortOrder
  windowStart?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
}

export type RateLimitSumOrderByAggregateInput = {
  count?: Prisma.SortOrder
}



export type RateLimitSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  identifier?: boolean
  action?: boolean
  count?: boolean
  windowStart?: boolean
  expiresAt?: boolean
}, ExtArgs["result"]["rateLimit"]>

export type RateLimitSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  identifier?: boolean
  action?: boolean
  count?: boolean
  windowStart?: boolean
  expiresAt?: boolean
}, ExtArgs["result"]["rateLimit"]>

export type RateLimitSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  identifier?: boolean
  action?: boolean
  count?: boolean
  windowStart?: boolean
  expiresAt?: boolean
}, ExtArgs["result"]["rateLimit"]>

export type RateLimitSelectScalar = {
  id?: boolean
  identifier?: boolean
  action?: boolean
  count?: boolean
  windowStart?: boolean
  expiresAt?: boolean
}

export type RateLimitOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "identifier" | "action" | "count" | "windowStart" | "expiresAt", ExtArgs["result"]["rateLimit"]>

export type $RateLimitPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "RateLimit"
  objects: {}
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    identifier: string
    action: string
    count: number
    windowStart: Date
    expiresAt: Date
  }, ExtArgs["result"]["rateLimit"]>
  composites: {}
}

export type RateLimitGetPayload<S extends boolean | null | undefined | RateLimitDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$RateLimitPayload, S>

export type RateLimitCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<RateLimitFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: RateLimitCountAggregateInputType | true
  }

export interface RateLimitDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['RateLimit'], meta: { name: 'RateLimit' } }
  /**
   * Find zero or one RateLimit that matches the filter.
   * @param {RateLimitFindUniqueArgs} args - Arguments to find a RateLimit
   * @example
   * // Get one RateLimit
   * const rateLimit = await prisma.rateLimit.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends RateLimitFindUniqueArgs>(args: Prisma.SelectSubset<T, RateLimitFindUniqueArgs<ExtArgs>>): Prisma.Prisma__RateLimitClient<runtime.Types.Result.GetResult<Prisma.$RateLimitPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one RateLimit that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {RateLimitFindUniqueOrThrowArgs} args - Arguments to find a RateLimit
   * @example
   * // Get one RateLimit
   * const rateLimit = await prisma.rateLimit.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends RateLimitFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, RateLimitFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__RateLimitClient<runtime.Types.Result.GetResult<Prisma.$RateLimitPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first RateLimit that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RateLimitFindFirstArgs} args - Arguments to find a RateLimit
   * @example
   * // Get one RateLimit
   * const rateLimit = await prisma.rateLimit.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends RateLimitFindFirstArgs>(args?: Prisma.SelectSubset<T, RateLimitFindFirstArgs<ExtArgs>>): Prisma.Prisma__RateLimitClient<runtime.Types.Result.GetResult<Prisma.$RateLimitPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first RateLimit that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RateLimitFindFirstOrThrowArgs} args - Arguments to find a RateLimit
   * @example
   * // Get one RateLimit
   * const rateLimit = await prisma.rateLimit.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends RateLimitFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, RateLimitFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__RateLimitClient<runtime.Types.Result.GetResult<Prisma.$RateLimitPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more RateLimits that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RateLimitFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all RateLimits
   * const rateLimits = await prisma.rateLimit.findMany()
   * 
   * // Get first 10 RateLimits
   * const rateLimits = await prisma.rateLimit.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const rateLimitWithIdOnly = await prisma.rateLimit.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends RateLimitFindManyArgs>(args?: Prisma.SelectSubset<T, RateLimitFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RateLimitPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a RateLimit.
   * @param {RateLimitCreateArgs} args - Arguments to create a RateLimit.
   * @example
   * // Create one RateLimit
   * const RateLimit = await prisma.rateLimit.create({
   *   data: {
   *     // ... data to create a RateLimit
   *   }
   * })
   * 
   */
  create<T extends RateLimitCreateArgs>(args: Prisma.SelectSubset<T, RateLimitCreateArgs<ExtArgs>>): Prisma.Prisma__RateLimitClient<runtime.Types.Result.GetResult<Prisma.$RateLimitPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many RateLimits.
   * @param {RateLimitCreateManyArgs} args - Arguments to create many RateLimits.
   * @example
   * // Create many RateLimits
   * const rateLimit = await prisma.rateLimit.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends RateLimitCreateManyArgs>(args?: Prisma.SelectSubset<T, RateLimitCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many RateLimits and returns the data saved in the database.
   * @param {RateLimitCreateManyAndReturnArgs} args - Arguments to create many RateLimits.
   * @example
   * // Create many RateLimits
   * const rateLimit = await prisma.rateLimit.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many RateLimits and only return the `id`
   * const rateLimitWithIdOnly = await prisma.rateLimit.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends RateLimitCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, RateLimitCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RateLimitPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a RateLimit.
   * @param {RateLimitDeleteArgs} args - Arguments to delete one RateLimit.
   * @example
   * // Delete one RateLimit
   * const RateLimit = await prisma.rateLimit.delete({
   *   where: {
   *     // ... filter to delete one RateLimit
   *   }
   * })
   * 
   */
  delete<T extends RateLimitDeleteArgs>(args: Prisma.SelectSubset<T, RateLimitDeleteArgs<ExtArgs>>): Prisma.Prisma__RateLimitClient<runtime.Types.Result.GetResult<Prisma.$RateLimitPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one RateLimit.
   * @param {RateLimitUpdateArgs} args - Arguments to update one RateLimit.
   * @example
   * // Update one RateLimit
   * const rateLimit = await prisma.rateLimit.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends RateLimitUpdateArgs>(args: Prisma.SelectSubset<T, RateLimitUpdateArgs<ExtArgs>>): Prisma.Prisma__RateLimitClient<runtime.Types.Result.GetResult<Prisma.$RateLimitPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more RateLimits.
   * @param {RateLimitDeleteManyArgs} args - Arguments to filter RateLimits to delete.
   * @example
   * // Delete a few RateLimits
   * const { count } = await prisma.rateLimit.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends RateLimitDeleteManyArgs>(args?: Prisma.SelectSubset<T, RateLimitDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more RateLimits.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RateLimitUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many RateLimits
   * const rateLimit = await prisma.rateLimit.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends RateLimitUpdateManyArgs>(args: Prisma.SelectSubset<T, RateLimitUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more RateLimits and returns the data updated in the database.
   * @param {RateLimitUpdateManyAndReturnArgs} args - Arguments to update many RateLimits.
   * @example
   * // Update many RateLimits
   * const rateLimit = await prisma.rateLimit.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more RateLimits and only return the `id`
   * const rateLimitWithIdOnly = await prisma.rateLimit.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends RateLimitUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, RateLimitUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$RateLimitPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one RateLimit.
   * @param {RateLimitUpsertArgs} args - Arguments to update or create a RateLimit.
   * @example
   * // Update or create a RateLimit
   * const rateLimit = await prisma.rateLimit.upsert({
   *   create: {
   *     // ... data to create a RateLimit
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the RateLimit we want to update
   *   }
   * })
   */
  upsert<T extends RateLimitUpsertArgs>(args: Prisma.SelectSubset<T, RateLimitUpsertArgs<ExtArgs>>): Prisma.Prisma__RateLimitClient<runtime.Types.Result.GetResult<Prisma.$RateLimitPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of RateLimits.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RateLimitCountArgs} args - Arguments to filter RateLimits to count.
   * @example
   * // Count the number of RateLimits
   * const count = await prisma.rateLimit.count({
   *   where: {
   *     // ... the filter for the RateLimits we want to count
   *   }
   * })
  **/
  count<T extends RateLimitCountArgs>(
    args?: Prisma.Subset<T, RateLimitCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], RateLimitCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a RateLimit.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RateLimitAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends RateLimitAggregateArgs>(args: Prisma.Subset<T, RateLimitAggregateArgs>): Prisma.PrismaPromise<GetRateLimitAggregateType<T>>

  /**
   * Group by RateLimit.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {RateLimitGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends RateLimitGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: RateLimitGroupByArgs['orderBy'] }
      : { orderBy?: RateLimitGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, RateLimitGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRateLimitGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the RateLimit model
 */
readonly fields: RateLimitFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for RateLimit.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__RateLimitClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the RateLimit model
 */
export interface RateLimitFieldRefs {
  readonly id: Prisma.FieldRef<"RateLimit", 'String'>
  readonly identifier: Prisma.FieldRef<"RateLimit", 'String'>
  readonly action: Prisma.FieldRef<"RateLimit", 'String'>
  readonly count: Prisma.FieldRef<"RateLimit", 'Int'>
  readonly windowStart: Prisma.FieldRef<"RateLimit", 'DateTime'>
  readonly expiresAt: Prisma.FieldRef<"RateLimit", 'DateTime'>
}
    

// Custom InputTypes
/**
 * RateLimit findUnique
 */
export type RateLimitFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RateLimit
   */
  select?: Prisma.RateLimitSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RateLimit
   */
  omit?: Prisma.RateLimitOmit<ExtArgs> | null
  /**
   * Filter, which RateLimit to fetch.
   */
  where: Prisma.RateLimitWhereUniqueInput
}

/**
 * RateLimit findUniqueOrThrow
 */
export type RateLimitFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RateLimit
   */
  select?: Prisma.RateLimitSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RateLimit
   */
  omit?: Prisma.RateLimitOmit<ExtArgs> | null
  /**
   * Filter, which RateLimit to fetch.
   */
  where: Prisma.RateLimitWhereUniqueInput
}

/**
 * RateLimit findFirst
 */
export type RateLimitFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RateLimit
   */
  select?: Prisma.RateLimitSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RateLimit
   */
  omit?: Prisma.RateLimitOmit<ExtArgs> | null
  /**
   * Filter, which RateLimit to fetch.
   */
  where?: Prisma.RateLimitWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RateLimits to fetch.
   */
  orderBy?: Prisma.RateLimitOrderByWithRelationInput | Prisma.RateLimitOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for RateLimits.
   */
  cursor?: Prisma.RateLimitWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RateLimits from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RateLimits.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of RateLimits.
   */
  distinct?: Prisma.RateLimitScalarFieldEnum | Prisma.RateLimitScalarFieldEnum[]
}

/**
 * RateLimit findFirstOrThrow
 */
export type RateLimitFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RateLimit
   */
  select?: Prisma.RateLimitSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RateLimit
   */
  omit?: Prisma.RateLimitOmit<ExtArgs> | null
  /**
   * Filter, which RateLimit to fetch.
   */
  where?: Prisma.RateLimitWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RateLimits to fetch.
   */
  orderBy?: Prisma.RateLimitOrderByWithRelationInput | Prisma.RateLimitOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for RateLimits.
   */
  cursor?: Prisma.RateLimitWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RateLimits from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RateLimits.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of RateLimits.
   */
  distinct?: Prisma.RateLimitScalarFieldEnum | Prisma.RateLimitScalarFieldEnum[]
}

/**
 * RateLimit findMany
 */
export type RateLimitFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RateLimit
   */
  select?: Prisma.RateLimitSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RateLimit
   */
  omit?: Prisma.RateLimitOmit<ExtArgs> | null
  /**
   * Filter, which RateLimits to fetch.
   */
  where?: Prisma.RateLimitWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of RateLimits to fetch.
   */
  orderBy?: Prisma.RateLimitOrderByWithRelationInput | Prisma.RateLimitOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing RateLimits.
   */
  cursor?: Prisma.RateLimitWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` RateLimits from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` RateLimits.
   */
  skip?: number
  distinct?: Prisma.RateLimitScalarFieldEnum | Prisma.RateLimitScalarFieldEnum[]
}

/**
 * RateLimit create
 */
export type RateLimitCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RateLimit
   */
  select?: Prisma.RateLimitSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RateLimit
   */
  omit?: Prisma.RateLimitOmit<ExtArgs> | null
  /**
   * The data needed to create a RateLimit.
   */
  data: Prisma.XOR<Prisma.RateLimitCreateInput, Prisma.RateLimitUncheckedCreateInput>
}

/**
 * RateLimit createMany
 */
export type RateLimitCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many RateLimits.
   */
  data: Prisma.RateLimitCreateManyInput | Prisma.RateLimitCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * RateLimit createManyAndReturn
 */
export type RateLimitCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RateLimit
   */
  select?: Prisma.RateLimitSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the RateLimit
   */
  omit?: Prisma.RateLimitOmit<ExtArgs> | null
  /**
   * The data used to create many RateLimits.
   */
  data: Prisma.RateLimitCreateManyInput | Prisma.RateLimitCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * RateLimit update
 */
export type RateLimitUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RateLimit
   */
  select?: Prisma.RateLimitSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RateLimit
   */
  omit?: Prisma.RateLimitOmit<ExtArgs> | null
  /**
   * The data needed to update a RateLimit.
   */
  data: Prisma.XOR<Prisma.RateLimitUpdateInput, Prisma.RateLimitUncheckedUpdateInput>
  /**
   * Choose, which RateLimit to update.
   */
  where: Prisma.RateLimitWhereUniqueInput
}

/**
 * RateLimit updateMany
 */
export type RateLimitUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update RateLimits.
   */
  data: Prisma.XOR<Prisma.RateLimitUpdateManyMutationInput, Prisma.RateLimitUncheckedUpdateManyInput>
  /**
   * Filter which RateLimits to update
   */
  where?: Prisma.RateLimitWhereInput
  /**
   * Limit how many RateLimits to update.
   */
  limit?: number
}

/**
 * RateLimit updateManyAndReturn
 */
export type RateLimitUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RateLimit
   */
  select?: Prisma.RateLimitSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the RateLimit
   */
  omit?: Prisma.RateLimitOmit<ExtArgs> | null
  /**
   * The data used to update RateLimits.
   */
  data: Prisma.XOR<Prisma.RateLimitUpdateManyMutationInput, Prisma.RateLimitUncheckedUpdateManyInput>
  /**
   * Filter which RateLimits to update
   */
  where?: Prisma.RateLimitWhereInput
  /**
   * Limit how many RateLimits to update.
   */
  limit?: number
}

/**
 * RateLimit upsert
 */
export type RateLimitUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RateLimit
   */
  select?: Prisma.RateLimitSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RateLimit
   */
  omit?: Prisma.RateLimitOmit<ExtArgs> | null
  /**
   * The filter to search for the RateLimit to update in case it exists.
   */
  where: Prisma.RateLimitWhereUniqueInput
  /**
   * In case the RateLimit found by the `where` argument doesn't exist, create a new RateLimit with this data.
   */
  create: Prisma.XOR<Prisma.RateLimitCreateInput, Prisma.RateLimitUncheckedCreateInput>
  /**
   * In case the RateLimit was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.RateLimitUpdateInput, Prisma.RateLimitUncheckedUpdateInput>
}

/**
 * RateLimit delete
 */
export type RateLimitDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RateLimit
   */
  select?: Prisma.RateLimitSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RateLimit
   */
  omit?: Prisma.RateLimitOmit<ExtArgs> | null
  /**
   * Filter which RateLimit to delete.
   */
  where: Prisma.RateLimitWhereUniqueInput
}

/**
 * RateLimit deleteMany
 */
export type RateLimitDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which RateLimits to delete
   */
  where?: Prisma.RateLimitWhereInput
  /**
   * Limit how many RateLimits to delete.
   */
  limit?: number
}

/**
 * RateLimit without action
 */
export type RateLimitDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the RateLimit
   */
  select?: Prisma.RateLimitSelect<ExtArgs> | null
  /**
   * Omit specific fields from the RateLimit
   */
  omit?: Prisma.RateLimitOmit<ExtArgs> | null
}
