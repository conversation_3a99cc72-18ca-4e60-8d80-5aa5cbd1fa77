
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/*
 * WARNING: This is an internal file that is subject to change!
 *
 * 🛑 Under no circumstances should you import this file directly! 🛑
 *
 * All exports from this file are wrapped under a `Prisma` namespace object in the browser.ts file.
 * While this enables partial backward compatibility, it is not part of the stable public API.
 *
 * If you are looking for your Models, Enums, and Input Types, please import them from the respective
 * model files in the `model` directory!
 */

import * as runtime from "@prisma/client/runtime/index-browser"
export type * from '../models'
export type * from './prismaNamespace'
export const Decimal = runtime.Decimal
export const ModelName = {
  User: 'User',
  Session: 'Session',
  Account: 'Account',
  Verification: 'Verification',
  UserInvitation: 'UserInvitation',
  PaymentIntent: 'PaymentIntent',
  WebhookEvent: 'WebhookEvent',
  License: 'License',
  Device: 'Device',
  DeviceExpansion: 'DeviceExpansion',
  RefundRequest: 'RefundRequest',
  AuditLog: 'AuditLog',
  RateLimit: 'RateLimit',
  SupportTicket: 'SupportTicket',
  SupportMessage: 'SupportMessage'
} as const

export type ModelName = (typeof ModelName)[keyof typeof ModelName]

/**
 * Enums
 */
export const TransactionIsolationLevel = runtime.makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
} as const)

export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


export const UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  emailVerified: 'emailVerified',
  image: 'image',
  role: 'role',
  isActive: 'isActive',
  invitedBy: 'invitedBy',
  invitedAt: 'invitedAt',
  lastLoginAt: 'lastLoginAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


export const SessionScalarFieldEnum = {
  id: 'id',
  expiresAt: 'expiresAt',
  token: 'token',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  userId: 'userId'
} as const

export type SessionScalarFieldEnum = (typeof SessionScalarFieldEnum)[keyof typeof SessionScalarFieldEnum]


export const AccountScalarFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  providerId: 'providerId',
  userId: 'userId',
  accessToken: 'accessToken',
  refreshToken: 'refreshToken',
  idToken: 'idToken',
  accessTokenExpiresAt: 'accessTokenExpiresAt',
  refreshTokenExpiresAt: 'refreshTokenExpiresAt',
  scope: 'scope',
  password: 'password',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type AccountScalarFieldEnum = (typeof AccountScalarFieldEnum)[keyof typeof AccountScalarFieldEnum]


export const VerificationScalarFieldEnum = {
  id: 'id',
  identifier: 'identifier',
  value: 'value',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
} as const

export type VerificationScalarFieldEnum = (typeof VerificationScalarFieldEnum)[keyof typeof VerificationScalarFieldEnum]


export const UserInvitationScalarFieldEnum = {
  id: 'id',
  email: 'email',
  role: 'role',
  token: 'token',
  status: 'status',
  expiresAt: 'expiresAt',
  sentAt: 'sentAt',
  acceptedAt: 'acceptedAt',
  sentBy: 'sentBy',
  acceptedBy: 'acceptedBy'
} as const

export type UserInvitationScalarFieldEnum = (typeof UserInvitationScalarFieldEnum)[keyof typeof UserInvitationScalarFieldEnum]


export const PaymentIntentScalarFieldEnum = {
  id: 'id',
  stripePaymentIntentId: 'stripePaymentIntentId',
  stripeCheckoutSessionId: 'stripeCheckoutSessionId',
  amount: 'amount',
  currency: 'currency',
  status: 'status',
  paymentType: 'paymentType',
  customerEmail: 'customerEmail',
  customerName: 'customerName',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  processedAt: 'processedAt'
} as const

export type PaymentIntentScalarFieldEnum = (typeof PaymentIntentScalarFieldEnum)[keyof typeof PaymentIntentScalarFieldEnum]


export const WebhookEventScalarFieldEnum = {
  id: 'id',
  stripeEventId: 'stripeEventId',
  eventType: 'eventType',
  processed: 'processed',
  processedAt: 'processedAt',
  errorMessage: 'errorMessage',
  retryCount: 'retryCount',
  createdAt: 'createdAt',
  paymentIntentId: 'paymentIntentId'
} as const

export type WebhookEventScalarFieldEnum = (typeof WebhookEventScalarFieldEnum)[keyof typeof WebhookEventScalarFieldEnum]


export const LicenseScalarFieldEnum = {
  id: 'id',
  licenseKey: 'licenseKey',
  licenseType: 'licenseType',
  status: 'status',
  maxDevices: 'maxDevices',
  usedDevices: 'usedDevices',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  expiresAt: 'expiresAt',
  activatedAt: 'activatedAt',
  customerEmail: 'customerEmail',
  customerName: 'customerName',
  createdBy: 'createdBy',
  paymentIntentId: 'paymentIntentId',
  totalPaidAmount: 'totalPaidAmount',
  refundedAt: 'refundedAt',
  refundReason: 'refundReason',
  refundAmount: 'refundAmount',
  emailSentAt: 'emailSentAt',
  emailDeliveryStatus: 'emailDeliveryStatus'
} as const

export type LicenseScalarFieldEnum = (typeof LicenseScalarFieldEnum)[keyof typeof LicenseScalarFieldEnum]


export const DeviceScalarFieldEnum = {
  id: 'id',
  licenseId: 'licenseId',
  deviceHash: 'deviceHash',
  salt: 'salt',
  status: 'status',
  firstSeen: 'firstSeen',
  lastSeen: 'lastSeen',
  removedAt: 'removedAt',
  appVersion: 'appVersion',
  deviceName: 'deviceName',
  deviceType: 'deviceType',
  deviceModel: 'deviceModel',
  operatingSystem: 'operatingSystem',
  architecture: 'architecture',
  screenResolution: 'screenResolution',
  totalMemory: 'totalMemory',
  userNickname: 'userNickname',
  location: 'location',
  notes: 'notes'
} as const

export type DeviceScalarFieldEnum = (typeof DeviceScalarFieldEnum)[keyof typeof DeviceScalarFieldEnum]


export const DeviceExpansionScalarFieldEnum = {
  id: 'id',
  licenseId: 'licenseId',
  paymentIntentId: 'paymentIntentId',
  additionalDevices: 'additionalDevices',
  amount: 'amount',
  status: 'status',
  createdAt: 'createdAt',
  processedAt: 'processedAt'
} as const

export type DeviceExpansionScalarFieldEnum = (typeof DeviceExpansionScalarFieldEnum)[keyof typeof DeviceExpansionScalarFieldEnum]


export const RefundRequestScalarFieldEnum = {
  id: 'id',
  licenseId: 'licenseId',
  requestedBy: 'requestedBy',
  reason: 'reason',
  status: 'status',
  requestedAmount: 'requestedAmount',
  approvedAmount: 'approvedAmount',
  stripeRefundIds: 'stripeRefundIds',
  adminNotes: 'adminNotes',
  processedBy: 'processedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  processedAt: 'processedAt'
} as const

export type RefundRequestScalarFieldEnum = (typeof RefundRequestScalarFieldEnum)[keyof typeof RefundRequestScalarFieldEnum]


export const AuditLogScalarFieldEnum = {
  id: 'id',
  action: 'action',
  licenseKey: 'licenseKey',
  deviceHash: 'deviceHash',
  licenseId: 'licenseId',
  deviceId: 'deviceId',
  userId: 'userId',
  userEmail: 'userEmail',
  targetId: 'targetId',
  customerEmail: 'customerEmail',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  details: 'details',
  createdAt: 'createdAt'
} as const

export type AuditLogScalarFieldEnum = (typeof AuditLogScalarFieldEnum)[keyof typeof AuditLogScalarFieldEnum]


export const RateLimitScalarFieldEnum = {
  id: 'id',
  identifier: 'identifier',
  action: 'action',
  count: 'count',
  windowStart: 'windowStart',
  expiresAt: 'expiresAt'
} as const

export type RateLimitScalarFieldEnum = (typeof RateLimitScalarFieldEnum)[keyof typeof RateLimitScalarFieldEnum]


export const SupportTicketScalarFieldEnum = {
  id: 'id',
  ticketId: 'ticketId',
  subject: 'subject',
  description: 'description',
  status: 'status',
  priority: 'priority',
  category: 'category',
  customerEmail: 'customerEmail',
  customerName: 'customerName',
  licenseKey: 'licenseKey',
  assignedTo: 'assignedTo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  resolvedAt: 'resolvedAt'
} as const

export type SupportTicketScalarFieldEnum = (typeof SupportTicketScalarFieldEnum)[keyof typeof SupportTicketScalarFieldEnum]


export const SupportMessageScalarFieldEnum = {
  id: 'id',
  ticketId: 'ticketId',
  message: 'message',
  isInternal: 'isInternal',
  authorEmail: 'authorEmail',
  authorId: 'authorId',
  createdAt: 'createdAt'
} as const

export type SupportMessageScalarFieldEnum = (typeof SupportMessageScalarFieldEnum)[keyof typeof SupportMessageScalarFieldEnum]


export const SortOrder = {
  asc: 'asc',
  desc: 'desc'
} as const

export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


export const NullableJsonNullValueInput = {
  DbNull: DbNull,
  JsonNull: JsonNull
} as const

export type NullableJsonNullValueInput = (typeof NullableJsonNullValueInput)[keyof typeof NullableJsonNullValueInput]


export const QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
} as const

export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


export const NullsOrder = {
  first: 'first',
  last: 'last'
} as const

export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


export const JsonNullValueFilter = {
  DbNull: DbNull,
  JsonNull: JsonNull,
  AnyNull: AnyNull
} as const

export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]

