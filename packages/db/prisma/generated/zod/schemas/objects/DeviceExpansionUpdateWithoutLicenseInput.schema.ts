import { z } from 'zod';
import type { Prisma } from '../../../client';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { DeviceExpansionStatusSchema } from '../enums/DeviceExpansionStatus.schema';
import { EnumDeviceExpansionStatusFieldUpdateOperationsInputObjectSchema } from './EnumDeviceExpansionStatusFieldUpdateOperationsInput.schema';
import { NullableDateTimeFieldUpdateOperationsInputObjectSchema } from './NullableDateTimeFieldUpdateOperationsInput.schema';
import { PaymentIntentUpdateOneRequiredWithoutDeviceExpansionsNestedInputObjectSchema } from './PaymentIntentUpdateOneRequiredWithoutDeviceExpansionsNestedInput.schema'

const makeSchema = () => z.object({
  additionalDevices: z.union([z.number().int(), z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(),
  amount: z.union([z.number().int(), z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(),
  status: z.union([DeviceExpansionStatusSchema, z.lazy(() => EnumDeviceExpansionStatusFieldUpdateOperationsInputObjectSchema)]).optional(),
  processedAt: z.union([z.coerce.date(), z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  paymentIntent: z.lazy(() => PaymentIntentUpdateOneRequiredWithoutDeviceExpansionsNestedInputObjectSchema).optional()
}).strict();
export const DeviceExpansionUpdateWithoutLicenseInputObjectSchema: z.ZodType<Prisma.DeviceExpansionUpdateWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionUpdateWithoutLicenseInput>;
export const DeviceExpansionUpdateWithoutLicenseInputObjectZodSchema = makeSchema();
