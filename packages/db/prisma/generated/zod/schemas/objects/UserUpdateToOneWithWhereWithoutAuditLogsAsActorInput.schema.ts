import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserUpdateWithoutAuditLogsAsActorInputObjectSchema } from './UserUpdateWithoutAuditLogsAsActorInput.schema';
import { UserUncheckedUpdateWithoutAuditLogsAsActorInputObjectSchema } from './UserUncheckedUpdateWithoutAuditLogsAsActorInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserWhereInputObjectSchema).optional(),
  data: z.union([z.lazy(() => UserUpdateWithoutAuditLogsAsActorInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutAuditLogsAsActorInputObjectSchema)])
}).strict();
export const UserUpdateToOneWithWhereWithoutAuditLogsAsActorInputObjectSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutAuditLogsAsActorInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutAuditLogsAsActorInput>;
export const UserUpdateToOneWithWhereWithoutAuditLogsAsActorInputObjectZodSchema = makeSchema();
