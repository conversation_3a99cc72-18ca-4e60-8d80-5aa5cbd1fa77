import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutSupportMessagesInputObjectSchema } from './UserCreateWithoutSupportMessagesInput.schema';
import { UserUncheckedCreateWithoutSupportMessagesInputObjectSchema } from './UserUncheckedCreateWithoutSupportMessagesInput.schema';
import { UserCreateOrConnectWithoutSupportMessagesInputObjectSchema } from './UserCreateOrConnectWithoutSupportMessagesInput.schema';
import { UserUpsertWithoutSupportMessagesInputObjectSchema } from './UserUpsertWithoutSupportMessagesInput.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserUpdateToOneWithWhereWithoutSupportMessagesInputObjectSchema } from './UserUpdateToOneWithWhereWithoutSupportMessagesInput.schema';
import { UserUpdateWithoutSupportMessagesInputObjectSchema } from './UserUpdateWithoutSupportMessagesInput.schema';
import { UserUncheckedUpdateWithoutSupportMessagesInputObjectSchema } from './UserUncheckedUpdateWithoutSupportMessagesInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutSupportMessagesInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutSupportMessagesInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutSupportMessagesInputObjectSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutSupportMessagesInputObjectSchema).optional(),
  disconnect: z.union([z.boolean(), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  delete: z.union([z.boolean(), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional(),
  update: z.union([z.lazy(() => UserUpdateToOneWithWhereWithoutSupportMessagesInputObjectSchema), z.lazy(() => UserUpdateWithoutSupportMessagesInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutSupportMessagesInputObjectSchema)]).optional()
}).strict();
export const UserUpdateOneWithoutSupportMessagesNestedInputObjectSchema: z.ZodType<Prisma.UserUpdateOneWithoutSupportMessagesNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateOneWithoutSupportMessagesNestedInput>;
export const UserUpdateOneWithoutSupportMessagesNestedInputObjectZodSchema = makeSchema();
