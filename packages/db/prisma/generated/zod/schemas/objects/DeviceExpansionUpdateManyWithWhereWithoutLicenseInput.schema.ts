import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceExpansionScalarWhereInputObjectSchema } from './DeviceExpansionScalarWhereInput.schema';
import { DeviceExpansionUpdateManyMutationInputObjectSchema } from './DeviceExpansionUpdateManyMutationInput.schema';
import { DeviceExpansionUncheckedUpdateManyWithoutLicenseInputObjectSchema } from './DeviceExpansionUncheckedUpdateManyWithoutLicenseInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => DeviceExpansionScalarWhereInputObjectSchema),
  data: z.union([z.lazy(() => DeviceExpansionUpdateManyMutationInputObjectSchema), z.lazy(() => DeviceExpansionUncheckedUpdateManyWithoutLicenseInputObjectSchema)])
}).strict();
export const DeviceExpansionUpdateManyWithWhereWithoutLicenseInputObjectSchema: z.ZodType<Prisma.DeviceExpansionUpdateManyWithWhereWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionUpdateManyWithWhereWithoutLicenseInput>;
export const DeviceExpansionUpdateManyWithWhereWithoutLicenseInputObjectZodSchema = makeSchema();
