import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.boolean().optional(),
  identifier: z.boolean().optional(),
  action: z.boolean().optional(),
  count: z.boolean().optional(),
  windowStart: z.boolean().optional(),
  expiresAt: z.boolean().optional()
}).strict();
export const RateLimitSelectObjectSchema: z.ZodType<Prisma.RateLimitSelect> = makeSchema() as unknown as z.ZodType<Prisma.RateLimitSelect>;
export const RateLimitSelectObjectZodSchema = makeSchema();
