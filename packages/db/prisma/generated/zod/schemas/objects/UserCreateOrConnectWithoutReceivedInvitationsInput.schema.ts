import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserCreateWithoutReceivedInvitationsInputObjectSchema } from './UserCreateWithoutReceivedInvitationsInput.schema';
import { UserUncheckedCreateWithoutReceivedInvitationsInputObjectSchema } from './UserUncheckedCreateWithoutReceivedInvitationsInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => UserCreateWithoutReceivedInvitationsInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutReceivedInvitationsInputObjectSchema)])
}).strict();
export const UserCreateOrConnectWithoutReceivedInvitationsInputObjectSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutReceivedInvitationsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateOrConnectWithoutReceivedInvitationsInput>;
export const UserCreateOrConnectWithoutReceivedInvitationsInputObjectZodSchema = makeSchema();
