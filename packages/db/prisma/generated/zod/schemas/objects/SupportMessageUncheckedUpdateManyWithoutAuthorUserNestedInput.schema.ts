import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageCreateWithoutAuthorUserInputObjectSchema } from './SupportMessageCreateWithoutAuthorUserInput.schema';
import { SupportMessageUncheckedCreateWithoutAuthorUserInputObjectSchema } from './SupportMessageUncheckedCreateWithoutAuthorUserInput.schema';
import { SupportMessageCreateOrConnectWithoutAuthorUserInputObjectSchema } from './SupportMessageCreateOrConnectWithoutAuthorUserInput.schema';
import { SupportMessageUpsertWithWhereUniqueWithoutAuthorUserInputObjectSchema } from './SupportMessageUpsertWithWhereUniqueWithoutAuthorUserInput.schema';
import { SupportMessageCreateManyAuthorUserInputEnvelopeObjectSchema } from './SupportMessageCreateManyAuthorUserInputEnvelope.schema';
import { SupportMessageWhereUniqueInputObjectSchema } from './SupportMessageWhereUniqueInput.schema';
import { SupportMessageUpdateWithWhereUniqueWithoutAuthorUserInputObjectSchema } from './SupportMessageUpdateWithWhereUniqueWithoutAuthorUserInput.schema';
import { SupportMessageUpdateManyWithWhereWithoutAuthorUserInputObjectSchema } from './SupportMessageUpdateManyWithWhereWithoutAuthorUserInput.schema';
import { SupportMessageScalarWhereInputObjectSchema } from './SupportMessageScalarWhereInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => SupportMessageCreateWithoutAuthorUserInputObjectSchema), z.lazy(() => SupportMessageCreateWithoutAuthorUserInputObjectSchema).array(), z.lazy(() => SupportMessageUncheckedCreateWithoutAuthorUserInputObjectSchema), z.lazy(() => SupportMessageUncheckedCreateWithoutAuthorUserInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => SupportMessageCreateOrConnectWithoutAuthorUserInputObjectSchema), z.lazy(() => SupportMessageCreateOrConnectWithoutAuthorUserInputObjectSchema).array()]).optional(),
  upsert: z.union([z.lazy(() => SupportMessageUpsertWithWhereUniqueWithoutAuthorUserInputObjectSchema), z.lazy(() => SupportMessageUpsertWithWhereUniqueWithoutAuthorUserInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => SupportMessageCreateManyAuthorUserInputEnvelopeObjectSchema).optional(),
  set: z.union([z.lazy(() => SupportMessageWhereUniqueInputObjectSchema), z.lazy(() => SupportMessageWhereUniqueInputObjectSchema).array()]).optional(),
  disconnect: z.union([z.lazy(() => SupportMessageWhereUniqueInputObjectSchema), z.lazy(() => SupportMessageWhereUniqueInputObjectSchema).array()]).optional(),
  delete: z.union([z.lazy(() => SupportMessageWhereUniqueInputObjectSchema), z.lazy(() => SupportMessageWhereUniqueInputObjectSchema).array()]).optional(),
  connect: z.union([z.lazy(() => SupportMessageWhereUniqueInputObjectSchema), z.lazy(() => SupportMessageWhereUniqueInputObjectSchema).array()]).optional(),
  update: z.union([z.lazy(() => SupportMessageUpdateWithWhereUniqueWithoutAuthorUserInputObjectSchema), z.lazy(() => SupportMessageUpdateWithWhereUniqueWithoutAuthorUserInputObjectSchema).array()]).optional(),
  updateMany: z.union([z.lazy(() => SupportMessageUpdateManyWithWhereWithoutAuthorUserInputObjectSchema), z.lazy(() => SupportMessageUpdateManyWithWhereWithoutAuthorUserInputObjectSchema).array()]).optional(),
  deleteMany: z.union([z.lazy(() => SupportMessageScalarWhereInputObjectSchema), z.lazy(() => SupportMessageScalarWhereInputObjectSchema).array()]).optional()
}).strict();
export const SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInputObjectSchema: z.ZodType<Prisma.SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInput>;
export const SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInputObjectZodSchema = makeSchema();
