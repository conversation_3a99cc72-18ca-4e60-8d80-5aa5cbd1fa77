import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceExpansionStatusSchema } from '../enums/DeviceExpansionStatus.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  licenseId: z.string(),
  paymentIntentId: z.string(),
  additionalDevices: z.number().int(),
  amount: z.number().int(),
  status: DeviceExpansionStatusSchema.optional(),
  createdAt: z.coerce.date().optional(),
  processedAt: z.coerce.date().optional().nullable()
}).strict();
export const DeviceExpansionUncheckedCreateInputObjectSchema: z.ZodType<Prisma.DeviceExpansionUncheckedCreateInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionUncheckedCreateInput>;
export const DeviceExpansionUncheckedCreateInputObjectZodSchema = makeSchema();
