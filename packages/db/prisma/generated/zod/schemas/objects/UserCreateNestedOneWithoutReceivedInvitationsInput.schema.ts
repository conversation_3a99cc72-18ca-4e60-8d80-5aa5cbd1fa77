import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutReceivedInvitationsInputObjectSchema } from './UserCreateWithoutReceivedInvitationsInput.schema';
import { UserUncheckedCreateWithoutReceivedInvitationsInputObjectSchema } from './UserUncheckedCreateWithoutReceivedInvitationsInput.schema';
import { UserCreateOrConnectWithoutReceivedInvitationsInputObjectSchema } from './UserCreateOrConnectWithoutReceivedInvitationsInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutReceivedInvitationsInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutReceivedInvitationsInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutReceivedInvitationsInputObjectSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional()
}).strict();
export const UserCreateNestedOneWithoutReceivedInvitationsInputObjectSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutReceivedInvitationsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateNestedOneWithoutReceivedInvitationsInput>;
export const UserCreateNestedOneWithoutReceivedInvitationsInputObjectZodSchema = makeSchema();
