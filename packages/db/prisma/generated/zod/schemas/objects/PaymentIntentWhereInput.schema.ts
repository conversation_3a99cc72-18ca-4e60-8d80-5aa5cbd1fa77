import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { IntFilterObjectSchema } from './IntFilter.schema';
import { EnumPaymentStatusFilterObjectSchema } from './EnumPaymentStatusFilter.schema';
import { PaymentStatusSchema } from '../enums/PaymentStatus.schema';
import { EnumPaymentTypeFilterObjectSchema } from './EnumPaymentTypeFilter.schema';
import { PaymentTypeSchema } from '../enums/PaymentType.schema';
import { DateTimeNullableFilterObjectSchema } from './DateTimeNullableFilter.schema';
import { LicenseListRelationFilterObjectSchema } from './LicenseListRelationFilter.schema';
import { DeviceExpansionListRelationFilterObjectSchema } from './DeviceExpansionListRelationFilter.schema';
import { WebhookEventListRelationFilterObjectSchema } from './WebhookEventListRelationFilter.schema'

const paymentintentwhereinputSchema = z.object({
  AND: z.union([z.lazy(() => PaymentIntentWhereInputObjectSchema), z.lazy(() => PaymentIntentWhereInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => PaymentIntentWhereInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => PaymentIntentWhereInputObjectSchema), z.lazy(() => PaymentIntentWhereInputObjectSchema).array()]).optional(),
  stripePaymentIntentId: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  stripeCheckoutSessionId: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  amount: z.union([z.lazy(() => IntFilterObjectSchema), z.number().int()]).optional(),
  currency: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  status: z.union([z.lazy(() => EnumPaymentStatusFilterObjectSchema), PaymentStatusSchema]).optional(),
  paymentType: z.union([z.lazy(() => EnumPaymentTypeFilterObjectSchema), PaymentTypeSchema]).optional(),
  customerEmail: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  customerName: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  processedAt: z.union([z.lazy(() => DateTimeNullableFilterObjectSchema), z.coerce.date()]).optional().nullable(),
  licenses: z.lazy(() => LicenseListRelationFilterObjectSchema).optional(),
  deviceExpansions: z.lazy(() => DeviceExpansionListRelationFilterObjectSchema).optional(),
  webhookEvents: z.lazy(() => WebhookEventListRelationFilterObjectSchema).optional()
}).strict();
export const PaymentIntentWhereInputObjectSchema: z.ZodType<Prisma.PaymentIntentWhereInput> = paymentintentwhereinputSchema as unknown as z.ZodType<Prisma.PaymentIntentWhereInput>;
export const PaymentIntentWhereInputObjectZodSchema = paymentintentwhereinputSchema;
