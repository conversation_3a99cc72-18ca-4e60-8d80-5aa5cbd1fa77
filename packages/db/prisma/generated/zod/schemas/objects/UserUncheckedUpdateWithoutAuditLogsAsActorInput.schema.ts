import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { BoolFieldUpdateOperationsInputObjectSchema } from './BoolFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { UserRoleSchema } from '../enums/UserRole.schema';
import { EnumUserRoleFieldUpdateOperationsInputObjectSchema } from './EnumUserRoleFieldUpdateOperationsInput.schema';
import { NullableDateTimeFieldUpdateOperationsInputObjectSchema } from './NullableDateTimeFieldUpdateOperationsInput.schema';
import { SessionUncheckedUpdateManyWithoutUserNestedInputObjectSchema } from './SessionUncheckedUpdateManyWithoutUserNestedInput.schema';
import { AccountUncheckedUpdateManyWithoutUserNestedInputObjectSchema } from './AccountUncheckedUpdateManyWithoutUserNestedInput.schema';
import { LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInputObjectSchema } from './LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInput.schema';
import { UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInputObjectSchema } from './UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput.schema';
import { UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInputObjectSchema } from './UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInput.schema';
import { AuditLogUncheckedUpdateManyWithoutTargetNestedInputObjectSchema } from './AuditLogUncheckedUpdateManyWithoutTargetNestedInput.schema';
import { RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInputObjectSchema } from './RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInput.schema';
import { SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInputObjectSchema } from './SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInput.schema';
import { SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInputObjectSchema } from './SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInput.schema'

const makeSchema = () => z.object({
  name: z.union([z.string(), z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(),
  email: z.union([z.string(), z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(),
  emailVerified: z.union([z.boolean(), z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(),
  image: z.union([z.string(), z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  role: z.union([UserRoleSchema, z.lazy(() => EnumUserRoleFieldUpdateOperationsInputObjectSchema)]).optional(),
  isActive: z.union([z.boolean(), z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(),
  invitedBy: z.union([z.string(), z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  invitedAt: z.union([z.coerce.date(), z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  lastLoginAt: z.union([z.coerce.date(), z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  sessions: z.lazy(() => SessionUncheckedUpdateManyWithoutUserNestedInputObjectSchema).optional(),
  accounts: z.lazy(() => AccountUncheckedUpdateManyWithoutUserNestedInputObjectSchema).optional(),
  createdLicenses: z.lazy(() => LicenseUncheckedUpdateManyWithoutCreatedByUserNestedInputObjectSchema).optional(),
  sentInvitations: z.lazy(() => UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInputObjectSchema).optional(),
  receivedInvitations: z.lazy(() => UserInvitationUncheckedUpdateManyWithoutAcceptedByUserNestedInputObjectSchema).optional(),
  auditLogsAsTarget: z.lazy(() => AuditLogUncheckedUpdateManyWithoutTargetNestedInputObjectSchema).optional(),
  processedRefunds: z.lazy(() => RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInputObjectSchema).optional(),
  assignedTickets: z.lazy(() => SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInputObjectSchema).optional(),
  supportMessages: z.lazy(() => SupportMessageUncheckedUpdateManyWithoutAuthorUserNestedInputObjectSchema).optional()
}).strict();
export const UserUncheckedUpdateWithoutAuditLogsAsActorInputObjectSchema: z.ZodType<Prisma.UserUncheckedUpdateWithoutAuditLogsAsActorInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUncheckedUpdateWithoutAuditLogsAsActorInput>;
export const UserUncheckedUpdateWithoutAuditLogsAsActorInputObjectZodSchema = makeSchema();
