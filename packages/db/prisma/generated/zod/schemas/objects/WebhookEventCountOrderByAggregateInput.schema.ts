import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  stripeEventId: SortOrderSchema.optional(),
  eventType: SortOrderSchema.optional(),
  processed: SortOrderSchema.optional(),
  processedAt: SortOrderSchema.optional(),
  errorMessage: SortOrderSchema.optional(),
  retryCount: SortOrderSchema.optional(),
  createdAt: SortOrderSchema.optional(),
  paymentIntentId: SortOrderSchema.optional()
}).strict();
export const WebhookEventCountOrderByAggregateInputObjectSchema: z.ZodType<Prisma.WebhookEventCountOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventCountOrderByAggregateInput>;
export const WebhookEventCountOrderByAggregateInputObjectZodSchema = makeSchema();
