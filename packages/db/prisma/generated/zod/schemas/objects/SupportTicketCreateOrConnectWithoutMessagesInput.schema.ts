import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketWhereUniqueInputObjectSchema } from './SupportTicketWhereUniqueInput.schema';
import { SupportTicketCreateWithoutMessagesInputObjectSchema } from './SupportTicketCreateWithoutMessagesInput.schema';
import { SupportTicketUncheckedCreateWithoutMessagesInputObjectSchema } from './SupportTicketUncheckedCreateWithoutMessagesInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => SupportTicketWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => SupportTicketCreateWithoutMessagesInputObjectSchema), z.lazy(() => SupportTicketUncheckedCreateWithoutMessagesInputObjectSchema)])
}).strict();
export const SupportTicketCreateOrConnectWithoutMessagesInputObjectSchema: z.ZodType<Prisma.SupportTicketCreateOrConnectWithoutMessagesInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketCreateOrConnectWithoutMessagesInput>;
export const SupportTicketCreateOrConnectWithoutMessagesInputObjectZodSchema = makeSchema();
