import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { SupportMessageCountOrderByAggregateInputObjectSchema } from './SupportMessageCountOrderByAggregateInput.schema';
import { SupportMessageMaxOrderByAggregateInputObjectSchema } from './SupportMessageMaxOrderByAggregateInput.schema';
import { SupportMessageMinOrderByAggregateInputObjectSchema } from './SupportMessageMinOrderByAggregateInput.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  ticketId: SortOrderSchema.optional(),
  message: SortOrderSchema.optional(),
  isInternal: SortOrderSchema.optional(),
  authorEmail: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  authorId: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  createdAt: SortOrderSchema.optional(),
  _count: z.lazy(() => SupportMessageCountOrderByAggregateInputObjectSchema).optional(),
  _max: z.lazy(() => SupportMessageMaxOrderByAggregateInputObjectSchema).optional(),
  _min: z.lazy(() => SupportMessageMinOrderByAggregateInputObjectSchema).optional()
}).strict();
export const SupportMessageOrderByWithAggregationInputObjectSchema: z.ZodType<Prisma.SupportMessageOrderByWithAggregationInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageOrderByWithAggregationInput>;
export const SupportMessageOrderByWithAggregationInputObjectZodSchema = makeSchema();
