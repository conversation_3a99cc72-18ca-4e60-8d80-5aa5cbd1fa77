import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationScalarWhereInputObjectSchema } from './UserInvitationScalarWhereInput.schema';
import { UserInvitationUpdateManyMutationInputObjectSchema } from './UserInvitationUpdateManyMutationInput.schema';
import { UserInvitationUncheckedUpdateManyWithoutAcceptedByUserInputObjectSchema } from './UserInvitationUncheckedUpdateManyWithoutAcceptedByUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserInvitationScalarWhereInputObjectSchema),
  data: z.union([z.lazy(() => UserInvitationUpdateManyMutationInputObjectSchema), z.lazy(() => UserInvitationUncheckedUpdateManyWithoutAcceptedByUserInputObjectSchema)])
}).strict();
export const UserInvitationUpdateManyWithWhereWithoutAcceptedByUserInputObjectSchema: z.ZodType<Prisma.UserInvitationUpdateManyWithWhereWithoutAcceptedByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationUpdateManyWithWhereWithoutAcceptedByUserInput>;
export const UserInvitationUpdateManyWithWhereWithoutAcceptedByUserInputObjectZodSchema = makeSchema();
