import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { PaymentIntentOrderByWithRelationInputObjectSchema } from './PaymentIntentOrderByWithRelationInput.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  stripeEventId: SortOrderSchema.optional(),
  eventType: SortOrderSchema.optional(),
  processed: SortOrderSchema.optional(),
  processedAt: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  errorMessage: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  retryCount: SortOrderSchema.optional(),
  createdAt: SortOrderSchema.optional(),
  paymentIntentId: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  paymentIntent: z.lazy(() => PaymentIntentOrderByWithRelationInputObjectSchema).optional()
}).strict();
export const WebhookEventOrderByWithRelationInputObjectSchema: z.ZodType<Prisma.WebhookEventOrderByWithRelationInput> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventOrderByWithRelationInput>;
export const WebhookEventOrderByWithRelationInputObjectZodSchema = makeSchema();
