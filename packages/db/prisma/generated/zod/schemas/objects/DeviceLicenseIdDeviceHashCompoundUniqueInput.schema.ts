import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  licenseId: z.string(),
  deviceHash: z.string()
}).strict();
export const DeviceLicenseIdDeviceHashCompoundUniqueInputObjectSchema: z.ZodType<Prisma.DeviceLicenseIdDeviceHashCompoundUniqueInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceLicenseIdDeviceHashCompoundUniqueInput>;
export const DeviceLicenseIdDeviceHashCompoundUniqueInputObjectZodSchema = makeSchema();
