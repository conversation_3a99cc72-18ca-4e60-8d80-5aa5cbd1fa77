import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceCreateWithoutLicenseInputObjectSchema } from './DeviceCreateWithoutLicenseInput.schema';
import { DeviceUncheckedCreateWithoutLicenseInputObjectSchema } from './DeviceUncheckedCreateWithoutLicenseInput.schema';
import { DeviceCreateOrConnectWithoutLicenseInputObjectSchema } from './DeviceCreateOrConnectWithoutLicenseInput.schema';
import { DeviceUpsertWithWhereUniqueWithoutLicenseInputObjectSchema } from './DeviceUpsertWithWhereUniqueWithoutLicenseInput.schema';
import { DeviceCreateManyLicenseInputEnvelopeObjectSchema } from './DeviceCreateManyLicenseInputEnvelope.schema';
import { DeviceWhereUniqueInputObjectSchema } from './DeviceWhereUniqueInput.schema';
import { DeviceUpdateWithWhereUniqueWithoutLicenseInputObjectSchema } from './DeviceUpdateWithWhereUniqueWithoutLicenseInput.schema';
import { DeviceUpdateManyWithWhereWithoutLicenseInputObjectSchema } from './DeviceUpdateManyWithWhereWithoutLicenseInput.schema';
import { DeviceScalarWhereInputObjectSchema } from './DeviceScalarWhereInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => DeviceCreateWithoutLicenseInputObjectSchema), z.lazy(() => DeviceCreateWithoutLicenseInputObjectSchema).array(), z.lazy(() => DeviceUncheckedCreateWithoutLicenseInputObjectSchema), z.lazy(() => DeviceUncheckedCreateWithoutLicenseInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => DeviceCreateOrConnectWithoutLicenseInputObjectSchema), z.lazy(() => DeviceCreateOrConnectWithoutLicenseInputObjectSchema).array()]).optional(),
  upsert: z.union([z.lazy(() => DeviceUpsertWithWhereUniqueWithoutLicenseInputObjectSchema), z.lazy(() => DeviceUpsertWithWhereUniqueWithoutLicenseInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => DeviceCreateManyLicenseInputEnvelopeObjectSchema).optional(),
  set: z.union([z.lazy(() => DeviceWhereUniqueInputObjectSchema), z.lazy(() => DeviceWhereUniqueInputObjectSchema).array()]).optional(),
  disconnect: z.union([z.lazy(() => DeviceWhereUniqueInputObjectSchema), z.lazy(() => DeviceWhereUniqueInputObjectSchema).array()]).optional(),
  delete: z.union([z.lazy(() => DeviceWhereUniqueInputObjectSchema), z.lazy(() => DeviceWhereUniqueInputObjectSchema).array()]).optional(),
  connect: z.union([z.lazy(() => DeviceWhereUniqueInputObjectSchema), z.lazy(() => DeviceWhereUniqueInputObjectSchema).array()]).optional(),
  update: z.union([z.lazy(() => DeviceUpdateWithWhereUniqueWithoutLicenseInputObjectSchema), z.lazy(() => DeviceUpdateWithWhereUniqueWithoutLicenseInputObjectSchema).array()]).optional(),
  updateMany: z.union([z.lazy(() => DeviceUpdateManyWithWhereWithoutLicenseInputObjectSchema), z.lazy(() => DeviceUpdateManyWithWhereWithoutLicenseInputObjectSchema).array()]).optional(),
  deleteMany: z.union([z.lazy(() => DeviceScalarWhereInputObjectSchema), z.lazy(() => DeviceScalarWhereInputObjectSchema).array()]).optional()
}).strict();
export const DeviceUncheckedUpdateManyWithoutLicenseNestedInputObjectSchema: z.ZodType<Prisma.DeviceUncheckedUpdateManyWithoutLicenseNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceUncheckedUpdateManyWithoutLicenseNestedInput>;
export const DeviceUncheckedUpdateManyWithoutLicenseNestedInputObjectZodSchema = makeSchema();
