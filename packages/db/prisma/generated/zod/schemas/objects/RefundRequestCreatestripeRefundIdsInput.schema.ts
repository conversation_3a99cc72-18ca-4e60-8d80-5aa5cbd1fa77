import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  set: z.string().array()
}).strict();
export const RefundRequestCreatestripeRefundIdsInputObjectSchema: z.ZodType<Prisma.RefundRequestCreatestripeRefundIdsInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestCreatestripeRefundIdsInput>;
export const RefundRequestCreatestripeRefundIdsInputObjectZodSchema = makeSchema();
