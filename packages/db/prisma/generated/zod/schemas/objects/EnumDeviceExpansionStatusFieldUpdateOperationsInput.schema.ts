import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceExpansionStatusSchema } from '../enums/DeviceExpansionStatus.schema'

const makeSchema = () => z.object({
  set: DeviceExpansionStatusSchema.optional()
}).strict();
export const EnumDeviceExpansionStatusFieldUpdateOperationsInputObjectSchema: z.ZodType<Prisma.EnumDeviceExpansionStatusFieldUpdateOperationsInput> = makeSchema() as unknown as z.ZodType<Prisma.EnumDeviceExpansionStatusFieldUpdateOperationsInput>;
export const EnumDeviceExpansionStatusFieldUpdateOperationsInputObjectZodSchema = makeSchema();
