import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageScalarWhereInputObjectSchema } from './SupportMessageScalarWhereInput.schema';
import { SupportMessageUpdateManyMutationInputObjectSchema } from './SupportMessageUpdateManyMutationInput.schema';
import { SupportMessageUncheckedUpdateManyWithoutAuthorUserInputObjectSchema } from './SupportMessageUncheckedUpdateManyWithoutAuthorUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => SupportMessageScalarWhereInputObjectSchema),
  data: z.union([z.lazy(() => SupportMessageUpdateManyMutationInputObjectSchema), z.lazy(() => SupportMessageUncheckedUpdateManyWithoutAuthorUserInputObjectSchema)])
}).strict();
export const SupportMessageUpdateManyWithWhereWithoutAuthorUserInputObjectSchema: z.ZodType<Prisma.SupportMessageUpdateManyWithWhereWithoutAuthorUserInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageUpdateManyWithWhereWithoutAuthorUserInput>;
export const SupportMessageUpdateManyWithWhereWithoutAuthorUserInputObjectZodSchema = makeSchema();
