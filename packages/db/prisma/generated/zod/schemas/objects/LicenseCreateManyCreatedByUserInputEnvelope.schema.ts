import { z } from 'zod';
import type { Prisma } from '../../../client';
import { LicenseCreateManyCreatedByUserInputObjectSchema } from './LicenseCreateManyCreatedByUserInput.schema'

const makeSchema = () => z.object({
  data: z.union([z.lazy(() => LicenseCreateManyCreatedByUserInputObjectSchema), z.lazy(() => LicenseCreateManyCreatedByUserInputObjectSchema).array()]),
  skipDuplicates: z.boolean().optional()
}).strict();
export const LicenseCreateManyCreatedByUserInputEnvelopeObjectSchema: z.ZodType<Prisma.LicenseCreateManyCreatedByUserInputEnvelope> = makeSchema() as unknown as z.ZodType<Prisma.LicenseCreateManyCreatedByUserInputEnvelope>;
export const LicenseCreateManyCreatedByUserInputEnvelopeObjectZodSchema = makeSchema();
