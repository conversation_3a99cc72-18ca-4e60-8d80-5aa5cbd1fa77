import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestWhereUniqueInputObjectSchema } from './RefundRequestWhereUniqueInput.schema';
import { RefundRequestUpdateWithoutProcessedByUserInputObjectSchema } from './RefundRequestUpdateWithoutProcessedByUserInput.schema';
import { RefundRequestUncheckedUpdateWithoutProcessedByUserInputObjectSchema } from './RefundRequestUncheckedUpdateWithoutProcessedByUserInput.schema';
import { RefundRequestCreateWithoutProcessedByUserInputObjectSchema } from './RefundRequestCreateWithoutProcessedByUserInput.schema';
import { RefundRequestUncheckedCreateWithoutProcessedByUserInputObjectSchema } from './RefundRequestUncheckedCreateWithoutProcessedByUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => RefundRequestWhereUniqueInputObjectSchema),
  update: z.union([z.lazy(() => RefundRequestUpdateWithoutProcessedByUserInputObjectSchema), z.lazy(() => RefundRequestUncheckedUpdateWithoutProcessedByUserInputObjectSchema)]),
  create: z.union([z.lazy(() => RefundRequestCreateWithoutProcessedByUserInputObjectSchema), z.lazy(() => RefundRequestUncheckedCreateWithoutProcessedByUserInputObjectSchema)])
}).strict();
export const RefundRequestUpsertWithWhereUniqueWithoutProcessedByUserInputObjectSchema: z.ZodType<Prisma.RefundRequestUpsertWithWhereUniqueWithoutProcessedByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestUpsertWithWhereUniqueWithoutProcessedByUserInput>;
export const RefundRequestUpsertWithWhereUniqueWithoutProcessedByUserInputObjectZodSchema = makeSchema();
