import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationCreateWithoutAcceptedByUserInputObjectSchema } from './UserInvitationCreateWithoutAcceptedByUserInput.schema';
import { UserInvitationUncheckedCreateWithoutAcceptedByUserInputObjectSchema } from './UserInvitationUncheckedCreateWithoutAcceptedByUserInput.schema';
import { UserInvitationCreateOrConnectWithoutAcceptedByUserInputObjectSchema } from './UserInvitationCreateOrConnectWithoutAcceptedByUserInput.schema';
import { UserInvitationCreateManyAcceptedByUserInputEnvelopeObjectSchema } from './UserInvitationCreateManyAcceptedByUserInputEnvelope.schema';
import { UserInvitationWhereUniqueInputObjectSchema } from './UserInvitationWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserInvitationCreateWithoutAcceptedByUserInputObjectSchema), z.lazy(() => UserInvitationCreateWithoutAcceptedByUserInputObjectSchema).array(), z.lazy(() => UserInvitationUncheckedCreateWithoutAcceptedByUserInputObjectSchema), z.lazy(() => UserInvitationUncheckedCreateWithoutAcceptedByUserInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => UserInvitationCreateOrConnectWithoutAcceptedByUserInputObjectSchema), z.lazy(() => UserInvitationCreateOrConnectWithoutAcceptedByUserInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => UserInvitationCreateManyAcceptedByUserInputEnvelopeObjectSchema).optional(),
  connect: z.union([z.lazy(() => UserInvitationWhereUniqueInputObjectSchema), z.lazy(() => UserInvitationWhereUniqueInputObjectSchema).array()]).optional()
}).strict();
export const UserInvitationCreateNestedManyWithoutAcceptedByUserInputObjectSchema: z.ZodType<Prisma.UserInvitationCreateNestedManyWithoutAcceptedByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationCreateNestedManyWithoutAcceptedByUserInput>;
export const UserInvitationCreateNestedManyWithoutAcceptedByUserInputObjectZodSchema = makeSchema();
