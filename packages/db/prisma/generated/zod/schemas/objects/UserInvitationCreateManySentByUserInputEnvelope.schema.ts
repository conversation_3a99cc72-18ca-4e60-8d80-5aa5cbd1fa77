import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationCreateManySentByUserInputObjectSchema } from './UserInvitationCreateManySentByUserInput.schema'

const makeSchema = () => z.object({
  data: z.union([z.lazy(() => UserInvitationCreateManySentByUserInputObjectSchema), z.lazy(() => UserInvitationCreateManySentByUserInputObjectSchema).array()]),
  skipDuplicates: z.boolean().optional()
}).strict();
export const UserInvitationCreateManySentByUserInputEnvelopeObjectSchema: z.ZodType<Prisma.UserInvitationCreateManySentByUserInputEnvelope> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationCreateManySentByUserInputEnvelope>;
export const UserInvitationCreateManySentByUserInputEnvelopeObjectZodSchema = makeSchema();
