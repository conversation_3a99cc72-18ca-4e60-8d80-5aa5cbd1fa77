import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserUpdateWithoutAuditLogsAsActorInputObjectSchema } from './UserUpdateWithoutAuditLogsAsActorInput.schema';
import { UserUncheckedUpdateWithoutAuditLogsAsActorInputObjectSchema } from './UserUncheckedUpdateWithoutAuditLogsAsActorInput.schema';
import { UserCreateWithoutAuditLogsAsActorInputObjectSchema } from './UserCreateWithoutAuditLogsAsActorInput.schema';
import { UserUncheckedCreateWithoutAuditLogsAsActorInputObjectSchema } from './UserUncheckedCreateWithoutAuditLogsAsActorInput.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema'

const makeSchema = () => z.object({
  update: z.union([z.lazy(() => UserUpdateWithoutAuditLogsAsActorInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutAuditLogsAsActorInputObjectSchema)]),
  create: z.union([z.lazy(() => UserCreateWithoutAuditLogsAsActorInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutAuditLogsAsActorInputObjectSchema)]),
  where: z.lazy(() => UserWhereInputObjectSchema).optional()
}).strict();
export const UserUpsertWithoutAuditLogsAsActorInputObjectSchema: z.ZodType<Prisma.UserUpsertWithoutAuditLogsAsActorInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpsertWithoutAuditLogsAsActorInput>;
export const UserUpsertWithoutAuditLogsAsActorInputObjectZodSchema = makeSchema();
