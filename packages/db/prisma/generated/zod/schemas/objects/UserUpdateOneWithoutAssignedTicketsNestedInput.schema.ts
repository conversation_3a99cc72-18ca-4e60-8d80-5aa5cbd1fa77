import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutAssignedTicketsInputObjectSchema } from './UserCreateWithoutAssignedTicketsInput.schema';
import { UserUncheckedCreateWithoutAssignedTicketsInputObjectSchema } from './UserUncheckedCreateWithoutAssignedTicketsInput.schema';
import { UserCreateOrConnectWithoutAssignedTicketsInputObjectSchema } from './UserCreateOrConnectWithoutAssignedTicketsInput.schema';
import { UserUpsertWithoutAssignedTicketsInputObjectSchema } from './UserUpsertWithoutAssignedTicketsInput.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserUpdateToOneWithWhereWithoutAssignedTicketsInputObjectSchema } from './UserUpdateToOneWithWhereWithoutAssignedTicketsInput.schema';
import { UserUpdateWithoutAssignedTicketsInputObjectSchema } from './UserUpdateWithoutAssignedTicketsInput.schema';
import { UserUncheckedUpdateWithoutAssignedTicketsInputObjectSchema } from './UserUncheckedUpdateWithoutAssignedTicketsInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutAssignedTicketsInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutAssignedTicketsInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutAssignedTicketsInputObjectSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutAssignedTicketsInputObjectSchema).optional(),
  disconnect: z.union([z.boolean(), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  delete: z.union([z.boolean(), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional(),
  update: z.union([z.lazy(() => UserUpdateToOneWithWhereWithoutAssignedTicketsInputObjectSchema), z.lazy(() => UserUpdateWithoutAssignedTicketsInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutAssignedTicketsInputObjectSchema)]).optional()
}).strict();
export const UserUpdateOneWithoutAssignedTicketsNestedInputObjectSchema: z.ZodType<Prisma.UserUpdateOneWithoutAssignedTicketsNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateOneWithoutAssignedTicketsNestedInput>;
export const UserUpdateOneWithoutAssignedTicketsNestedInputObjectZodSchema = makeSchema();
