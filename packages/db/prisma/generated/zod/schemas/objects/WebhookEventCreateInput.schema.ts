import { z } from 'zod';
import type { Prisma } from '../../../client';
import { PaymentIntentCreateNestedOneWithoutWebhookEventsInputObjectSchema } from './PaymentIntentCreateNestedOneWithoutWebhookEventsInput.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  stripeEventId: z.string(),
  eventType: z.string(),
  processed: z.boolean().optional(),
  processedAt: z.coerce.date().optional().nullable(),
  errorMessage: z.string().optional().nullable(),
  retryCount: z.number().int().optional(),
  createdAt: z.coerce.date().optional(),
  paymentIntent: z.lazy(() => PaymentIntentCreateNestedOneWithoutWebhookEventsInputObjectSchema).optional()
}).strict();
export const WebhookEventCreateInputObjectSchema: z.ZodType<Prisma.WebhookEventCreateInput> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventCreateInput>;
export const WebhookEventCreateInputObjectZodSchema = makeSchema();
