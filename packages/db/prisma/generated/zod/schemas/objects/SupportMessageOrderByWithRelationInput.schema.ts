import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { SupportTicketOrderByWithRelationInputObjectSchema } from './SupportTicketOrderByWithRelationInput.schema';
import { UserOrderByWithRelationInputObjectSchema } from './UserOrderByWithRelationInput.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  ticketId: SortOrderSchema.optional(),
  message: SortOrderSchema.optional(),
  isInternal: SortOrderSchema.optional(),
  authorEmail: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  authorId: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  createdAt: SortOrderSchema.optional(),
  ticket: z.lazy(() => SupportTicketOrderByWithRelationInputObjectSchema).optional(),
  authorUser: z.lazy(() => UserOrderByWithRelationInputObjectSchema).optional()
}).strict();
export const SupportMessageOrderByWithRelationInputObjectSchema: z.ZodType<Prisma.SupportMessageOrderByWithRelationInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageOrderByWithRelationInput>;
export const SupportMessageOrderByWithRelationInputObjectZodSchema = makeSchema();
