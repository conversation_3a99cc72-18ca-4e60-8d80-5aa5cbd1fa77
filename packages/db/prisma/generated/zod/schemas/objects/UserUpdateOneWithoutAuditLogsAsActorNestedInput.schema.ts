import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutAuditLogsAsActorInputObjectSchema } from './UserCreateWithoutAuditLogsAsActorInput.schema';
import { UserUncheckedCreateWithoutAuditLogsAsActorInputObjectSchema } from './UserUncheckedCreateWithoutAuditLogsAsActorInput.schema';
import { UserCreateOrConnectWithoutAuditLogsAsActorInputObjectSchema } from './UserCreateOrConnectWithoutAuditLogsAsActorInput.schema';
import { UserUpsertWithoutAuditLogsAsActorInputObjectSchema } from './UserUpsertWithoutAuditLogsAsActorInput.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserUpdateToOneWithWhereWithoutAuditLogsAsActorInputObjectSchema } from './UserUpdateToOneWithWhereWithoutAuditLogsAsActorInput.schema';
import { UserUpdateWithoutAuditLogsAsActorInputObjectSchema } from './UserUpdateWithoutAuditLogsAsActorInput.schema';
import { UserUncheckedUpdateWithoutAuditLogsAsActorInputObjectSchema } from './UserUncheckedUpdateWithoutAuditLogsAsActorInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutAuditLogsAsActorInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutAuditLogsAsActorInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutAuditLogsAsActorInputObjectSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutAuditLogsAsActorInputObjectSchema).optional(),
  disconnect: z.union([z.boolean(), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  delete: z.union([z.boolean(), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional(),
  update: z.union([z.lazy(() => UserUpdateToOneWithWhereWithoutAuditLogsAsActorInputObjectSchema), z.lazy(() => UserUpdateWithoutAuditLogsAsActorInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutAuditLogsAsActorInputObjectSchema)]).optional()
}).strict();
export const UserUpdateOneWithoutAuditLogsAsActorNestedInputObjectSchema: z.ZodType<Prisma.UserUpdateOneWithoutAuditLogsAsActorNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateOneWithoutAuditLogsAsActorNestedInput>;
export const UserUpdateOneWithoutAuditLogsAsActorNestedInputObjectZodSchema = makeSchema();
