import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserRoleSchema } from '../enums/UserRole.schema';
import { SessionCreateNestedManyWithoutUserInputObjectSchema } from './SessionCreateNestedManyWithoutUserInput.schema';
import { AccountCreateNestedManyWithoutUserInputObjectSchema } from './AccountCreateNestedManyWithoutUserInput.schema';
import { LicenseCreateNestedManyWithoutCreatedByUserInputObjectSchema } from './LicenseCreateNestedManyWithoutCreatedByUserInput.schema';
import { UserInvitationCreateNestedManyWithoutSentByUserInputObjectSchema } from './UserInvitationCreateNestedManyWithoutSentByUserInput.schema';
import { UserInvitationCreateNestedManyWithoutAcceptedByUserInputObjectSchema } from './UserInvitationCreateNestedManyWithoutAcceptedByUserInput.schema';
import { AuditLogCreateNestedManyWithoutUserInputObjectSchema } from './AuditLogCreateNestedManyWithoutUserInput.schema';
import { AuditLogCreateNestedManyWithoutTargetInputObjectSchema } from './AuditLogCreateNestedManyWithoutTargetInput.schema';
import { RefundRequestCreateNestedManyWithoutProcessedByUserInputObjectSchema } from './RefundRequestCreateNestedManyWithoutProcessedByUserInput.schema';
import { SupportTicketCreateNestedManyWithoutAssignedToUserInputObjectSchema } from './SupportTicketCreateNestedManyWithoutAssignedToUserInput.schema';
import { SupportMessageCreateNestedManyWithoutAuthorUserInputObjectSchema } from './SupportMessageCreateNestedManyWithoutAuthorUserInput.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  name: z.string(),
  email: z.string(),
  emailVerified: z.boolean().optional(),
  image: z.string().optional().nullable(),
  role: UserRoleSchema.optional(),
  isActive: z.boolean().optional(),
  invitedBy: z.string().optional().nullable(),
  invitedAt: z.coerce.date().optional().nullable(),
  lastLoginAt: z.coerce.date().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  sessions: z.lazy(() => SessionCreateNestedManyWithoutUserInputObjectSchema),
  accounts: z.lazy(() => AccountCreateNestedManyWithoutUserInputObjectSchema),
  createdLicenses: z.lazy(() => LicenseCreateNestedManyWithoutCreatedByUserInputObjectSchema),
  sentInvitations: z.lazy(() => UserInvitationCreateNestedManyWithoutSentByUserInputObjectSchema),
  receivedInvitations: z.lazy(() => UserInvitationCreateNestedManyWithoutAcceptedByUserInputObjectSchema),
  auditLogsAsActor: z.lazy(() => AuditLogCreateNestedManyWithoutUserInputObjectSchema),
  auditLogsAsTarget: z.lazy(() => AuditLogCreateNestedManyWithoutTargetInputObjectSchema),
  processedRefunds: z.lazy(() => RefundRequestCreateNestedManyWithoutProcessedByUserInputObjectSchema),
  assignedTickets: z.lazy(() => SupportTicketCreateNestedManyWithoutAssignedToUserInputObjectSchema),
  supportMessages: z.lazy(() => SupportMessageCreateNestedManyWithoutAuthorUserInputObjectSchema)
}).strict();
export const UserCreateInputObjectSchema: z.ZodType<Prisma.UserCreateInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateInput>;
export const UserCreateInputObjectZodSchema = makeSchema();
