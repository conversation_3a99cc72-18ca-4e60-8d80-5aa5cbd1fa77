import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketWhereInputObjectSchema } from './SupportTicketWhereInput.schema';
import { SupportTicketUpdateWithoutMessagesInputObjectSchema } from './SupportTicketUpdateWithoutMessagesInput.schema';
import { SupportTicketUncheckedUpdateWithoutMessagesInputObjectSchema } from './SupportTicketUncheckedUpdateWithoutMessagesInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => SupportTicketWhereInputObjectSchema).optional(),
  data: z.union([z.lazy(() => SupportTicketUpdateWithoutMessagesInputObjectSchema), z.lazy(() => SupportTicketUncheckedUpdateWithoutMessagesInputObjectSchema)])
}).strict();
export const SupportTicketUpdateToOneWithWhereWithoutMessagesInputObjectSchema: z.ZodType<Prisma.SupportTicketUpdateToOneWithWhereWithoutMessagesInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketUpdateToOneWithWhereWithoutMessagesInput>;
export const SupportTicketUpdateToOneWithWhereWithoutMessagesInputObjectZodSchema = makeSchema();
