import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateNestedOneWithoutSupportMessagesInputObjectSchema } from './UserCreateNestedOneWithoutSupportMessagesInput.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  message: z.string(),
  isInternal: z.boolean().optional(),
  authorEmail: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  authorUser: z.lazy(() => UserCreateNestedOneWithoutSupportMessagesInputObjectSchema).optional()
}).strict();
export const SupportMessageCreateWithoutTicketInputObjectSchema: z.ZodType<Prisma.SupportMessageCreateWithoutTicketInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageCreateWithoutTicketInput>;
export const SupportMessageCreateWithoutTicketInputObjectZodSchema = makeSchema();
