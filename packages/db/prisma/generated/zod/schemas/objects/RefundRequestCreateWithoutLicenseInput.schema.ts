import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundStatusSchema } from '../enums/RefundStatus.schema';
import { RefundRequestCreatestripeRefundIdsInputObjectSchema } from './RefundRequestCreatestripeRefundIdsInput.schema';
import { UserCreateNestedOneWithoutProcessedRefundsInputObjectSchema } from './UserCreateNestedOneWithoutProcessedRefundsInput.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  requestedBy: z.string(),
  reason: z.string(),
  status: RefundStatusSchema.optional(),
  requestedAmount: z.number().int().optional().nullable(),
  approvedAmount: z.number().int().optional().nullable(),
  stripeRefundIds: z.union([z.lazy(() => RefundRequestCreatestripeRefundIdsInputObjectSchema), z.string().array()]).optional(),
  adminNotes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  processedAt: z.coerce.date().optional().nullable(),
  processedByUser: z.lazy(() => UserCreateNestedOneWithoutProcessedRefundsInputObjectSchema).optional()
}).strict();
export const RefundRequestCreateWithoutLicenseInputObjectSchema: z.ZodType<Prisma.RefundRequestCreateWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestCreateWithoutLicenseInput>;
export const RefundRequestCreateWithoutLicenseInputObjectZodSchema = makeSchema();
