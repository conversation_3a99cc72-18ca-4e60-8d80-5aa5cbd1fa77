import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationCreateWithoutAcceptedByUserInputObjectSchema } from './UserInvitationCreateWithoutAcceptedByUserInput.schema';
import { UserInvitationUncheckedCreateWithoutAcceptedByUserInputObjectSchema } from './UserInvitationUncheckedCreateWithoutAcceptedByUserInput.schema';
import { UserInvitationCreateOrConnectWithoutAcceptedByUserInputObjectSchema } from './UserInvitationCreateOrConnectWithoutAcceptedByUserInput.schema';
import { UserInvitationUpsertWithWhereUniqueWithoutAcceptedByUserInputObjectSchema } from './UserInvitationUpsertWithWhereUniqueWithoutAcceptedByUserInput.schema';
import { UserInvitationCreateManyAcceptedByUserInputEnvelopeObjectSchema } from './UserInvitationCreateManyAcceptedByUserInputEnvelope.schema';
import { UserInvitationWhereUniqueInputObjectSchema } from './UserInvitationWhereUniqueInput.schema';
import { UserInvitationUpdateWithWhereUniqueWithoutAcceptedByUserInputObjectSchema } from './UserInvitationUpdateWithWhereUniqueWithoutAcceptedByUserInput.schema';
import { UserInvitationUpdateManyWithWhereWithoutAcceptedByUserInputObjectSchema } from './UserInvitationUpdateManyWithWhereWithoutAcceptedByUserInput.schema';
import { UserInvitationScalarWhereInputObjectSchema } from './UserInvitationScalarWhereInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserInvitationCreateWithoutAcceptedByUserInputObjectSchema), z.lazy(() => UserInvitationCreateWithoutAcceptedByUserInputObjectSchema).array(), z.lazy(() => UserInvitationUncheckedCreateWithoutAcceptedByUserInputObjectSchema), z.lazy(() => UserInvitationUncheckedCreateWithoutAcceptedByUserInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => UserInvitationCreateOrConnectWithoutAcceptedByUserInputObjectSchema), z.lazy(() => UserInvitationCreateOrConnectWithoutAcceptedByUserInputObjectSchema).array()]).optional(),
  upsert: z.union([z.lazy(() => UserInvitationUpsertWithWhereUniqueWithoutAcceptedByUserInputObjectSchema), z.lazy(() => UserInvitationUpsertWithWhereUniqueWithoutAcceptedByUserInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => UserInvitationCreateManyAcceptedByUserInputEnvelopeObjectSchema).optional(),
  set: z.union([z.lazy(() => UserInvitationWhereUniqueInputObjectSchema), z.lazy(() => UserInvitationWhereUniqueInputObjectSchema).array()]).optional(),
  disconnect: z.union([z.lazy(() => UserInvitationWhereUniqueInputObjectSchema), z.lazy(() => UserInvitationWhereUniqueInputObjectSchema).array()]).optional(),
  delete: z.union([z.lazy(() => UserInvitationWhereUniqueInputObjectSchema), z.lazy(() => UserInvitationWhereUniqueInputObjectSchema).array()]).optional(),
  connect: z.union([z.lazy(() => UserInvitationWhereUniqueInputObjectSchema), z.lazy(() => UserInvitationWhereUniqueInputObjectSchema).array()]).optional(),
  update: z.union([z.lazy(() => UserInvitationUpdateWithWhereUniqueWithoutAcceptedByUserInputObjectSchema), z.lazy(() => UserInvitationUpdateWithWhereUniqueWithoutAcceptedByUserInputObjectSchema).array()]).optional(),
  updateMany: z.union([z.lazy(() => UserInvitationUpdateManyWithWhereWithoutAcceptedByUserInputObjectSchema), z.lazy(() => UserInvitationUpdateManyWithWhereWithoutAcceptedByUserInputObjectSchema).array()]).optional(),
  deleteMany: z.union([z.lazy(() => UserInvitationScalarWhereInputObjectSchema), z.lazy(() => UserInvitationScalarWhereInputObjectSchema).array()]).optional()
}).strict();
export const UserInvitationUpdateManyWithoutAcceptedByUserNestedInputObjectSchema: z.ZodType<Prisma.UserInvitationUpdateManyWithoutAcceptedByUserNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationUpdateManyWithoutAcceptedByUserNestedInput>;
export const UserInvitationUpdateManyWithoutAcceptedByUserNestedInputObjectZodSchema = makeSchema();
