import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserRoleSchema } from '../enums/UserRole.schema';
import { InvitationStatusSchema } from '../enums/InvitationStatus.schema';
import { UserCreateNestedOneWithoutSentInvitationsInputObjectSchema } from './UserCreateNestedOneWithoutSentInvitationsInput.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  email: z.string(),
  role: UserRoleSchema,
  token: z.string(),
  status: InvitationStatusSchema.optional(),
  expiresAt: z.coerce.date(),
  sentAt: z.coerce.date().optional(),
  acceptedAt: z.coerce.date().optional().nullable(),
  sentByUser: z.lazy(() => UserCreateNestedOneWithoutSentInvitationsInputObjectSchema)
}).strict();
export const UserInvitationCreateWithoutAcceptedByUserInputObjectSchema: z.ZodType<Prisma.UserInvitationCreateWithoutAcceptedByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationCreateWithoutAcceptedByUserInput>;
export const UserInvitationCreateWithoutAcceptedByUserInputObjectZodSchema = makeSchema();
