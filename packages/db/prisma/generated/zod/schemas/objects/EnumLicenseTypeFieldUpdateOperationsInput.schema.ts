import { z } from 'zod';
import type { Prisma } from '../../../client';
import { LicenseTypeSchema } from '../enums/LicenseType.schema'

const makeSchema = () => z.object({
  set: LicenseTypeSchema.optional()
}).strict();
export const EnumLicenseTypeFieldUpdateOperationsInputObjectSchema: z.ZodType<Prisma.EnumLicenseTypeFieldUpdateOperationsInput> = makeSchema() as unknown as z.ZodType<Prisma.EnumLicenseTypeFieldUpdateOperationsInput>;
export const EnumLicenseTypeFieldUpdateOperationsInputObjectZodSchema = makeSchema();
