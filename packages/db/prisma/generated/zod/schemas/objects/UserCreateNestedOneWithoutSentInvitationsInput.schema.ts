import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutSentInvitationsInputObjectSchema } from './UserCreateWithoutSentInvitationsInput.schema';
import { UserUncheckedCreateWithoutSentInvitationsInputObjectSchema } from './UserUncheckedCreateWithoutSentInvitationsInput.schema';
import { UserCreateOrConnectWithoutSentInvitationsInputObjectSchema } from './UserCreateOrConnectWithoutSentInvitationsInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutSentInvitationsInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutSentInvitationsInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutSentInvitationsInputObjectSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional()
}).strict();
export const UserCreateNestedOneWithoutSentInvitationsInputObjectSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutSentInvitationsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateNestedOneWithoutSentInvitationsInput>;
export const UserCreateNestedOneWithoutSentInvitationsInputObjectZodSchema = makeSchema();
