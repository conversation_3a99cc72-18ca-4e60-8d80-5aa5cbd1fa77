import { z } from 'zod';
import type { Prisma } from '../../../client';
import { PaymentIntentArgsObjectSchema } from './PaymentIntentArgs.schema'

const makeSchema = () => z.object({
  id: z.boolean().optional(),
  stripeEventId: z.boolean().optional(),
  eventType: z.boolean().optional(),
  processed: z.boolean().optional(),
  processedAt: z.boolean().optional(),
  errorMessage: z.boolean().optional(),
  retryCount: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  paymentIntentId: z.boolean().optional(),
  paymentIntent: z.union([z.boolean(), z.lazy(() => PaymentIntentArgsObjectSchema)]).optional()
}).strict();
export const WebhookEventSelectObjectSchema: z.ZodType<Prisma.WebhookEventSelect> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventSelect>;
export const WebhookEventSelectObjectZodSchema = makeSchema();
