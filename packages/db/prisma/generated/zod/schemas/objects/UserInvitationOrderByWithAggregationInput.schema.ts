import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { UserInvitationCountOrderByAggregateInputObjectSchema } from './UserInvitationCountOrderByAggregateInput.schema';
import { UserInvitationMaxOrderByAggregateInputObjectSchema } from './UserInvitationMaxOrderByAggregateInput.schema';
import { UserInvitationMinOrderByAggregateInputObjectSchema } from './UserInvitationMinOrderByAggregateInput.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  email: SortOrderSchema.optional(),
  role: SortOrderSchema.optional(),
  token: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  expiresAt: SortOrderSchema.optional(),
  sentAt: SortOrderSchema.optional(),
  acceptedAt: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  sentBy: SortOrderSchema.optional(),
  acceptedBy: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  _count: z.lazy(() => UserInvitationCountOrderByAggregateInputObjectSchema).optional(),
  _max: z.lazy(() => UserInvitationMaxOrderByAggregateInputObjectSchema).optional(),
  _min: z.lazy(() => UserInvitationMinOrderByAggregateInputObjectSchema).optional()
}).strict();
export const UserInvitationOrderByWithAggregationInputObjectSchema: z.ZodType<Prisma.UserInvitationOrderByWithAggregationInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationOrderByWithAggregationInput>;
export const UserInvitationOrderByWithAggregationInputObjectZodSchema = makeSchema();
