import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  requestedAmount: SortOrderSchema.optional(),
  approvedAmount: SortOrderSchema.optional()
}).strict();
export const RefundRequestAvgOrderByAggregateInputObjectSchema: z.ZodType<Prisma.RefundRequestAvgOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestAvgOrderByAggregateInput>;
export const RefundRequestAvgOrderByAggregateInputObjectZodSchema = makeSchema();
