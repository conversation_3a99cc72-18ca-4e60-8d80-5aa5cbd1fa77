import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserArgsObjectSchema } from './UserArgs.schema';
import { LicenseArgsObjectSchema } from './LicenseArgs.schema'

const makeSchema = () => z.object({
  id: z.boolean().optional(),
  licenseId: z.boolean().optional(),
  requestedBy: z.boolean().optional(),
  reason: z.boolean().optional(),
  status: z.boolean().optional(),
  requestedAmount: z.boolean().optional(),
  approvedAmount: z.boolean().optional(),
  stripeRefundIds: z.boolean().optional(),
  adminNotes: z.boolean().optional(),
  processedBy: z.boolean().optional(),
  processedByUser: z.union([z.boolean(), z.lazy(() => UserArgsObjectSchema)]).optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  processedAt: z.boolean().optional(),
  license: z.union([z.boolean(), z.lazy(() => LicenseArgsObjectSchema)]).optional()
}).strict();
export const RefundRequestSelectObjectSchema: z.ZodType<Prisma.RefundRequestSelect> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestSelect>;
export const RefundRequestSelectObjectZodSchema = makeSchema();
