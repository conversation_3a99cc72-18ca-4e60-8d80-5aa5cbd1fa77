import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageCreateManyAuthorUserInputObjectSchema } from './SupportMessageCreateManyAuthorUserInput.schema'

const makeSchema = () => z.object({
  data: z.union([z.lazy(() => SupportMessageCreateManyAuthorUserInputObjectSchema), z.lazy(() => SupportMessageCreateManyAuthorUserInputObjectSchema).array()]),
  skipDuplicates: z.boolean().optional()
}).strict();
export const SupportMessageCreateManyAuthorUserInputEnvelopeObjectSchema: z.ZodType<Prisma.SupportMessageCreateManyAuthorUserInputEnvelope> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageCreateManyAuthorUserInputEnvelope>;
export const SupportMessageCreateManyAuthorUserInputEnvelopeObjectZodSchema = makeSchema();
