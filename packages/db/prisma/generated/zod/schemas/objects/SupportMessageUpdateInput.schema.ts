import { z } from 'zod';
import type { <PERSON>risma } from '../../../client';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { BoolFieldUpdateOperationsInputObjectSchema } from './BoolFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { SupportTicketUpdateOneRequiredWithoutMessagesNestedInputObjectSchema } from './SupportTicketUpdateOneRequiredWithoutMessagesNestedInput.schema';
import { UserUpdateOneWithoutSupportMessagesNestedInputObjectSchema } from './UserUpdateOneWithoutSupportMessagesNestedInput.schema'

const makeSchema = () => z.object({
  message: z.union([z.string(), z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(),
  isInternal: z.union([z.boolean(), z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(),
  authorEmail: z.union([z.string(), z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  ticket: z.lazy(() => SupportTicketUpdateOneRequiredWithoutMessagesNestedInputObjectSchema).optional(),
  authorUser: z.lazy(() => UserUpdateOneWithoutSupportMessagesNestedInputObjectSchema).optional()
}).strict();
export const SupportMessageUpdateInputObjectSchema: z.ZodType<Prisma.SupportMessageUpdateInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageUpdateInput>;
export const SupportMessageUpdateInputObjectZodSchema = makeSchema();
