import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  additionalDevices: SortOrderSchema.optional(),
  amount: SortOrderSchema.optional()
}).strict();
export const DeviceExpansionSumOrderByAggregateInputObjectSchema: z.ZodType<Prisma.DeviceExpansionSumOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionSumOrderByAggregateInput>;
export const DeviceExpansionSumOrderByAggregateInputObjectZodSchema = makeSchema();
