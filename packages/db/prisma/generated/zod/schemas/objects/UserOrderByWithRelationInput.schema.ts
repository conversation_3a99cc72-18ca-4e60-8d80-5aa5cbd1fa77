import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { SessionOrderByRelationAggregateInputObjectSchema } from './SessionOrderByRelationAggregateInput.schema';
import { AccountOrderByRelationAggregateInputObjectSchema } from './AccountOrderByRelationAggregateInput.schema';
import { LicenseOrderByRelationAggregateInputObjectSchema } from './LicenseOrderByRelationAggregateInput.schema';
import { UserInvitationOrderByRelationAggregateInputObjectSchema } from './UserInvitationOrderByRelationAggregateInput.schema';
import { AuditLogOrderByRelationAggregateInputObjectSchema } from './AuditLogOrderByRelationAggregateInput.schema';
import { RefundRequestOrderByRelationAggregateInputObjectSchema } from './RefundRequestOrderByRelationAggregateInput.schema';
import { SupportTicketOrderByRelationAggregateInputObjectSchema } from './SupportTicketOrderByRelationAggregateInput.schema';
import { SupportMessageOrderByRelationAggregateInputObjectSchema } from './SupportMessageOrderByRelationAggregateInput.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  name: SortOrderSchema.optional(),
  email: SortOrderSchema.optional(),
  emailVerified: SortOrderSchema.optional(),
  image: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  role: SortOrderSchema.optional(),
  isActive: SortOrderSchema.optional(),
  invitedBy: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  invitedAt: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  lastLoginAt: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  createdAt: SortOrderSchema.optional(),
  updatedAt: SortOrderSchema.optional(),
  sessions: z.lazy(() => SessionOrderByRelationAggregateInputObjectSchema).optional(),
  accounts: z.lazy(() => AccountOrderByRelationAggregateInputObjectSchema).optional(),
  createdLicenses: z.lazy(() => LicenseOrderByRelationAggregateInputObjectSchema).optional(),
  sentInvitations: z.lazy(() => UserInvitationOrderByRelationAggregateInputObjectSchema).optional(),
  receivedInvitations: z.lazy(() => UserInvitationOrderByRelationAggregateInputObjectSchema).optional(),
  auditLogsAsActor: z.lazy(() => AuditLogOrderByRelationAggregateInputObjectSchema).optional(),
  auditLogsAsTarget: z.lazy(() => AuditLogOrderByRelationAggregateInputObjectSchema).optional(),
  processedRefunds: z.lazy(() => RefundRequestOrderByRelationAggregateInputObjectSchema).optional(),
  assignedTickets: z.lazy(() => SupportTicketOrderByRelationAggregateInputObjectSchema).optional(),
  supportMessages: z.lazy(() => SupportMessageOrderByRelationAggregateInputObjectSchema).optional()
}).strict();
export const UserOrderByWithRelationInputObjectSchema: z.ZodType<Prisma.UserOrderByWithRelationInput> = makeSchema() as unknown as z.ZodType<Prisma.UserOrderByWithRelationInput>;
export const UserOrderByWithRelationInputObjectZodSchema = makeSchema();
