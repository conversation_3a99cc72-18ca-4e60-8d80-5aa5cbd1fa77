import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageWhereUniqueInputObjectSchema } from './SupportMessageWhereUniqueInput.schema';
import { SupportMessageUpdateWithoutAuthorUserInputObjectSchema } from './SupportMessageUpdateWithoutAuthorUserInput.schema';
import { SupportMessageUncheckedUpdateWithoutAuthorUserInputObjectSchema } from './SupportMessageUncheckedUpdateWithoutAuthorUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => SupportMessageWhereUniqueInputObjectSchema),
  data: z.union([z.lazy(() => SupportMessageUpdateWithoutAuthorUserInputObjectSchema), z.lazy(() => SupportMessageUncheckedUpdateWithoutAuthorUserInputObjectSchema)])
}).strict();
export const SupportMessageUpdateWithWhereUniqueWithoutAuthorUserInputObjectSchema: z.ZodType<Prisma.SupportMessageUpdateWithWhereUniqueWithoutAuthorUserInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageUpdateWithWhereUniqueWithoutAuthorUserInput>;
export const SupportMessageUpdateWithWhereUniqueWithoutAuthorUserInputObjectZodSchema = makeSchema();
