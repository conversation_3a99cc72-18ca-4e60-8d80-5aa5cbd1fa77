import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserCreateWithoutSentInvitationsInputObjectSchema } from './UserCreateWithoutSentInvitationsInput.schema';
import { UserUncheckedCreateWithoutSentInvitationsInputObjectSchema } from './UserUncheckedCreateWithoutSentInvitationsInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => UserCreateWithoutSentInvitationsInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutSentInvitationsInputObjectSchema)])
}).strict();
export const UserCreateOrConnectWithoutSentInvitationsInputObjectSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutSentInvitationsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateOrConnectWithoutSentInvitationsInput>;
export const UserCreateOrConnectWithoutSentInvitationsInputObjectZodSchema = makeSchema();
