import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceStatusSchema } from '../enums/DeviceStatus.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  licenseId: z.string(),
  deviceHash: z.string(),
  salt: z.string(),
  status: DeviceStatusSchema.optional(),
  firstSeen: z.coerce.date().optional(),
  lastSeen: z.coerce.date().optional(),
  removedAt: z.coerce.date().optional().nullable(),
  appVersion: z.string().optional().nullable(),
  deviceName: z.string().optional().nullable(),
  deviceType: z.string().optional().nullable(),
  deviceModel: z.string().optional().nullable(),
  operatingSystem: z.string().optional().nullable(),
  architecture: z.string().optional().nullable(),
  screenResolution: z.string().optional().nullable(),
  totalMemory: z.string().optional().nullable(),
  userNickname: z.string().optional().nullable(),
  location: z.string().optional().nullable(),
  notes: z.string().optional().nullable()
}).strict();
export const DeviceUncheckedCreateInputObjectSchema: z.ZodType<Prisma.DeviceUncheckedCreateInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceUncheckedCreateInput>;
export const DeviceUncheckedCreateInputObjectZodSchema = makeSchema();
