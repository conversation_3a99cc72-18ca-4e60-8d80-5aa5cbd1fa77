import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationCreateWithoutSentByUserInputObjectSchema } from './UserInvitationCreateWithoutSentByUserInput.schema';
import { UserInvitationUncheckedCreateWithoutSentByUserInputObjectSchema } from './UserInvitationUncheckedCreateWithoutSentByUserInput.schema';
import { UserInvitationCreateOrConnectWithoutSentByUserInputObjectSchema } from './UserInvitationCreateOrConnectWithoutSentByUserInput.schema';
import { UserInvitationCreateManySentByUserInputEnvelopeObjectSchema } from './UserInvitationCreateManySentByUserInputEnvelope.schema';
import { UserInvitationWhereUniqueInputObjectSchema } from './UserInvitationWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserInvitationCreateWithoutSentByUserInputObjectSchema), z.lazy(() => UserInvitationCreateWithoutSentByUserInputObjectSchema).array(), z.lazy(() => UserInvitationUncheckedCreateWithoutSentByUserInputObjectSchema), z.lazy(() => UserInvitationUncheckedCreateWithoutSentByUserInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => UserInvitationCreateOrConnectWithoutSentByUserInputObjectSchema), z.lazy(() => UserInvitationCreateOrConnectWithoutSentByUserInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => UserInvitationCreateManySentByUserInputEnvelopeObjectSchema).optional(),
  connect: z.union([z.lazy(() => UserInvitationWhereUniqueInputObjectSchema), z.lazy(() => UserInvitationWhereUniqueInputObjectSchema).array()]).optional()
}).strict();
export const UserInvitationCreateNestedManyWithoutSentByUserInputObjectSchema: z.ZodType<Prisma.UserInvitationCreateNestedManyWithoutSentByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationCreateNestedManyWithoutSentByUserInput>;
export const UserInvitationCreateNestedManyWithoutSentByUserInputObjectZodSchema = makeSchema();
