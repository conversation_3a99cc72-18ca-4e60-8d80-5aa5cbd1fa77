import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  ticketId: SortOrderSchema.optional(),
  message: SortOrderSchema.optional(),
  isInternal: SortOrderSchema.optional(),
  authorEmail: SortOrderSchema.optional(),
  authorId: SortOrderSchema.optional(),
  createdAt: SortOrderSchema.optional()
}).strict();
export const SupportMessageMaxOrderByAggregateInputObjectSchema: z.ZodType<Prisma.SupportMessageMaxOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageMaxOrderByAggregateInput>;
export const SupportMessageMaxOrderByAggregateInputObjectZodSchema = makeSchema();
