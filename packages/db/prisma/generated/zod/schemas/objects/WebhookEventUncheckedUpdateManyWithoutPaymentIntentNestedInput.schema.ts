import { z } from 'zod';
import type { Prisma } from '../../../client';
import { WebhookEventCreateWithoutPaymentIntentInputObjectSchema } from './WebhookEventCreateWithoutPaymentIntentInput.schema';
import { WebhookEventUncheckedCreateWithoutPaymentIntentInputObjectSchema } from './WebhookEventUncheckedCreateWithoutPaymentIntentInput.schema';
import { WebhookEventCreateOrConnectWithoutPaymentIntentInputObjectSchema } from './WebhookEventCreateOrConnectWithoutPaymentIntentInput.schema';
import { WebhookEventUpsertWithWhereUniqueWithoutPaymentIntentInputObjectSchema } from './WebhookEventUpsertWithWhereUniqueWithoutPaymentIntentInput.schema';
import { WebhookEventCreateManyPaymentIntentInputEnvelopeObjectSchema } from './WebhookEventCreateManyPaymentIntentInputEnvelope.schema';
import { WebhookEventWhereUniqueInputObjectSchema } from './WebhookEventWhereUniqueInput.schema';
import { WebhookEventUpdateWithWhereUniqueWithoutPaymentIntentInputObjectSchema } from './WebhookEventUpdateWithWhereUniqueWithoutPaymentIntentInput.schema';
import { WebhookEventUpdateManyWithWhereWithoutPaymentIntentInputObjectSchema } from './WebhookEventUpdateManyWithWhereWithoutPaymentIntentInput.schema';
import { WebhookEventScalarWhereInputObjectSchema } from './WebhookEventScalarWhereInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => WebhookEventCreateWithoutPaymentIntentInputObjectSchema), z.lazy(() => WebhookEventCreateWithoutPaymentIntentInputObjectSchema).array(), z.lazy(() => WebhookEventUncheckedCreateWithoutPaymentIntentInputObjectSchema), z.lazy(() => WebhookEventUncheckedCreateWithoutPaymentIntentInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => WebhookEventCreateOrConnectWithoutPaymentIntentInputObjectSchema), z.lazy(() => WebhookEventCreateOrConnectWithoutPaymentIntentInputObjectSchema).array()]).optional(),
  upsert: z.union([z.lazy(() => WebhookEventUpsertWithWhereUniqueWithoutPaymentIntentInputObjectSchema), z.lazy(() => WebhookEventUpsertWithWhereUniqueWithoutPaymentIntentInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => WebhookEventCreateManyPaymentIntentInputEnvelopeObjectSchema).optional(),
  set: z.union([z.lazy(() => WebhookEventWhereUniqueInputObjectSchema), z.lazy(() => WebhookEventWhereUniqueInputObjectSchema).array()]).optional(),
  disconnect: z.union([z.lazy(() => WebhookEventWhereUniqueInputObjectSchema), z.lazy(() => WebhookEventWhereUniqueInputObjectSchema).array()]).optional(),
  delete: z.union([z.lazy(() => WebhookEventWhereUniqueInputObjectSchema), z.lazy(() => WebhookEventWhereUniqueInputObjectSchema).array()]).optional(),
  connect: z.union([z.lazy(() => WebhookEventWhereUniqueInputObjectSchema), z.lazy(() => WebhookEventWhereUniqueInputObjectSchema).array()]).optional(),
  update: z.union([z.lazy(() => WebhookEventUpdateWithWhereUniqueWithoutPaymentIntentInputObjectSchema), z.lazy(() => WebhookEventUpdateWithWhereUniqueWithoutPaymentIntentInputObjectSchema).array()]).optional(),
  updateMany: z.union([z.lazy(() => WebhookEventUpdateManyWithWhereWithoutPaymentIntentInputObjectSchema), z.lazy(() => WebhookEventUpdateManyWithWhereWithoutPaymentIntentInputObjectSchema).array()]).optional(),
  deleteMany: z.union([z.lazy(() => WebhookEventScalarWhereInputObjectSchema), z.lazy(() => WebhookEventScalarWhereInputObjectSchema).array()]).optional()
}).strict();
export const WebhookEventUncheckedUpdateManyWithoutPaymentIntentNestedInputObjectSchema: z.ZodType<Prisma.WebhookEventUncheckedUpdateManyWithoutPaymentIntentNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventUncheckedUpdateManyWithoutPaymentIntentNestedInput>;
export const WebhookEventUncheckedUpdateManyWithoutPaymentIntentNestedInputObjectZodSchema = makeSchema();
