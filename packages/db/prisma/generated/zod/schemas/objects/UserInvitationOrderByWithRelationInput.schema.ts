import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { UserOrderByWithRelationInputObjectSchema } from './UserOrderByWithRelationInput.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  email: SortOrderSchema.optional(),
  role: SortOrderSchema.optional(),
  token: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  expiresAt: SortOrderSchema.optional(),
  sentAt: SortOrderSchema.optional(),
  acceptedAt: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  sentBy: SortOrderSchema.optional(),
  acceptedBy: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  sentByUser: z.lazy(() => UserOrderByWithRelationInputObjectSchema).optional(),
  acceptedByUser: z.lazy(() => UserOrderByWithRelationInputObjectSchema).optional()
}).strict();
export const UserInvitationOrderByWithRelationInputObjectSchema: z.ZodType<Prisma.UserInvitationOrderByWithRelationInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationOrderByWithRelationInput>;
export const UserInvitationOrderByWithRelationInputObjectZodSchema = makeSchema();
