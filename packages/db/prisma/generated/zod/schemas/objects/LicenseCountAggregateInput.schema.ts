import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.literal(true).optional(),
  licenseKey: z.literal(true).optional(),
  licenseType: z.literal(true).optional(),
  status: z.literal(true).optional(),
  maxDevices: z.literal(true).optional(),
  usedDevices: z.literal(true).optional(),
  createdAt: z.literal(true).optional(),
  updatedAt: z.literal(true).optional(),
  expiresAt: z.literal(true).optional(),
  activatedAt: z.literal(true).optional(),
  customerEmail: z.literal(true).optional(),
  customerName: z.literal(true).optional(),
  createdBy: z.literal(true).optional(),
  paymentIntentId: z.literal(true).optional(),
  totalPaidAmount: z.literal(true).optional(),
  refundedAt: z.literal(true).optional(),
  refundReason: z.literal(true).optional(),
  refundAmount: z.literal(true).optional(),
  emailSentAt: z.literal(true).optional(),
  emailDeliveryStatus: z.literal(true).optional(),
  _all: z.literal(true).optional()
}).strict();
export const LicenseCountAggregateInputObjectSchema: z.ZodType<Prisma.LicenseCountAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.LicenseCountAggregateInputType>;
export const LicenseCountAggregateInputObjectZodSchema = makeSchema();
