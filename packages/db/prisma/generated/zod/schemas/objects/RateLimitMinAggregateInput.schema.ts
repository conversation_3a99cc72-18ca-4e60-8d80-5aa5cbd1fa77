import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.literal(true).optional(),
  identifier: z.literal(true).optional(),
  action: z.literal(true).optional(),
  count: z.literal(true).optional(),
  windowStart: z.literal(true).optional(),
  expiresAt: z.literal(true).optional()
}).strict();
export const RateLimitMinAggregateInputObjectSchema: z.ZodType<Prisma.RateLimitMinAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.RateLimitMinAggregateInputType>;
export const RateLimitMinAggregateInputObjectZodSchema = makeSchema();
