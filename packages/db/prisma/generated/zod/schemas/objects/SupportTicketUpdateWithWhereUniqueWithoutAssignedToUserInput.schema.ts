import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketWhereUniqueInputObjectSchema } from './SupportTicketWhereUniqueInput.schema';
import { SupportTicketUpdateWithoutAssignedToUserInputObjectSchema } from './SupportTicketUpdateWithoutAssignedToUserInput.schema';
import { SupportTicketUncheckedUpdateWithoutAssignedToUserInputObjectSchema } from './SupportTicketUncheckedUpdateWithoutAssignedToUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => SupportTicketWhereUniqueInputObjectSchema),
  data: z.union([z.lazy(() => SupportTicketUpdateWithoutAssignedToUserInputObjectSchema), z.lazy(() => SupportTicketUncheckedUpdateWithoutAssignedToUserInputObjectSchema)])
}).strict();
export const SupportTicketUpdateWithWhereUniqueWithoutAssignedToUserInputObjectSchema: z.ZodType<Prisma.SupportTicketUpdateWithWhereUniqueWithoutAssignedToUserInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketUpdateWithWhereUniqueWithoutAssignedToUserInput>;
export const SupportTicketUpdateWithWhereUniqueWithoutAssignedToUserInputObjectZodSchema = makeSchema();
