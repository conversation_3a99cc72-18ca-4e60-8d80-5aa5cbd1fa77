import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringWithAggregatesFilterObjectSchema } from './StringWithAggregatesFilter.schema';
import { EnumRefundStatusWithAggregatesFilterObjectSchema } from './EnumRefundStatusWithAggregatesFilter.schema';
import { RefundStatusSchema } from '../enums/RefundStatus.schema';
import { IntNullableWithAggregatesFilterObjectSchema } from './IntNullableWithAggregatesFilter.schema';
import { StringNullableListFilterObjectSchema } from './StringNullableListFilter.schema';
import { StringNullableWithAggregatesFilterObjectSchema } from './StringNullableWithAggregatesFilter.schema';
import { DateTimeNullableWithAggregatesFilterObjectSchema } from './DateTimeNullableWithAggregatesFilter.schema'

const refundrequestscalarwherewithaggregatesinputSchema = z.object({
  AND: z.union([z.lazy(() => RefundRequestScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => RefundRequestScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => RefundRequestScalarWhereWithAggregatesInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => RefundRequestScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => RefundRequestScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  licenseId: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  requestedBy: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  reason: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  status: z.union([z.lazy(() => EnumRefundStatusWithAggregatesFilterObjectSchema), RefundStatusSchema]).optional(),
  requestedAmount: z.union([z.lazy(() => IntNullableWithAggregatesFilterObjectSchema), z.number().int()]).optional().nullable(),
  approvedAmount: z.union([z.lazy(() => IntNullableWithAggregatesFilterObjectSchema), z.number().int()]).optional().nullable(),
  stripeRefundIds: z.lazy(() => StringNullableListFilterObjectSchema).optional(),
  adminNotes: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  processedBy: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  processedAt: z.union([z.lazy(() => DateTimeNullableWithAggregatesFilterObjectSchema), z.coerce.date()]).optional().nullable()
}).strict();
export const RefundRequestScalarWhereWithAggregatesInputObjectSchema: z.ZodType<Prisma.RefundRequestScalarWhereWithAggregatesInput> = refundrequestscalarwherewithaggregatesinputSchema as unknown as z.ZodType<Prisma.RefundRequestScalarWhereWithAggregatesInput>;
export const RefundRequestScalarWhereWithAggregatesInputObjectZodSchema = refundrequestscalarwherewithaggregatesinputSchema;
