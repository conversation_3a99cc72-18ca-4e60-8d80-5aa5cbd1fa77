import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  identifier: SortOrderSchema.optional(),
  action: SortOrderSchema.optional(),
  count: SortOrderSchema.optional(),
  windowStart: SortOrderSchema.optional(),
  expiresAt: SortOrderSchema.optional()
}).strict();
export const RateLimitMaxOrderByAggregateInputObjectSchema: z.ZodType<Prisma.RateLimitMaxOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.RateLimitMaxOrderByAggregateInput>;
export const RateLimitMaxOrderByAggregateInputObjectZodSchema = makeSchema();
