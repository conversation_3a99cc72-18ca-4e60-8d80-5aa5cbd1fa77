import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  maxDevices: z.literal(true).optional(),
  usedDevices: z.literal(true).optional(),
  totalPaidAmount: z.literal(true).optional(),
  refundAmount: z.literal(true).optional()
}).strict();
export const LicenseAvgAggregateInputObjectSchema: z.ZodType<Prisma.LicenseAvgAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.LicenseAvgAggregateInputType>;
export const LicenseAvgAggregateInputObjectZodSchema = makeSchema();
