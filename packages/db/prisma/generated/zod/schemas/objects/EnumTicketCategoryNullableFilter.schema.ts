import { z } from 'zod';
import type { Prisma } from '../../../client';
import { TicketCategorySchema } from '../enums/TicketCategory.schema';
import { NestedEnumTicketCategoryNullableFilterObjectSchema } from './NestedEnumTicketCategoryNullableFilter.schema'

const makeSchema = () => z.object({
  equals: TicketCategorySchema.optional().nullable(),
  in: TicketCategorySchema.array().optional().nullable(),
  notIn: TicketCategorySchema.array().optional().nullable(),
  not: z.union([TicketCategorySchema, z.lazy(() => NestedEnumTicketCategoryNullableFilterObjectSchema)]).optional().nullable()
}).strict();
export const EnumTicketCategoryNullableFilterObjectSchema: z.ZodType<Prisma.EnumTicketCategoryNullableFilter> = makeSchema() as unknown as z.ZodType<Prisma.EnumTicketCategoryNullableFilter>;
export const EnumTicketCategoryNullableFilterObjectZodSchema = makeSchema();
