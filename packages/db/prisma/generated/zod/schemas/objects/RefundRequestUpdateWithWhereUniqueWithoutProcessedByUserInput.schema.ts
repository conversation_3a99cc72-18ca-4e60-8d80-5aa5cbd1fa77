import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestWhereUniqueInputObjectSchema } from './RefundRequestWhereUniqueInput.schema';
import { RefundRequestUpdateWithoutProcessedByUserInputObjectSchema } from './RefundRequestUpdateWithoutProcessedByUserInput.schema';
import { RefundRequestUncheckedUpdateWithoutProcessedByUserInputObjectSchema } from './RefundRequestUncheckedUpdateWithoutProcessedByUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => RefundRequestWhereUniqueInputObjectSchema),
  data: z.union([z.lazy(() => RefundRequestUpdateWithoutProcessedByUserInputObjectSchema), z.lazy(() => RefundRequestUncheckedUpdateWithoutProcessedByUserInputObjectSchema)])
}).strict();
export const RefundRequestUpdateWithWhereUniqueWithoutProcessedByUserInputObjectSchema: z.ZodType<Prisma.RefundRequestUpdateWithWhereUniqueWithoutProcessedByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestUpdateWithWhereUniqueWithoutProcessedByUserInput>;
export const RefundRequestUpdateWithWhereUniqueWithoutProcessedByUserInputObjectZodSchema = makeSchema();
