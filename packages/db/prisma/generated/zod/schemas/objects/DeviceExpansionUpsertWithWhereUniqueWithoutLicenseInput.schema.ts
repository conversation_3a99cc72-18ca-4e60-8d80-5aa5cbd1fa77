import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceExpansionWhereUniqueInputObjectSchema } from './DeviceExpansionWhereUniqueInput.schema';
import { DeviceExpansionUpdateWithoutLicenseInputObjectSchema } from './DeviceExpansionUpdateWithoutLicenseInput.schema';
import { DeviceExpansionUncheckedUpdateWithoutLicenseInputObjectSchema } from './DeviceExpansionUncheckedUpdateWithoutLicenseInput.schema';
import { DeviceExpansionCreateWithoutLicenseInputObjectSchema } from './DeviceExpansionCreateWithoutLicenseInput.schema';
import { DeviceExpansionUncheckedCreateWithoutLicenseInputObjectSchema } from './DeviceExpansionUncheckedCreateWithoutLicenseInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema),
  update: z.union([z.lazy(() => DeviceExpansionUpdateWithoutLicenseInputObjectSchema), z.lazy(() => DeviceExpansionUncheckedUpdateWithoutLicenseInputObjectSchema)]),
  create: z.union([z.lazy(() => DeviceExpansionCreateWithoutLicenseInputObjectSchema), z.lazy(() => DeviceExpansionUncheckedCreateWithoutLicenseInputObjectSchema)])
}).strict();
export const DeviceExpansionUpsertWithWhereUniqueWithoutLicenseInputObjectSchema: z.ZodType<Prisma.DeviceExpansionUpsertWithWhereUniqueWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionUpsertWithWhereUniqueWithoutLicenseInput>;
export const DeviceExpansionUpsertWithWhereUniqueWithoutLicenseInputObjectZodSchema = makeSchema();
