import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutAuditLogsAsActorInputObjectSchema } from './UserCreateWithoutAuditLogsAsActorInput.schema';
import { UserUncheckedCreateWithoutAuditLogsAsActorInputObjectSchema } from './UserUncheckedCreateWithoutAuditLogsAsActorInput.schema';
import { UserCreateOrConnectWithoutAuditLogsAsActorInputObjectSchema } from './UserCreateOrConnectWithoutAuditLogsAsActorInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutAuditLogsAsActorInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutAuditLogsAsActorInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutAuditLogsAsActorInputObjectSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional()
}).strict();
export const UserCreateNestedOneWithoutAuditLogsAsActorInputObjectSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutAuditLogsAsActorInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateNestedOneWithoutAuditLogsAsActorInput>;
export const UserCreateNestedOneWithoutAuditLogsAsActorInputObjectZodSchema = makeSchema();
