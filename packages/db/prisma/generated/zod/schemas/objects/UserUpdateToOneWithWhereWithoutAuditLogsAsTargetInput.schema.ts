import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserUpdateWithoutAuditLogsAsTargetInputObjectSchema } from './UserUpdateWithoutAuditLogsAsTargetInput.schema';
import { UserUncheckedUpdateWithoutAuditLogsAsTargetInputObjectSchema } from './UserUncheckedUpdateWithoutAuditLogsAsTargetInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserWhereInputObjectSchema).optional(),
  data: z.union([z.lazy(() => UserUpdateWithoutAuditLogsAsTargetInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutAuditLogsAsTargetInputObjectSchema)])
}).strict();
export const UserUpdateToOneWithWhereWithoutAuditLogsAsTargetInputObjectSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutAuditLogsAsTargetInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutAuditLogsAsTargetInput>;
export const UserUpdateToOneWithWhereWithoutAuditLogsAsTargetInputObjectZodSchema = makeSchema();
