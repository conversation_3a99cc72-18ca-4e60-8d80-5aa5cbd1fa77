import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestCreateWithoutLicenseInputObjectSchema } from './RefundRequestCreateWithoutLicenseInput.schema';
import { RefundRequestUncheckedCreateWithoutLicenseInputObjectSchema } from './RefundRequestUncheckedCreateWithoutLicenseInput.schema';
import { RefundRequestCreateOrConnectWithoutLicenseInputObjectSchema } from './RefundRequestCreateOrConnectWithoutLicenseInput.schema';
import { RefundRequestUpsertWithWhereUniqueWithoutLicenseInputObjectSchema } from './RefundRequestUpsertWithWhereUniqueWithoutLicenseInput.schema';
import { RefundRequestCreateManyLicenseInputEnvelopeObjectSchema } from './RefundRequestCreateManyLicenseInputEnvelope.schema';
import { RefundRequestWhereUniqueInputObjectSchema } from './RefundRequestWhereUniqueInput.schema';
import { RefundRequestUpdateWithWhereUniqueWithoutLicenseInputObjectSchema } from './RefundRequestUpdateWithWhereUniqueWithoutLicenseInput.schema';
import { RefundRequestUpdateManyWithWhereWithoutLicenseInputObjectSchema } from './RefundRequestUpdateManyWithWhereWithoutLicenseInput.schema';
import { RefundRequestScalarWhereInputObjectSchema } from './RefundRequestScalarWhereInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => RefundRequestCreateWithoutLicenseInputObjectSchema), z.lazy(() => RefundRequestCreateWithoutLicenseInputObjectSchema).array(), z.lazy(() => RefundRequestUncheckedCreateWithoutLicenseInputObjectSchema), z.lazy(() => RefundRequestUncheckedCreateWithoutLicenseInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => RefundRequestCreateOrConnectWithoutLicenseInputObjectSchema), z.lazy(() => RefundRequestCreateOrConnectWithoutLicenseInputObjectSchema).array()]).optional(),
  upsert: z.union([z.lazy(() => RefundRequestUpsertWithWhereUniqueWithoutLicenseInputObjectSchema), z.lazy(() => RefundRequestUpsertWithWhereUniqueWithoutLicenseInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => RefundRequestCreateManyLicenseInputEnvelopeObjectSchema).optional(),
  set: z.union([z.lazy(() => RefundRequestWhereUniqueInputObjectSchema), z.lazy(() => RefundRequestWhereUniqueInputObjectSchema).array()]).optional(),
  disconnect: z.union([z.lazy(() => RefundRequestWhereUniqueInputObjectSchema), z.lazy(() => RefundRequestWhereUniqueInputObjectSchema).array()]).optional(),
  delete: z.union([z.lazy(() => RefundRequestWhereUniqueInputObjectSchema), z.lazy(() => RefundRequestWhereUniqueInputObjectSchema).array()]).optional(),
  connect: z.union([z.lazy(() => RefundRequestWhereUniqueInputObjectSchema), z.lazy(() => RefundRequestWhereUniqueInputObjectSchema).array()]).optional(),
  update: z.union([z.lazy(() => RefundRequestUpdateWithWhereUniqueWithoutLicenseInputObjectSchema), z.lazy(() => RefundRequestUpdateWithWhereUniqueWithoutLicenseInputObjectSchema).array()]).optional(),
  updateMany: z.union([z.lazy(() => RefundRequestUpdateManyWithWhereWithoutLicenseInputObjectSchema), z.lazy(() => RefundRequestUpdateManyWithWhereWithoutLicenseInputObjectSchema).array()]).optional(),
  deleteMany: z.union([z.lazy(() => RefundRequestScalarWhereInputObjectSchema), z.lazy(() => RefundRequestScalarWhereInputObjectSchema).array()]).optional()
}).strict();
export const RefundRequestUncheckedUpdateManyWithoutLicenseNestedInputObjectSchema: z.ZodType<Prisma.RefundRequestUncheckedUpdateManyWithoutLicenseNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestUncheckedUpdateManyWithoutLicenseNestedInput>;
export const RefundRequestUncheckedUpdateManyWithoutLicenseNestedInputObjectZodSchema = makeSchema();
