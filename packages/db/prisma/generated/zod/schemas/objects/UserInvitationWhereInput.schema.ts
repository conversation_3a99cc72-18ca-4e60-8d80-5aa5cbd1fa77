import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { EnumUserRoleFilterObjectSchema } from './EnumUserRoleFilter.schema';
import { UserRoleSchema } from '../enums/UserRole.schema';
import { EnumInvitationStatusFilterObjectSchema } from './EnumInvitationStatusFilter.schema';
import { InvitationStatusSchema } from '../enums/InvitationStatus.schema';
import { DateTimeFilterObjectSchema } from './DateTimeFilter.schema';
import { DateTimeNullableFilterObjectSchema } from './DateTimeNullableFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { UserScalarRelationFilterObjectSchema } from './UserScalarRelationFilter.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserNullableScalarRelationFilterObjectSchema } from './UserNullableScalarRelationFilter.schema'

const userinvitationwhereinputSchema = z.object({
  AND: z.union([z.lazy(() => UserInvitationWhereInputObjectSchema), z.lazy(() => UserInvitationWhereInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => UserInvitationWhereInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => UserInvitationWhereInputObjectSchema), z.lazy(() => UserInvitationWhereInputObjectSchema).array()]).optional(),
  email: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  role: z.union([z.lazy(() => EnumUserRoleFilterObjectSchema), UserRoleSchema]).optional(),
  token: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  status: z.union([z.lazy(() => EnumInvitationStatusFilterObjectSchema), InvitationStatusSchema]).optional(),
  expiresAt: z.union([z.lazy(() => DateTimeFilterObjectSchema), z.coerce.date()]).optional(),
  sentAt: z.union([z.lazy(() => DateTimeFilterObjectSchema), z.coerce.date()]).optional(),
  acceptedAt: z.union([z.lazy(() => DateTimeNullableFilterObjectSchema), z.coerce.date()]).optional().nullable(),
  sentBy: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  acceptedBy: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  sentByUser: z.union([z.lazy(() => UserScalarRelationFilterObjectSchema), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  acceptedByUser: z.union([z.lazy(() => UserNullableScalarRelationFilterObjectSchema), z.lazy(() => UserWhereInputObjectSchema)]).optional()
}).strict();
export const UserInvitationWhereInputObjectSchema: z.ZodType<Prisma.UserInvitationWhereInput> = userinvitationwhereinputSchema as unknown as z.ZodType<Prisma.UserInvitationWhereInput>;
export const UserInvitationWhereInputObjectZodSchema = userinvitationwhereinputSchema;
