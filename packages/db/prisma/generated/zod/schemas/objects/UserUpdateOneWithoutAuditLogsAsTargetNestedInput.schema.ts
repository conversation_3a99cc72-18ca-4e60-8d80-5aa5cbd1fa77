import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutAuditLogsAsTargetInputObjectSchema } from './UserCreateWithoutAuditLogsAsTargetInput.schema';
import { UserUncheckedCreateWithoutAuditLogsAsTargetInputObjectSchema } from './UserUncheckedCreateWithoutAuditLogsAsTargetInput.schema';
import { UserCreateOrConnectWithoutAuditLogsAsTargetInputObjectSchema } from './UserCreateOrConnectWithoutAuditLogsAsTargetInput.schema';
import { UserUpsertWithoutAuditLogsAsTargetInputObjectSchema } from './UserUpsertWithoutAuditLogsAsTargetInput.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserUpdateToOneWithWhereWithoutAuditLogsAsTargetInputObjectSchema } from './UserUpdateToOneWithWhereWithoutAuditLogsAsTargetInput.schema';
import { UserUpdateWithoutAuditLogsAsTargetInputObjectSchema } from './UserUpdateWithoutAuditLogsAsTargetInput.schema';
import { UserUncheckedUpdateWithoutAuditLogsAsTargetInputObjectSchema } from './UserUncheckedUpdateWithoutAuditLogsAsTargetInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutAuditLogsAsTargetInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutAuditLogsAsTargetInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutAuditLogsAsTargetInputObjectSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutAuditLogsAsTargetInputObjectSchema).optional(),
  disconnect: z.union([z.boolean(), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  delete: z.union([z.boolean(), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional(),
  update: z.union([z.lazy(() => UserUpdateToOneWithWhereWithoutAuditLogsAsTargetInputObjectSchema), z.lazy(() => UserUpdateWithoutAuditLogsAsTargetInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutAuditLogsAsTargetInputObjectSchema)]).optional()
}).strict();
export const UserUpdateOneWithoutAuditLogsAsTargetNestedInputObjectSchema: z.ZodType<Prisma.UserUpdateOneWithoutAuditLogsAsTargetNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateOneWithoutAuditLogsAsTargetNestedInput>;
export const UserUpdateOneWithoutAuditLogsAsTargetNestedInputObjectZodSchema = makeSchema();
