import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringWithAggregatesFilterObjectSchema } from './StringWithAggregatesFilter.schema';
import { IntWithAggregatesFilterObjectSchema } from './IntWithAggregatesFilter.schema';
import { EnumDeviceExpansionStatusWithAggregatesFilterObjectSchema } from './EnumDeviceExpansionStatusWithAggregatesFilter.schema';
import { DeviceExpansionStatusSchema } from '../enums/DeviceExpansionStatus.schema';
import { DateTimeNullableWithAggregatesFilterObjectSchema } from './DateTimeNullableWithAggregatesFilter.schema'

const deviceexpansionscalarwherewithaggregatesinputSchema = z.object({
  AND: z.union([z.lazy(() => DeviceExpansionScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => DeviceExpansionScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => DeviceExpansionScalarWhereWithAggregatesInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => DeviceExpansionScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => DeviceExpansionScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  licenseId: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  paymentIntentId: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  additionalDevices: z.union([z.lazy(() => IntWithAggregatesFilterObjectSchema), z.number().int()]).optional(),
  amount: z.union([z.lazy(() => IntWithAggregatesFilterObjectSchema), z.number().int()]).optional(),
  status: z.union([z.lazy(() => EnumDeviceExpansionStatusWithAggregatesFilterObjectSchema), DeviceExpansionStatusSchema]).optional(),
  processedAt: z.union([z.lazy(() => DateTimeNullableWithAggregatesFilterObjectSchema), z.coerce.date()]).optional().nullable()
}).strict();
export const DeviceExpansionScalarWhereWithAggregatesInputObjectSchema: z.ZodType<Prisma.DeviceExpansionScalarWhereWithAggregatesInput> = deviceexpansionscalarwherewithaggregatesinputSchema as unknown as z.ZodType<Prisma.DeviceExpansionScalarWhereWithAggregatesInput>;
export const DeviceExpansionScalarWhereWithAggregatesInputObjectZodSchema = deviceexpansionscalarwherewithaggregatesinputSchema;
