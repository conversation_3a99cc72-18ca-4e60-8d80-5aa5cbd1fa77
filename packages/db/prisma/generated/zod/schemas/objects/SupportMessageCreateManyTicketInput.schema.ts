import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.string().optional(),
  message: z.string(),
  isInternal: z.boolean().optional(),
  authorEmail: z.string().optional().nullable(),
  authorId: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional()
}).strict();
export const SupportMessageCreateManyTicketInputObjectSchema: z.ZodType<Prisma.SupportMessageCreateManyTicketInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageCreateManyTicketInput>;
export const SupportMessageCreateManyTicketInputObjectZodSchema = makeSchema();
