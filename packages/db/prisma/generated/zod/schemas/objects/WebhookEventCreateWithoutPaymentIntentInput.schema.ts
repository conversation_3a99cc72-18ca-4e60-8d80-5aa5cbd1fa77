import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.string().optional(),
  stripeEventId: z.string(),
  eventType: z.string(),
  processed: z.boolean().optional(),
  processedAt: z.coerce.date().optional().nullable(),
  errorMessage: z.string().optional().nullable(),
  retryCount: z.number().int().optional(),
  createdAt: z.coerce.date().optional()
}).strict();
export const WebhookEventCreateWithoutPaymentIntentInputObjectSchema: z.ZodType<Prisma.WebhookEventCreateWithoutPaymentIntentInput> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventCreateWithoutPaymentIntentInput>;
export const WebhookEventCreateWithoutPaymentIntentInputObjectZodSchema = makeSchema();
