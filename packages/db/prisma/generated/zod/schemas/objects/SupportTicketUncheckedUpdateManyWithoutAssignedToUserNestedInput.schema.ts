import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketCreateWithoutAssignedToUserInputObjectSchema } from './SupportTicketCreateWithoutAssignedToUserInput.schema';
import { SupportTicketUncheckedCreateWithoutAssignedToUserInputObjectSchema } from './SupportTicketUncheckedCreateWithoutAssignedToUserInput.schema';
import { SupportTicketCreateOrConnectWithoutAssignedToUserInputObjectSchema } from './SupportTicketCreateOrConnectWithoutAssignedToUserInput.schema';
import { SupportTicketUpsertWithWhereUniqueWithoutAssignedToUserInputObjectSchema } from './SupportTicketUpsertWithWhereUniqueWithoutAssignedToUserInput.schema';
import { SupportTicketCreateManyAssignedToUserInputEnvelopeObjectSchema } from './SupportTicketCreateManyAssignedToUserInputEnvelope.schema';
import { SupportTicketWhereUniqueInputObjectSchema } from './SupportTicketWhereUniqueInput.schema';
import { SupportTicketUpdateWithWhereUniqueWithoutAssignedToUserInputObjectSchema } from './SupportTicketUpdateWithWhereUniqueWithoutAssignedToUserInput.schema';
import { SupportTicketUpdateManyWithWhereWithoutAssignedToUserInputObjectSchema } from './SupportTicketUpdateManyWithWhereWithoutAssignedToUserInput.schema';
import { SupportTicketScalarWhereInputObjectSchema } from './SupportTicketScalarWhereInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => SupportTicketCreateWithoutAssignedToUserInputObjectSchema), z.lazy(() => SupportTicketCreateWithoutAssignedToUserInputObjectSchema).array(), z.lazy(() => SupportTicketUncheckedCreateWithoutAssignedToUserInputObjectSchema), z.lazy(() => SupportTicketUncheckedCreateWithoutAssignedToUserInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => SupportTicketCreateOrConnectWithoutAssignedToUserInputObjectSchema), z.lazy(() => SupportTicketCreateOrConnectWithoutAssignedToUserInputObjectSchema).array()]).optional(),
  upsert: z.union([z.lazy(() => SupportTicketUpsertWithWhereUniqueWithoutAssignedToUserInputObjectSchema), z.lazy(() => SupportTicketUpsertWithWhereUniqueWithoutAssignedToUserInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => SupportTicketCreateManyAssignedToUserInputEnvelopeObjectSchema).optional(),
  set: z.union([z.lazy(() => SupportTicketWhereUniqueInputObjectSchema), z.lazy(() => SupportTicketWhereUniqueInputObjectSchema).array()]).optional(),
  disconnect: z.union([z.lazy(() => SupportTicketWhereUniqueInputObjectSchema), z.lazy(() => SupportTicketWhereUniqueInputObjectSchema).array()]).optional(),
  delete: z.union([z.lazy(() => SupportTicketWhereUniqueInputObjectSchema), z.lazy(() => SupportTicketWhereUniqueInputObjectSchema).array()]).optional(),
  connect: z.union([z.lazy(() => SupportTicketWhereUniqueInputObjectSchema), z.lazy(() => SupportTicketWhereUniqueInputObjectSchema).array()]).optional(),
  update: z.union([z.lazy(() => SupportTicketUpdateWithWhereUniqueWithoutAssignedToUserInputObjectSchema), z.lazy(() => SupportTicketUpdateWithWhereUniqueWithoutAssignedToUserInputObjectSchema).array()]).optional(),
  updateMany: z.union([z.lazy(() => SupportTicketUpdateManyWithWhereWithoutAssignedToUserInputObjectSchema), z.lazy(() => SupportTicketUpdateManyWithWhereWithoutAssignedToUserInputObjectSchema).array()]).optional(),
  deleteMany: z.union([z.lazy(() => SupportTicketScalarWhereInputObjectSchema), z.lazy(() => SupportTicketScalarWhereInputObjectSchema).array()]).optional()
}).strict();
export const SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInputObjectSchema: z.ZodType<Prisma.SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInput>;
export const SupportTicketUncheckedUpdateManyWithoutAssignedToUserNestedInputObjectZodSchema = makeSchema();
