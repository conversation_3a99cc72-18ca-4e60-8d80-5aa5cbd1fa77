import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageCreateWithoutAuthorUserInputObjectSchema } from './SupportMessageCreateWithoutAuthorUserInput.schema';
import { SupportMessageUncheckedCreateWithoutAuthorUserInputObjectSchema } from './SupportMessageUncheckedCreateWithoutAuthorUserInput.schema';
import { SupportMessageCreateOrConnectWithoutAuthorUserInputObjectSchema } from './SupportMessageCreateOrConnectWithoutAuthorUserInput.schema';
import { SupportMessageCreateManyAuthorUserInputEnvelopeObjectSchema } from './SupportMessageCreateManyAuthorUserInputEnvelope.schema';
import { SupportMessageWhereUniqueInputObjectSchema } from './SupportMessageWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => SupportMessageCreateWithoutAuthorUserInputObjectSchema), z.lazy(() => SupportMessageCreateWithoutAuthorUserInputObjectSchema).array(), z.lazy(() => SupportMessageUncheckedCreateWithoutAuthorUserInputObjectSchema), z.lazy(() => SupportMessageUncheckedCreateWithoutAuthorUserInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => SupportMessageCreateOrConnectWithoutAuthorUserInputObjectSchema), z.lazy(() => SupportMessageCreateOrConnectWithoutAuthorUserInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => SupportMessageCreateManyAuthorUserInputEnvelopeObjectSchema).optional(),
  connect: z.union([z.lazy(() => SupportMessageWhereUniqueInputObjectSchema), z.lazy(() => SupportMessageWhereUniqueInputObjectSchema).array()]).optional()
}).strict();
export const SupportMessageCreateNestedManyWithoutAuthorUserInputObjectSchema: z.ZodType<Prisma.SupportMessageCreateNestedManyWithoutAuthorUserInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageCreateNestedManyWithoutAuthorUserInput>;
export const SupportMessageCreateNestedManyWithoutAuthorUserInputObjectZodSchema = makeSchema();
