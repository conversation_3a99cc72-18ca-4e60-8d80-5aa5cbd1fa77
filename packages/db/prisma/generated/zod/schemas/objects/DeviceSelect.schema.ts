import { z } from 'zod';
import type { Prisma } from '../../../client';
import { LicenseArgsObjectSchema } from './LicenseArgs.schema'

const makeSchema = () => z.object({
  id: z.boolean().optional(),
  licenseId: z.boolean().optional(),
  deviceHash: z.boolean().optional(),
  salt: z.boolean().optional(),
  status: z.boolean().optional(),
  firstSeen: z.boolean().optional(),
  lastSeen: z.boolean().optional(),
  removedAt: z.boolean().optional(),
  appVersion: z.boolean().optional(),
  deviceName: z.boolean().optional(),
  deviceType: z.boolean().optional(),
  deviceModel: z.boolean().optional(),
  operatingSystem: z.boolean().optional(),
  architecture: z.boolean().optional(),
  screenResolution: z.boolean().optional(),
  totalMemory: z.boolean().optional(),
  userNickname: z.boolean().optional(),
  location: z.boolean().optional(),
  notes: z.boolean().optional(),
  license: z.union([z.boolean(), z.lazy(() => LicenseArgsObjectSchema)]).optional()
}).strict();
export const DeviceSelectObjectSchema: z.ZodType<Prisma.DeviceSelect> = makeSchema() as unknown as z.ZodType<Prisma.DeviceSelect>;
export const DeviceSelectObjectZodSchema = makeSchema();
