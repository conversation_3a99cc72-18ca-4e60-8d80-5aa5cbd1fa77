import { z } from 'zod';
import type { Prisma } from '../../../client';
import { AuditLogWhereUniqueInputObjectSchema } from './AuditLogWhereUniqueInput.schema';
import { AuditLogUpdateWithoutTargetInputObjectSchema } from './AuditLogUpdateWithoutTargetInput.schema';
import { AuditLogUncheckedUpdateWithoutTargetInputObjectSchema } from './AuditLogUncheckedUpdateWithoutTargetInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => AuditLogWhereUniqueInputObjectSchema),
  data: z.union([z.lazy(() => AuditLogUpdateWithoutTargetInputObjectSchema), z.lazy(() => AuditLogUncheckedUpdateWithoutTargetInputObjectSchema)])
}).strict();
export const AuditLogUpdateWithWhereUniqueWithoutTargetInputObjectSchema: z.ZodType<Prisma.AuditLogUpdateWithWhereUniqueWithoutTargetInput> = makeSchema() as unknown as z.ZodType<Prisma.AuditLogUpdateWithWhereUniqueWithoutTargetInput>;
export const AuditLogUpdateWithWhereUniqueWithoutTargetInputObjectZodSchema = makeSchema();
