import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestWhereUniqueInputObjectSchema } from './RefundRequestWhereUniqueInput.schema';
import { RefundRequestUpdateWithoutLicenseInputObjectSchema } from './RefundRequestUpdateWithoutLicenseInput.schema';
import { RefundRequestUncheckedUpdateWithoutLicenseInputObjectSchema } from './RefundRequestUncheckedUpdateWithoutLicenseInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => RefundRequestWhereUniqueInputObjectSchema),
  data: z.union([z.lazy(() => RefundRequestUpdateWithoutLicenseInputObjectSchema), z.lazy(() => RefundRequestUncheckedUpdateWithoutLicenseInputObjectSchema)])
}).strict();
export const RefundRequestUpdateWithWhereUniqueWithoutLicenseInputObjectSchema: z.ZodType<Prisma.RefundRequestUpdateWithWhereUniqueWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestUpdateWithWhereUniqueWithoutLicenseInput>;
export const RefundRequestUpdateWithWhereUniqueWithoutLicenseInputObjectZodSchema = makeSchema();
