import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageCreateManyTicketInputObjectSchema } from './SupportMessageCreateManyTicketInput.schema'

const makeSchema = () => z.object({
  data: z.union([z.lazy(() => SupportMessageCreateManyTicketInputObjectSchema), z.lazy(() => SupportMessageCreateManyTicketInputObjectSchema).array()]),
  skipDuplicates: z.boolean().optional()
}).strict();
export const SupportMessageCreateManyTicketInputEnvelopeObjectSchema: z.ZodType<Prisma.SupportMessageCreateManyTicketInputEnvelope> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageCreateManyTicketInputEnvelope>;
export const SupportMessageCreateManyTicketInputEnvelopeObjectZodSchema = makeSchema();
