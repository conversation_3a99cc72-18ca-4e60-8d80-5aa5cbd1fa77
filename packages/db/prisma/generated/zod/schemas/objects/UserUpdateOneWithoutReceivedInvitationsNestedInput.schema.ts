import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutReceivedInvitationsInputObjectSchema } from './UserCreateWithoutReceivedInvitationsInput.schema';
import { UserUncheckedCreateWithoutReceivedInvitationsInputObjectSchema } from './UserUncheckedCreateWithoutReceivedInvitationsInput.schema';
import { UserCreateOrConnectWithoutReceivedInvitationsInputObjectSchema } from './UserCreateOrConnectWithoutReceivedInvitationsInput.schema';
import { UserUpsertWithoutReceivedInvitationsInputObjectSchema } from './UserUpsertWithoutReceivedInvitationsInput.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserUpdateToOneWithWhereWithoutReceivedInvitationsInputObjectSchema } from './UserUpdateToOneWithWhereWithoutReceivedInvitationsInput.schema';
import { UserUpdateWithoutReceivedInvitationsInputObjectSchema } from './UserUpdateWithoutReceivedInvitationsInput.schema';
import { UserUncheckedUpdateWithoutReceivedInvitationsInputObjectSchema } from './UserUncheckedUpdateWithoutReceivedInvitationsInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutReceivedInvitationsInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutReceivedInvitationsInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutReceivedInvitationsInputObjectSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutReceivedInvitationsInputObjectSchema).optional(),
  disconnect: z.union([z.boolean(), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  delete: z.union([z.boolean(), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional(),
  update: z.union([z.lazy(() => UserUpdateToOneWithWhereWithoutReceivedInvitationsInputObjectSchema), z.lazy(() => UserUpdateWithoutReceivedInvitationsInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutReceivedInvitationsInputObjectSchema)]).optional()
}).strict();
export const UserUpdateOneWithoutReceivedInvitationsNestedInputObjectSchema: z.ZodType<Prisma.UserUpdateOneWithoutReceivedInvitationsNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateOneWithoutReceivedInvitationsNestedInput>;
export const UserUpdateOneWithoutReceivedInvitationsNestedInputObjectZodSchema = makeSchema();
