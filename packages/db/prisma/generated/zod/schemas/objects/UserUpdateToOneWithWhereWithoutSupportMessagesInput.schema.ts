import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserUpdateWithoutSupportMessagesInputObjectSchema } from './UserUpdateWithoutSupportMessagesInput.schema';
import { UserUncheckedUpdateWithoutSupportMessagesInputObjectSchema } from './UserUncheckedUpdateWithoutSupportMessagesInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserWhereInputObjectSchema).optional(),
  data: z.union([z.lazy(() => UserUpdateWithoutSupportMessagesInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutSupportMessagesInputObjectSchema)])
}).strict();
export const UserUpdateToOneWithWhereWithoutSupportMessagesInputObjectSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutSupportMessagesInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutSupportMessagesInput>;
export const UserUpdateToOneWithWhereWithoutSupportMessagesInputObjectZodSchema = makeSchema();
