import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceExpansionWhereUniqueInputObjectSchema } from './DeviceExpansionWhereUniqueInput.schema';
import { DeviceExpansionUpdateWithoutLicenseInputObjectSchema } from './DeviceExpansionUpdateWithoutLicenseInput.schema';
import { DeviceExpansionUncheckedUpdateWithoutLicenseInputObjectSchema } from './DeviceExpansionUncheckedUpdateWithoutLicenseInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema),
  data: z.union([z.lazy(() => DeviceExpansionUpdateWithoutLicenseInputObjectSchema), z.lazy(() => DeviceExpansionUncheckedUpdateWithoutLicenseInputObjectSchema)])
}).strict();
export const DeviceExpansionUpdateWithWhereUniqueWithoutLicenseInputObjectSchema: z.ZodType<Prisma.DeviceExpansionUpdateWithWhereUniqueWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionUpdateWithWhereUniqueWithoutLicenseInput>;
export const DeviceExpansionUpdateWithWhereUniqueWithoutLicenseInputObjectZodSchema = makeSchema();
