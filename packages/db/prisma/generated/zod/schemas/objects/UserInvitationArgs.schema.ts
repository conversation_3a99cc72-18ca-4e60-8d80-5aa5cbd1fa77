import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationSelectObjectSchema } from './UserInvitationSelect.schema';
import { UserInvitationIncludeObjectSchema } from './UserInvitationInclude.schema'

const makeSchema = () => z.object({
  select: z.lazy(() => UserInvitationSelectObjectSchema).optional(),
  include: z.lazy(() => UserInvitationIncludeObjectSchema).optional()
}).strict();
export const UserInvitationArgsObjectSchema = makeSchema();
export const UserInvitationArgsObjectZodSchema = makeSchema();
