import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationWhereUniqueInputObjectSchema } from './UserInvitationWhereUniqueInput.schema';
import { UserInvitationCreateWithoutAcceptedByUserInputObjectSchema } from './UserInvitationCreateWithoutAcceptedByUserInput.schema';
import { UserInvitationUncheckedCreateWithoutAcceptedByUserInputObjectSchema } from './UserInvitationUncheckedCreateWithoutAcceptedByUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserInvitationWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => UserInvitationCreateWithoutAcceptedByUserInputObjectSchema), z.lazy(() => UserInvitationUncheckedCreateWithoutAcceptedByUserInputObjectSchema)])
}).strict();
export const UserInvitationCreateOrConnectWithoutAcceptedByUserInputObjectSchema: z.ZodType<Prisma.UserInvitationCreateOrConnectWithoutAcceptedByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationCreateOrConnectWithoutAcceptedByUserInput>;
export const UserInvitationCreateOrConnectWithoutAcceptedByUserInputObjectZodSchema = makeSchema();
