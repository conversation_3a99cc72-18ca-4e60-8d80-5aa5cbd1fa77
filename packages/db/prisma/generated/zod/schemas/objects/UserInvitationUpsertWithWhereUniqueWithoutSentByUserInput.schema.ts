import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationWhereUniqueInputObjectSchema } from './UserInvitationWhereUniqueInput.schema';
import { UserInvitationUpdateWithoutSentByUserInputObjectSchema } from './UserInvitationUpdateWithoutSentByUserInput.schema';
import { UserInvitationUncheckedUpdateWithoutSentByUserInputObjectSchema } from './UserInvitationUncheckedUpdateWithoutSentByUserInput.schema';
import { UserInvitationCreateWithoutSentByUserInputObjectSchema } from './UserInvitationCreateWithoutSentByUserInput.schema';
import { UserInvitationUncheckedCreateWithoutSentByUserInputObjectSchema } from './UserInvitationUncheckedCreateWithoutSentByUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserInvitationWhereUniqueInputObjectSchema),
  update: z.union([z.lazy(() => UserInvitationUpdateWithoutSentByUserInputObjectSchema), z.lazy(() => UserInvitationUncheckedUpdateWithoutSentByUserInputObjectSchema)]),
  create: z.union([z.lazy(() => UserInvitationCreateWithoutSentByUserInputObjectSchema), z.lazy(() => UserInvitationUncheckedCreateWithoutSentByUserInputObjectSchema)])
}).strict();
export const UserInvitationUpsertWithWhereUniqueWithoutSentByUserInputObjectSchema: z.ZodType<Prisma.UserInvitationUpsertWithWhereUniqueWithoutSentByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationUpsertWithWhereUniqueWithoutSentByUserInput>;
export const UserInvitationUpsertWithWhereUniqueWithoutSentByUserInputObjectZodSchema = makeSchema();
