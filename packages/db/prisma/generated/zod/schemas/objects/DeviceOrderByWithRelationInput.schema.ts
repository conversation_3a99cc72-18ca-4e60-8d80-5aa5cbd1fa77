import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { LicenseOrderByWithRelationInputObjectSchema } from './LicenseOrderByWithRelationInput.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  licenseId: SortOrderSchema.optional(),
  deviceHash: SortOrderSchema.optional(),
  salt: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  firstSeen: SortOrderSchema.optional(),
  lastSeen: SortOrderSchema.optional(),
  removedAt: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  appVersion: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  deviceName: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  deviceType: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  deviceModel: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  operatingSystem: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  architecture: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  screenResolution: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  totalMemory: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  userNickname: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  location: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  notes: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  license: z.lazy(() => LicenseOrderByWithRelationInputObjectSchema).optional()
}).strict();
export const DeviceOrderByWithRelationInputObjectSchema: z.ZodType<Prisma.DeviceOrderByWithRelationInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceOrderByWithRelationInput>;
export const DeviceOrderByWithRelationInputObjectZodSchema = makeSchema();
