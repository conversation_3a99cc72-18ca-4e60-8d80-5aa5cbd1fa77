import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  licenseKey: SortOrderSchema.optional(),
  licenseType: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  maxDevices: SortOrderSchema.optional(),
  usedDevices: SortOrderSchema.optional(),
  createdAt: SortOrderSchema.optional(),
  updatedAt: SortOrderSchema.optional(),
  expiresAt: SortOrderSchema.optional(),
  activatedAt: SortOrderSchema.optional(),
  customerEmail: SortOrderSchema.optional(),
  customerName: SortOrderSchema.optional(),
  createdBy: SortOrderSchema.optional(),
  paymentIntentId: SortOrderSchema.optional(),
  totalPaidAmount: SortOrderSchema.optional(),
  refundedAt: SortOrderSchema.optional(),
  refundReason: SortOrderSchema.optional(),
  refundAmount: SortOrderSchema.optional(),
  emailSentAt: SortOrderSchema.optional(),
  emailDeliveryStatus: SortOrderSchema.optional()
}).strict();
export const LicenseCountOrderByAggregateInputObjectSchema: z.ZodType<Prisma.LicenseCountOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.LicenseCountOrderByAggregateInput>;
export const LicenseCountOrderByAggregateInputObjectZodSchema = makeSchema();
