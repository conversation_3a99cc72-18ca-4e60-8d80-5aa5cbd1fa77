import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserUpdateWithoutCreatedLicensesInputObjectSchema } from './UserUpdateWithoutCreatedLicensesInput.schema';
import { UserUncheckedUpdateWithoutCreatedLicensesInputObjectSchema } from './UserUncheckedUpdateWithoutCreatedLicensesInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserWhereInputObjectSchema).optional(),
  data: z.union([z.lazy(() => UserUpdateWithoutCreatedLicensesInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutCreatedLicensesInputObjectSchema)])
}).strict();
export const UserUpdateToOneWithWhereWithoutCreatedLicensesInputObjectSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutCreatedLicensesInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutCreatedLicensesInput>;
export const UserUpdateToOneWithWhereWithoutCreatedLicensesInputObjectZodSchema = makeSchema();
