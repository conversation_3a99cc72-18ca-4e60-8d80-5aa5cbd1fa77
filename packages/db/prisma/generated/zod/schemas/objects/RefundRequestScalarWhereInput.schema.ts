import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { EnumRefundStatusFilterObjectSchema } from './EnumRefundStatusFilter.schema';
import { RefundStatusSchema } from '../enums/RefundStatus.schema';
import { IntNullableFilterObjectSchema } from './IntNullableFilter.schema';
import { StringNullableListFilterObjectSchema } from './StringNullableListFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { DateTimeNullableFilterObjectSchema } from './DateTimeNullableFilter.schema'

const refundrequestscalarwhereinputSchema = z.object({
  AND: z.union([z.lazy(() => RefundRequestScalarWhereInputObjectSchema), z.lazy(() => RefundRequestScalarWhereInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => RefundRequestScalarWhereInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => RefundRequestScalarWhereInputObjectSchema), z.lazy(() => RefundRequestScalarWhereInputObjectSchema).array()]).optional(),
  licenseId: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  requestedBy: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  reason: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  status: z.union([z.lazy(() => EnumRefundStatusFilterObjectSchema), RefundStatusSchema]).optional(),
  requestedAmount: z.union([z.lazy(() => IntNullableFilterObjectSchema), z.number().int()]).optional().nullable(),
  approvedAmount: z.union([z.lazy(() => IntNullableFilterObjectSchema), z.number().int()]).optional().nullable(),
  stripeRefundIds: z.lazy(() => StringNullableListFilterObjectSchema).optional(),
  adminNotes: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  processedBy: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  processedAt: z.union([z.lazy(() => DateTimeNullableFilterObjectSchema), z.coerce.date()]).optional().nullable()
}).strict();
export const RefundRequestScalarWhereInputObjectSchema: z.ZodType<Prisma.RefundRequestScalarWhereInput> = refundrequestscalarwhereinputSchema as unknown as z.ZodType<Prisma.RefundRequestScalarWhereInput>;
export const RefundRequestScalarWhereInputObjectZodSchema = refundrequestscalarwhereinputSchema;
