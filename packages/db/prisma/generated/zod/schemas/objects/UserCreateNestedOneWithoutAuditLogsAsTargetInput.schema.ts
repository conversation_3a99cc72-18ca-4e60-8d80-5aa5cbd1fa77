import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutAuditLogsAsTargetInputObjectSchema } from './UserCreateWithoutAuditLogsAsTargetInput.schema';
import { UserUncheckedCreateWithoutAuditLogsAsTargetInputObjectSchema } from './UserUncheckedCreateWithoutAuditLogsAsTargetInput.schema';
import { UserCreateOrConnectWithoutAuditLogsAsTargetInputObjectSchema } from './UserCreateOrConnectWithoutAuditLogsAsTargetInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutAuditLogsAsTargetInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutAuditLogsAsTargetInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutAuditLogsAsTargetInputObjectSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional()
}).strict();
export const UserCreateNestedOneWithoutAuditLogsAsTargetInputObjectSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutAuditLogsAsTargetInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateNestedOneWithoutAuditLogsAsTargetInput>;
export const UserCreateNestedOneWithoutAuditLogsAsTargetInputObjectZodSchema = makeSchema();
