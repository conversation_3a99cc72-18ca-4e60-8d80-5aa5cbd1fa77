import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceStatusSchema } from '../enums/DeviceStatus.schema';
import { NestedEnumDeviceStatusWithAggregatesFilterObjectSchema } from './NestedEnumDeviceStatusWithAggregatesFilter.schema';
import { NestedIntFilterObjectSchema } from './NestedIntFilter.schema';
import { NestedEnumDeviceStatusFilterObjectSchema } from './NestedEnumDeviceStatusFilter.schema'

const makeSchema = () => z.object({
  equals: DeviceStatusSchema.optional(),
  in: DeviceStatusSchema.array().optional(),
  notIn: DeviceStatusSchema.array().optional(),
  not: z.union([DeviceStatusSchema, z.lazy(() => NestedEnumDeviceStatusWithAggregatesFilterObjectSchema)]).optional(),
  _count: z.lazy(() => NestedIntFilterObjectSchema).optional(),
  _min: z.lazy(() => NestedEnumDeviceStatusFilterObjectSchema).optional(),
  _max: z.lazy(() => NestedEnumDeviceStatusFilterObjectSchema).optional()
}).strict();
export const EnumDeviceStatusWithAggregatesFilterObjectSchema: z.ZodType<Prisma.EnumDeviceStatusWithAggregatesFilter> = makeSchema() as unknown as z.ZodType<Prisma.EnumDeviceStatusWithAggregatesFilter>;
export const EnumDeviceStatusWithAggregatesFilterObjectZodSchema = makeSchema();
