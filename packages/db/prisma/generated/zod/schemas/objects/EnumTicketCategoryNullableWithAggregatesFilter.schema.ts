import { z } from 'zod';
import type { Prisma } from '../../../client';
import { TicketCategorySchema } from '../enums/TicketCategory.schema';
import { NestedEnumTicketCategoryNullableWithAggregatesFilterObjectSchema } from './NestedEnumTicketCategoryNullableWithAggregatesFilter.schema';
import { NestedIntNullableFilterObjectSchema } from './NestedIntNullableFilter.schema';
import { NestedEnumTicketCategoryNullableFilterObjectSchema } from './NestedEnumTicketCategoryNullableFilter.schema'

const makeSchema = () => z.object({
  equals: TicketCategorySchema.optional().nullable(),
  in: TicketCategorySchema.array().optional().nullable(),
  notIn: TicketCategorySchema.array().optional().nullable(),
  not: z.union([TicketCategorySchema, z.lazy(() => NestedEnumTicketCategoryNullableWithAggregatesFilterObjectSchema)]).optional().nullable(),
  _count: z.lazy(() => NestedIntNullableFilterObjectSchema).optional(),
  _min: z.lazy(() => NestedEnumTicketCategoryNullableFilterObjectSchema).optional(),
  _max: z.lazy(() => NestedEnumTicketCategoryNullableFilterObjectSchema).optional()
}).strict();
export const EnumTicketCategoryNullableWithAggregatesFilterObjectSchema: z.ZodType<Prisma.EnumTicketCategoryNullableWithAggregatesFilter> = makeSchema() as unknown as z.ZodType<Prisma.EnumTicketCategoryNullableWithAggregatesFilter>;
export const EnumTicketCategoryNullableWithAggregatesFilterObjectZodSchema = makeSchema();
