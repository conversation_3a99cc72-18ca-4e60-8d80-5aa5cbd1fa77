import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  email: SortOrderSchema.optional(),
  role: SortOrderSchema.optional(),
  token: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  expiresAt: SortOrderSchema.optional(),
  sentAt: SortOrderSchema.optional(),
  acceptedAt: SortOrderSchema.optional(),
  sentBy: SortOrderSchema.optional(),
  acceptedBy: SortOrderSchema.optional()
}).strict();
export const UserInvitationMaxOrderByAggregateInputObjectSchema: z.ZodType<Prisma.UserInvitationMaxOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationMaxOrderByAggregateInput>;
export const UserInvitationMaxOrderByAggregateInputObjectZodSchema = makeSchema();
