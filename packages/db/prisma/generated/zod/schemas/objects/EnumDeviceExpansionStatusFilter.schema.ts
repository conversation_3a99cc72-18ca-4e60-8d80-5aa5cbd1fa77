import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceExpansionStatusSchema } from '../enums/DeviceExpansionStatus.schema';
import { NestedEnumDeviceExpansionStatusFilterObjectSchema } from './NestedEnumDeviceExpansionStatusFilter.schema'

const makeSchema = () => z.object({
  equals: DeviceExpansionStatusSchema.optional(),
  in: DeviceExpansionStatusSchema.array().optional(),
  notIn: DeviceExpansionStatusSchema.array().optional(),
  not: z.union([DeviceExpansionStatusSchema, z.lazy(() => NestedEnumDeviceExpansionStatusFilterObjectSchema)]).optional()
}).strict();
export const EnumDeviceExpansionStatusFilterObjectSchema: z.ZodType<Prisma.EnumDeviceExpansionStatusFilter> = makeSchema() as unknown as z.ZodType<Prisma.EnumDeviceExpansionStatusFilter>;
export const EnumDeviceExpansionStatusFilterObjectZodSchema = makeSchema();
