import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  maxDevices: SortOrderSchema.optional(),
  usedDevices: SortOrderSchema.optional(),
  totalPaidAmount: SortOrderSchema.optional(),
  refundAmount: SortOrderSchema.optional()
}).strict();
export const LicenseAvgOrderByAggregateInputObjectSchema: z.ZodType<Prisma.LicenseAvgOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.LicenseAvgOrderByAggregateInput>;
export const LicenseAvgOrderByAggregateInputObjectZodSchema = makeSchema();
