import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  identifier: SortOrderSchema.optional(),
  action: SortOrderSchema.optional(),
  count: SortOrderSchema.optional(),
  windowStart: SortOrderSchema.optional(),
  expiresAt: SortOrderSchema.optional()
}).strict();
export const RateLimitOrderByWithRelationInputObjectSchema: z.ZodType<Prisma.RateLimitOrderByWithRelationInput> = makeSchema() as unknown as z.ZodType<Prisma.RateLimitOrderByWithRelationInput>;
export const RateLimitOrderByWithRelationInputObjectZodSchema = makeSchema();
