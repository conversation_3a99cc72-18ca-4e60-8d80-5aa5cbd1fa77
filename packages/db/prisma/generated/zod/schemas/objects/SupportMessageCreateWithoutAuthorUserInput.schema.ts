import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketCreateNestedOneWithoutMessagesInputObjectSchema } from './SupportTicketCreateNestedOneWithoutMessagesInput.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  message: z.string(),
  isInternal: z.boolean().optional(),
  authorEmail: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  ticket: z.lazy(() => SupportTicketCreateNestedOneWithoutMessagesInputObjectSchema)
}).strict();
export const SupportMessageCreateWithoutAuthorUserInputObjectSchema: z.ZodType<Prisma.SupportMessageCreateWithoutAuthorUserInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageCreateWithoutAuthorUserInput>;
export const SupportMessageCreateWithoutAuthorUserInputObjectZodSchema = makeSchema();
