import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestScalarWhereInputObjectSchema } from './RefundRequestScalarWhereInput.schema';
import { RefundRequestUpdateManyMutationInputObjectSchema } from './RefundRequestUpdateManyMutationInput.schema';
import { RefundRequestUncheckedUpdateManyWithoutProcessedByUserInputObjectSchema } from './RefundRequestUncheckedUpdateManyWithoutProcessedByUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => RefundRequestScalarWhereInputObjectSchema),
  data: z.union([z.lazy(() => RefundRequestUpdateManyMutationInputObjectSchema), z.lazy(() => RefundRequestUncheckedUpdateManyWithoutProcessedByUserInputObjectSchema)])
}).strict();
export const RefundRequestUpdateManyWithWhereWithoutProcessedByUserInputObjectSchema: z.ZodType<Prisma.RefundRequestUpdateManyWithWhereWithoutProcessedByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestUpdateManyWithWhereWithoutProcessedByUserInput>;
export const RefundRequestUpdateManyWithWhereWithoutProcessedByUserInputObjectZodSchema = makeSchema();
