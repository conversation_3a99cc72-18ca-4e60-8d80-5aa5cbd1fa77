import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { EnumTicketStatusFilterObjectSchema } from './EnumTicketStatusFilter.schema';
import { TicketStatusSchema } from '../enums/TicketStatus.schema';
import { EnumTicketPriorityFilterObjectSchema } from './EnumTicketPriorityFilter.schema';
import { TicketPrioritySchema } from '../enums/TicketPriority.schema';
import { EnumTicketCategoryNullableFilterObjectSchema } from './EnumTicketCategoryNullableFilter.schema';
import { TicketCategorySchema } from '../enums/TicketCategory.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { DateTimeNullableFilterObjectSchema } from './DateTimeNullableFilter.schema';
import { UserNullableScalarRelationFilterObjectSchema } from './UserNullableScalarRelationFilter.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { SupportMessageListRelationFilterObjectSchema } from './SupportMessageListRelationFilter.schema'

const supportticketwhereinputSchema = z.object({
  AND: z.union([z.lazy(() => SupportTicketWhereInputObjectSchema), z.lazy(() => SupportTicketWhereInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => SupportTicketWhereInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => SupportTicketWhereInputObjectSchema), z.lazy(() => SupportTicketWhereInputObjectSchema).array()]).optional(),
  ticketId: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  subject: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  description: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  status: z.union([z.lazy(() => EnumTicketStatusFilterObjectSchema), TicketStatusSchema]).optional(),
  priority: z.union([z.lazy(() => EnumTicketPriorityFilterObjectSchema), TicketPrioritySchema]).optional(),
  category: z.union([z.lazy(() => EnumTicketCategoryNullableFilterObjectSchema), TicketCategorySchema]).optional().nullable(),
  customerEmail: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  customerName: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  licenseKey: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  assignedTo: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  resolvedAt: z.union([z.lazy(() => DateTimeNullableFilterObjectSchema), z.coerce.date()]).optional().nullable(),
  assignedToUser: z.union([z.lazy(() => UserNullableScalarRelationFilterObjectSchema), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  messages: z.lazy(() => SupportMessageListRelationFilterObjectSchema).optional()
}).strict();
export const SupportTicketWhereInputObjectSchema: z.ZodType<Prisma.SupportTicketWhereInput> = supportticketwhereinputSchema as unknown as z.ZodType<Prisma.SupportTicketWhereInput>;
export const SupportTicketWhereInputObjectZodSchema = supportticketwhereinputSchema;
