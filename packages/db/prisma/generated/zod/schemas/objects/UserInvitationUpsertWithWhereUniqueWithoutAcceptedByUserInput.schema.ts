import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationWhereUniqueInputObjectSchema } from './UserInvitationWhereUniqueInput.schema';
import { UserInvitationUpdateWithoutAcceptedByUserInputObjectSchema } from './UserInvitationUpdateWithoutAcceptedByUserInput.schema';
import { UserInvitationUncheckedUpdateWithoutAcceptedByUserInputObjectSchema } from './UserInvitationUncheckedUpdateWithoutAcceptedByUserInput.schema';
import { UserInvitationCreateWithoutAcceptedByUserInputObjectSchema } from './UserInvitationCreateWithoutAcceptedByUserInput.schema';
import { UserInvitationUncheckedCreateWithoutAcceptedByUserInputObjectSchema } from './UserInvitationUncheckedCreateWithoutAcceptedByUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserInvitationWhereUniqueInputObjectSchema),
  update: z.union([z.lazy(() => UserInvitationUpdateWithoutAcceptedByUserInputObjectSchema), z.lazy(() => UserInvitationUncheckedUpdateWithoutAcceptedByUserInputObjectSchema)]),
  create: z.union([z.lazy(() => UserInvitationCreateWithoutAcceptedByUserInputObjectSchema), z.lazy(() => UserInvitationUncheckedCreateWithoutAcceptedByUserInputObjectSchema)])
}).strict();
export const UserInvitationUpsertWithWhereUniqueWithoutAcceptedByUserInputObjectSchema: z.ZodType<Prisma.UserInvitationUpsertWithWhereUniqueWithoutAcceptedByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationUpsertWithWhereUniqueWithoutAcceptedByUserInput>;
export const UserInvitationUpsertWithWhereUniqueWithoutAcceptedByUserInputObjectZodSchema = makeSchema();
