import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutProcessedRefundsInputObjectSchema } from './UserCreateWithoutProcessedRefundsInput.schema';
import { UserUncheckedCreateWithoutProcessedRefundsInputObjectSchema } from './UserUncheckedCreateWithoutProcessedRefundsInput.schema';
import { UserCreateOrConnectWithoutProcessedRefundsInputObjectSchema } from './UserCreateOrConnectWithoutProcessedRefundsInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutProcessedRefundsInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutProcessedRefundsInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutProcessedRefundsInputObjectSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional()
}).strict();
export const UserCreateNestedOneWithoutProcessedRefundsInputObjectSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutProcessedRefundsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateNestedOneWithoutProcessedRefundsInput>;
export const UserCreateNestedOneWithoutProcessedRefundsInputObjectZodSchema = makeSchema();
