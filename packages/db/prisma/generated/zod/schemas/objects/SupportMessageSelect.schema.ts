import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketArgsObjectSchema } from './SupportTicketArgs.schema';
import { UserArgsObjectSchema } from './UserArgs.schema'

const makeSchema = () => z.object({
  id: z.boolean().optional(),
  ticketId: z.boolean().optional(),
  ticket: z.union([z.boolean(), z.lazy(() => SupportTicketArgsObjectSchema)]).optional(),
  message: z.boolean().optional(),
  isInternal: z.boolean().optional(),
  authorEmail: z.boolean().optional(),
  authorId: z.boolean().optional(),
  authorUser: z.union([z.boolean(), z.lazy(() => UserArgsObjectSchema)]).optional(),
  createdAt: z.boolean().optional()
}).strict();
export const SupportMessageSelectObjectSchema: z.ZodType<Prisma.SupportMessageSelect> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageSelect>;
export const SupportMessageSelectObjectZodSchema = makeSchema();
