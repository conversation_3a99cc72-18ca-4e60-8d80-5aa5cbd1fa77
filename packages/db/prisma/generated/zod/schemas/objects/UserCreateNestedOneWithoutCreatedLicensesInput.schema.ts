import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutCreatedLicensesInputObjectSchema } from './UserCreateWithoutCreatedLicensesInput.schema';
import { UserUncheckedCreateWithoutCreatedLicensesInputObjectSchema } from './UserUncheckedCreateWithoutCreatedLicensesInput.schema';
import { UserCreateOrConnectWithoutCreatedLicensesInputObjectSchema } from './UserCreateOrConnectWithoutCreatedLicensesInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutCreatedLicensesInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutCreatedLicensesInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutCreatedLicensesInputObjectSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional()
}).strict();
export const UserCreateNestedOneWithoutCreatedLicensesInputObjectSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutCreatedLicensesInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateNestedOneWithoutCreatedLicensesInput>;
export const UserCreateNestedOneWithoutCreatedLicensesInputObjectZodSchema = makeSchema();
