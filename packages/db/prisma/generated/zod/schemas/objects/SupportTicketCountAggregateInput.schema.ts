import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.literal(true).optional(),
  ticketId: z.literal(true).optional(),
  subject: z.literal(true).optional(),
  description: z.literal(true).optional(),
  status: z.literal(true).optional(),
  priority: z.literal(true).optional(),
  category: z.literal(true).optional(),
  customerEmail: z.literal(true).optional(),
  customerName: z.literal(true).optional(),
  licenseKey: z.literal(true).optional(),
  assignedTo: z.literal(true).optional(),
  createdAt: z.literal(true).optional(),
  updatedAt: z.literal(true).optional(),
  resolvedAt: z.literal(true).optional(),
  _all: z.literal(true).optional()
}).strict();
export const SupportTicketCountAggregateInputObjectSchema: z.ZodType<Prisma.SupportTicketCountAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketCountAggregateInputType>;
export const SupportTicketCountAggregateInputObjectZodSchema = makeSchema();
