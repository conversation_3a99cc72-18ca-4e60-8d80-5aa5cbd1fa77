import { z } from 'zod';
import type { Prisma } from '../../../client';
import { LicenseTypeSchema } from '../enums/LicenseType.schema';
import { NestedEnumLicenseTypeWithAggregatesFilterObjectSchema } from './NestedEnumLicenseTypeWithAggregatesFilter.schema';
import { NestedIntFilterObjectSchema } from './NestedIntFilter.schema';
import { NestedEnumLicenseTypeFilterObjectSchema } from './NestedEnumLicenseTypeFilter.schema'

const makeSchema = () => z.object({
  equals: LicenseTypeSchema.optional(),
  in: LicenseTypeSchema.array().optional(),
  notIn: LicenseTypeSchema.array().optional(),
  not: z.union([LicenseTypeSchema, z.lazy(() => NestedEnumLicenseTypeWithAggregatesFilterObjectSchema)]).optional(),
  _count: z.lazy(() => NestedIntFilterObjectSchema).optional(),
  _min: z.lazy(() => NestedEnumLicenseTypeFilterObjectSchema).optional(),
  _max: z.lazy(() => NestedEnumLicenseTypeFilterObjectSchema).optional()
}).strict();
export const EnumLicenseTypeWithAggregatesFilterObjectSchema: z.ZodType<Prisma.EnumLicenseTypeWithAggregatesFilter> = makeSchema() as unknown as z.ZodType<Prisma.EnumLicenseTypeWithAggregatesFilter>;
export const EnumLicenseTypeWithAggregatesFilterObjectZodSchema = makeSchema();
