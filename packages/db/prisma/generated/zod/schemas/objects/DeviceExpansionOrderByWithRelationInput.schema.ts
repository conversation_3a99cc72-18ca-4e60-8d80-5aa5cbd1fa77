import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { LicenseOrderByWithRelationInputObjectSchema } from './LicenseOrderByWithRelationInput.schema';
import { PaymentIntentOrderByWithRelationInputObjectSchema } from './PaymentIntentOrderByWithRelationInput.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  licenseId: SortOrderSchema.optional(),
  paymentIntentId: SortOrderSchema.optional(),
  additionalDevices: SortOrderSchema.optional(),
  amount: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  createdAt: SortOrderSchema.optional(),
  processedAt: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  license: z.lazy(() => LicenseOrderByWithRelationInputObjectSchema).optional(),
  paymentIntent: z.lazy(() => PaymentIntentOrderByWithRelationInputObjectSchema).optional()
}).strict();
export const DeviceExpansionOrderByWithRelationInputObjectSchema: z.ZodType<Prisma.DeviceExpansionOrderByWithRelationInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionOrderByWithRelationInput>;
export const DeviceExpansionOrderByWithRelationInputObjectZodSchema = makeSchema();
