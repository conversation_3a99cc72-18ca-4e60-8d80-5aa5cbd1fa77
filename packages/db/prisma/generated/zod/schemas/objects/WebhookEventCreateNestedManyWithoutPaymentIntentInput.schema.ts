import { z } from 'zod';
import type { Prisma } from '../../../client';
import { WebhookEventCreateWithoutPaymentIntentInputObjectSchema } from './WebhookEventCreateWithoutPaymentIntentInput.schema';
import { WebhookEventUncheckedCreateWithoutPaymentIntentInputObjectSchema } from './WebhookEventUncheckedCreateWithoutPaymentIntentInput.schema';
import { WebhookEventCreateOrConnectWithoutPaymentIntentInputObjectSchema } from './WebhookEventCreateOrConnectWithoutPaymentIntentInput.schema';
import { WebhookEventCreateManyPaymentIntentInputEnvelopeObjectSchema } from './WebhookEventCreateManyPaymentIntentInputEnvelope.schema';
import { WebhookEventWhereUniqueInputObjectSchema } from './WebhookEventWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => WebhookEventCreateWithoutPaymentIntentInputObjectSchema), z.lazy(() => WebhookEventCreateWithoutPaymentIntentInputObjectSchema).array(), z.lazy(() => WebhookEventUncheckedCreateWithoutPaymentIntentInputObjectSchema), z.lazy(() => WebhookEventUncheckedCreateWithoutPaymentIntentInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => WebhookEventCreateOrConnectWithoutPaymentIntentInputObjectSchema), z.lazy(() => WebhookEventCreateOrConnectWithoutPaymentIntentInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => WebhookEventCreateManyPaymentIntentInputEnvelopeObjectSchema).optional(),
  connect: z.union([z.lazy(() => WebhookEventWhereUniqueInputObjectSchema), z.lazy(() => WebhookEventWhereUniqueInputObjectSchema).array()]).optional()
}).strict();
export const WebhookEventCreateNestedManyWithoutPaymentIntentInputObjectSchema: z.ZodType<Prisma.WebhookEventCreateNestedManyWithoutPaymentIntentInput> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventCreateNestedManyWithoutPaymentIntentInput>;
export const WebhookEventCreateNestedManyWithoutPaymentIntentInputObjectZodSchema = makeSchema();
