import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestCreateWithoutLicenseInputObjectSchema } from './RefundRequestCreateWithoutLicenseInput.schema';
import { RefundRequestUncheckedCreateWithoutLicenseInputObjectSchema } from './RefundRequestUncheckedCreateWithoutLicenseInput.schema';
import { RefundRequestCreateOrConnectWithoutLicenseInputObjectSchema } from './RefundRequestCreateOrConnectWithoutLicenseInput.schema';
import { RefundRequestCreateManyLicenseInputEnvelopeObjectSchema } from './RefundRequestCreateManyLicenseInputEnvelope.schema';
import { RefundRequestWhereUniqueInputObjectSchema } from './RefundRequestWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => RefundRequestCreateWithoutLicenseInputObjectSchema), z.lazy(() => RefundRequestCreateWithoutLicenseInputObjectSchema).array(), z.lazy(() => RefundRequestUncheckedCreateWithoutLicenseInputObjectSchema), z.lazy(() => RefundRequestUncheckedCreateWithoutLicenseInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => RefundRequestCreateOrConnectWithoutLicenseInputObjectSchema), z.lazy(() => RefundRequestCreateOrConnectWithoutLicenseInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => RefundRequestCreateManyLicenseInputEnvelopeObjectSchema).optional(),
  connect: z.union([z.lazy(() => RefundRequestWhereUniqueInputObjectSchema), z.lazy(() => RefundRequestWhereUniqueInputObjectSchema).array()]).optional()
}).strict();
export const RefundRequestUncheckedCreateNestedManyWithoutLicenseInputObjectSchema: z.ZodType<Prisma.RefundRequestUncheckedCreateNestedManyWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestUncheckedCreateNestedManyWithoutLicenseInput>;
export const RefundRequestUncheckedCreateNestedManyWithoutLicenseInputObjectZodSchema = makeSchema();
