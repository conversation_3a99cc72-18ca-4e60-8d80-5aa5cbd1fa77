import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.literal(true).optional(),
  ticketId: z.literal(true).optional(),
  message: z.literal(true).optional(),
  isInternal: z.literal(true).optional(),
  authorEmail: z.literal(true).optional(),
  authorId: z.literal(true).optional(),
  createdAt: z.literal(true).optional()
}).strict();
export const SupportMessageMinAggregateInputObjectSchema: z.ZodType<Prisma.SupportMessageMinAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageMinAggregateInputType>;
export const SupportMessageMinAggregateInputObjectZodSchema = makeSchema();
