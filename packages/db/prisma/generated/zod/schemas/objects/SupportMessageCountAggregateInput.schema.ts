import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.literal(true).optional(),
  ticketId: z.literal(true).optional(),
  message: z.literal(true).optional(),
  isInternal: z.literal(true).optional(),
  authorEmail: z.literal(true).optional(),
  authorId: z.literal(true).optional(),
  createdAt: z.literal(true).optional(),
  _all: z.literal(true).optional()
}).strict();
export const SupportMessageCountAggregateInputObjectSchema: z.ZodType<Prisma.SupportMessageCountAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageCountAggregateInputType>;
export const SupportMessageCountAggregateInputObjectZodSchema = makeSchema();
