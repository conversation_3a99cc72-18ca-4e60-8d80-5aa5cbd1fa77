import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { RateLimitCountOrderByAggregateInputObjectSchema } from './RateLimitCountOrderByAggregateInput.schema';
import { RateLimitAvgOrderByAggregateInputObjectSchema } from './RateLimitAvgOrderByAggregateInput.schema';
import { RateLimitMaxOrderByAggregateInputObjectSchema } from './RateLimitMaxOrderByAggregateInput.schema';
import { RateLimitMinOrderByAggregateInputObjectSchema } from './RateLimitMinOrderByAggregateInput.schema';
import { RateLimitSumOrderByAggregateInputObjectSchema } from './RateLimitSumOrderByAggregateInput.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  identifier: SortOrderSchema.optional(),
  action: SortOrderSchema.optional(),
  count: SortOrderSchema.optional(),
  windowStart: SortOrderSchema.optional(),
  expiresAt: SortOrderSchema.optional(),
  _count: z.lazy(() => RateLimitCountOrderByAggregateInputObjectSchema).optional(),
  _avg: z.lazy(() => RateLimitAvgOrderByAggregateInputObjectSchema).optional(),
  _max: z.lazy(() => RateLimitMaxOrderByAggregateInputObjectSchema).optional(),
  _min: z.lazy(() => RateLimitMinOrderByAggregateInputObjectSchema).optional(),
  _sum: z.lazy(() => RateLimitSumOrderByAggregateInputObjectSchema).optional()
}).strict();
export const RateLimitOrderByWithAggregationInputObjectSchema: z.ZodType<Prisma.RateLimitOrderByWithAggregationInput> = makeSchema() as unknown as z.ZodType<Prisma.RateLimitOrderByWithAggregationInput>;
export const RateLimitOrderByWithAggregationInputObjectZodSchema = makeSchema();
