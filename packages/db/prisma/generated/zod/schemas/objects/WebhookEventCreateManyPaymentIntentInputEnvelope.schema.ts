import { z } from 'zod';
import type { Prisma } from '../../../client';
import { WebhookEventCreateManyPaymentIntentInputObjectSchema } from './WebhookEventCreateManyPaymentIntentInput.schema'

const makeSchema = () => z.object({
  data: z.union([z.lazy(() => WebhookEventCreateManyPaymentIntentInputObjectSchema), z.lazy(() => WebhookEventCreateManyPaymentIntentInputObjectSchema).array()]),
  skipDuplicates: z.boolean().optional()
}).strict();
export const WebhookEventCreateManyPaymentIntentInputEnvelopeObjectSchema: z.ZodType<Prisma.WebhookEventCreateManyPaymentIntentInputEnvelope> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventCreateManyPaymentIntentInputEnvelope>;
export const WebhookEventCreateManyPaymentIntentInputEnvelopeObjectZodSchema = makeSchema();
