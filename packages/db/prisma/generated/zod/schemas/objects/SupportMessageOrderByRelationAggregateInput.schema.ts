import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  _count: SortOrderSchema.optional()
}).strict();
export const SupportMessageOrderByRelationAggregateInputObjectSchema: z.ZodType<Prisma.SupportMessageOrderByRelationAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageOrderByRelationAggregateInput>;
export const SupportMessageOrderByRelationAggregateInputObjectZodSchema = makeSchema();
