import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.string().optional(),
  message: z.string(),
  isInternal: z.boolean().optional(),
  authorEmail: z.string().optional().nullable(),
  authorId: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional()
}).strict();
export const SupportMessageUncheckedCreateWithoutTicketInputObjectSchema: z.ZodType<Prisma.SupportMessageUncheckedCreateWithoutTicketInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageUncheckedCreateWithoutTicketInput>;
export const SupportMessageUncheckedCreateWithoutTicketInputObjectZodSchema = makeSchema();
