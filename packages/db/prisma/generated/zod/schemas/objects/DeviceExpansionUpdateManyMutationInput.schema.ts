import { z } from 'zod';
import type { <PERSON>risma } from '../../../client';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { DeviceExpansionStatusSchema } from '../enums/DeviceExpansionStatus.schema';
import { EnumDeviceExpansionStatusFieldUpdateOperationsInputObjectSchema } from './EnumDeviceExpansionStatusFieldUpdateOperationsInput.schema';
import { NullableDateTimeFieldUpdateOperationsInputObjectSchema } from './NullableDateTimeFieldUpdateOperationsInput.schema'

const makeSchema = () => z.object({
  additionalDevices: z.union([z.number().int(), z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(),
  amount: z.union([z.number().int(), z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(),
  status: z.union([DeviceExpansionStatusSchema, z.lazy(() => EnumDeviceExpansionStatusFieldUpdateOperationsInputObjectSchema)]).optional(),
  processedAt: z.union([z.coerce.date(), z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema)]).optional().nullable()
}).strict();
export const DeviceExpansionUpdateManyMutationInputObjectSchema: z.ZodType<Prisma.DeviceExpansionUpdateManyMutationInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionUpdateManyMutationInput>;
export const DeviceExpansionUpdateManyMutationInputObjectZodSchema = makeSchema();
