import { z } from 'zod';
import type { Prisma } from '../../../client';
import { LicenseSelectObjectSchema } from './LicenseSelect.schema';
import { LicenseIncludeObjectSchema } from './LicenseInclude.schema'

const makeSchema = () => z.object({
  select: z.lazy(() => LicenseSelectObjectSchema).optional(),
  include: z.lazy(() => LicenseIncludeObjectSchema).optional()
}).strict();
export const LicenseArgsObjectSchema = makeSchema();
export const LicenseArgsObjectZodSchema = makeSchema();
