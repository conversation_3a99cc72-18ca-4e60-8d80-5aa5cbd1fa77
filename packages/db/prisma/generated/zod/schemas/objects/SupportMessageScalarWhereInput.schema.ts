import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { BoolFilterObjectSchema } from './BoolFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema'

const supportmessagescalarwhereinputSchema = z.object({
  AND: z.union([z.lazy(() => SupportMessageScalarWhereInputObjectSchema), z.lazy(() => SupportMessageScalarWhereInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => SupportMessageScalarWhereInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => SupportMessageScalarWhereInputObjectSchema), z.lazy(() => SupportMessageScalarWhereInputObjectSchema).array()]).optional(),
  ticketId: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  message: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  isInternal: z.union([z.lazy(() => BoolFilterObjectSchema), z.boolean()]).optional(),
  authorEmail: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  authorId: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable()
}).strict();
export const SupportMessageScalarWhereInputObjectSchema: z.ZodType<Prisma.SupportMessageScalarWhereInput> = supportmessagescalarwhereinputSchema as unknown as z.ZodType<Prisma.SupportMessageScalarWhereInput>;
export const SupportMessageScalarWhereInputObjectZodSchema = supportmessagescalarwhereinputSchema;
