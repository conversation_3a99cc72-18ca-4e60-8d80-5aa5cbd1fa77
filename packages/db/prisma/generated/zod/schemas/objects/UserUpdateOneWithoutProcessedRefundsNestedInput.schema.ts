import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutProcessedRefundsInputObjectSchema } from './UserCreateWithoutProcessedRefundsInput.schema';
import { UserUncheckedCreateWithoutProcessedRefundsInputObjectSchema } from './UserUncheckedCreateWithoutProcessedRefundsInput.schema';
import { UserCreateOrConnectWithoutProcessedRefundsInputObjectSchema } from './UserCreateOrConnectWithoutProcessedRefundsInput.schema';
import { UserUpsertWithoutProcessedRefundsInputObjectSchema } from './UserUpsertWithoutProcessedRefundsInput.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserUpdateToOneWithWhereWithoutProcessedRefundsInputObjectSchema } from './UserUpdateToOneWithWhereWithoutProcessedRefundsInput.schema';
import { UserUpdateWithoutProcessedRefundsInputObjectSchema } from './UserUpdateWithoutProcessedRefundsInput.schema';
import { UserUncheckedUpdateWithoutProcessedRefundsInputObjectSchema } from './UserUncheckedUpdateWithoutProcessedRefundsInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutProcessedRefundsInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutProcessedRefundsInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutProcessedRefundsInputObjectSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutProcessedRefundsInputObjectSchema).optional(),
  disconnect: z.union([z.boolean(), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  delete: z.union([z.boolean(), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional(),
  update: z.union([z.lazy(() => UserUpdateToOneWithWhereWithoutProcessedRefundsInputObjectSchema), z.lazy(() => UserUpdateWithoutProcessedRefundsInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutProcessedRefundsInputObjectSchema)]).optional()
}).strict();
export const UserUpdateOneWithoutProcessedRefundsNestedInputObjectSchema: z.ZodType<Prisma.UserUpdateOneWithoutProcessedRefundsNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateOneWithoutProcessedRefundsNestedInput>;
export const UserUpdateOneWithoutProcessedRefundsNestedInputObjectZodSchema = makeSchema();
