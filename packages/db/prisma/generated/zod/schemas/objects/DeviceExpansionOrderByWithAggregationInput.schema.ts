import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { DeviceExpansionCountOrderByAggregateInputObjectSchema } from './DeviceExpansionCountOrderByAggregateInput.schema';
import { DeviceExpansionAvgOrderByAggregateInputObjectSchema } from './DeviceExpansionAvgOrderByAggregateInput.schema';
import { DeviceExpansionMaxOrderByAggregateInputObjectSchema } from './DeviceExpansionMaxOrderByAggregateInput.schema';
import { DeviceExpansionMinOrderByAggregateInputObjectSchema } from './DeviceExpansionMinOrderByAggregateInput.schema';
import { DeviceExpansionSumOrderByAggregateInputObjectSchema } from './DeviceExpansionSumOrderByAggregateInput.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  licenseId: SortOrderSchema.optional(),
  paymentIntentId: SortOrderSchema.optional(),
  additionalDevices: SortOrderSchema.optional(),
  amount: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  createdAt: SortOrderSchema.optional(),
  processedAt: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  _count: z.lazy(() => DeviceExpansionCountOrderByAggregateInputObjectSchema).optional(),
  _avg: z.lazy(() => DeviceExpansionAvgOrderByAggregateInputObjectSchema).optional(),
  _max: z.lazy(() => DeviceExpansionMaxOrderByAggregateInputObjectSchema).optional(),
  _min: z.lazy(() => DeviceExpansionMinOrderByAggregateInputObjectSchema).optional(),
  _sum: z.lazy(() => DeviceExpansionSumOrderByAggregateInputObjectSchema).optional()
}).strict();
export const DeviceExpansionOrderByWithAggregationInputObjectSchema: z.ZodType<Prisma.DeviceExpansionOrderByWithAggregationInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionOrderByWithAggregationInput>;
export const DeviceExpansionOrderByWithAggregationInputObjectZodSchema = makeSchema();
