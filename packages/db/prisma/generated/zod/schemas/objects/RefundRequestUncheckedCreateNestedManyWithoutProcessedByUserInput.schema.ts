import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestCreateWithoutProcessedByUserInputObjectSchema } from './RefundRequestCreateWithoutProcessedByUserInput.schema';
import { RefundRequestUncheckedCreateWithoutProcessedByUserInputObjectSchema } from './RefundRequestUncheckedCreateWithoutProcessedByUserInput.schema';
import { RefundRequestCreateOrConnectWithoutProcessedByUserInputObjectSchema } from './RefundRequestCreateOrConnectWithoutProcessedByUserInput.schema';
import { RefundRequestCreateManyProcessedByUserInputEnvelopeObjectSchema } from './RefundRequestCreateManyProcessedByUserInputEnvelope.schema';
import { RefundRequestWhereUniqueInputObjectSchema } from './RefundRequestWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => RefundRequestCreateWithoutProcessedByUserInputObjectSchema), z.lazy(() => RefundRequestCreateWithoutProcessedByUserInputObjectSchema).array(), z.lazy(() => RefundRequestUncheckedCreateWithoutProcessedByUserInputObjectSchema), z.lazy(() => RefundRequestUncheckedCreateWithoutProcessedByUserInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => RefundRequestCreateOrConnectWithoutProcessedByUserInputObjectSchema), z.lazy(() => RefundRequestCreateOrConnectWithoutProcessedByUserInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => RefundRequestCreateManyProcessedByUserInputEnvelopeObjectSchema).optional(),
  connect: z.union([z.lazy(() => RefundRequestWhereUniqueInputObjectSchema), z.lazy(() => RefundRequestWhereUniqueInputObjectSchema).array()]).optional()
}).strict();
export const RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInputObjectSchema: z.ZodType<Prisma.RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput>;
export const RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInputObjectZodSchema = makeSchema();
