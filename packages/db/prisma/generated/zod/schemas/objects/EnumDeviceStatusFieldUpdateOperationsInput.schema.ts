import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceStatusSchema } from '../enums/DeviceStatus.schema'

const makeSchema = () => z.object({
  set: DeviceStatusSchema.optional()
}).strict();
export const EnumDeviceStatusFieldUpdateOperationsInputObjectSchema: z.ZodType<Prisma.EnumDeviceStatusFieldUpdateOperationsInput> = makeSchema() as unknown as z.ZodType<Prisma.EnumDeviceStatusFieldUpdateOperationsInput>;
export const EnumDeviceStatusFieldUpdateOperationsInputObjectZodSchema = makeSchema();
