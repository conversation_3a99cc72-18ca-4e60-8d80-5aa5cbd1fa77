import { z } from 'zod';
import type { Prisma } from '../../../client';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { DeviceExpansionStatusSchema } from '../enums/DeviceExpansionStatus.schema';
import { EnumDeviceExpansionStatusFieldUpdateOperationsInputObjectSchema } from './EnumDeviceExpansionStatusFieldUpdateOperationsInput.schema';
import { NullableDateTimeFieldUpdateOperationsInputObjectSchema } from './NullableDateTimeFieldUpdateOperationsInput.schema';
import { LicenseUpdateOneRequiredWithoutDeviceExpansionsNestedInputObjectSchema } from './LicenseUpdateOneRequiredWithoutDeviceExpansionsNestedInput.schema'

const makeSchema = () => z.object({
  additionalDevices: z.union([z.number().int(), z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(),
  amount: z.union([z.number().int(), z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(),
  status: z.union([DeviceExpansionStatusSchema, z.lazy(() => EnumDeviceExpansionStatusFieldUpdateOperationsInputObjectSchema)]).optional(),
  processedAt: z.union([z.coerce.date(), z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  license: z.lazy(() => LicenseUpdateOneRequiredWithoutDeviceExpansionsNestedInputObjectSchema).optional()
}).strict();
export const DeviceExpansionUpdateWithoutPaymentIntentInputObjectSchema: z.ZodType<Prisma.DeviceExpansionUpdateWithoutPaymentIntentInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionUpdateWithoutPaymentIntentInput>;
export const DeviceExpansionUpdateWithoutPaymentIntentInputObjectZodSchema = makeSchema();
