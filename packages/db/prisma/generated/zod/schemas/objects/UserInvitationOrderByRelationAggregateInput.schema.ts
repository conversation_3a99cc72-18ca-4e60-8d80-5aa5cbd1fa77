import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  _count: SortOrderSchema.optional()
}).strict();
export const UserInvitationOrderByRelationAggregateInputObjectSchema: z.ZodType<Prisma.UserInvitationOrderByRelationAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationOrderByRelationAggregateInput>;
export const UserInvitationOrderByRelationAggregateInputObjectZodSchema = makeSchema();
