import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  _count: SortOrderSchema.optional()
}).strict();
export const RefundRequestOrderByRelationAggregateInputObjectSchema: z.ZodType<Prisma.RefundRequestOrderByRelationAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestOrderByRelationAggregateInput>;
export const RefundRequestOrderByRelationAggregateInputObjectZodSchema = makeSchema();
