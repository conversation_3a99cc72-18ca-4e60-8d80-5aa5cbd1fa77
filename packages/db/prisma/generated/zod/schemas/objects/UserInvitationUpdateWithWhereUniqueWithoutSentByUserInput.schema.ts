import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationWhereUniqueInputObjectSchema } from './UserInvitationWhereUniqueInput.schema';
import { UserInvitationUpdateWithoutSentByUserInputObjectSchema } from './UserInvitationUpdateWithoutSentByUserInput.schema';
import { UserInvitationUncheckedUpdateWithoutSentByUserInputObjectSchema } from './UserInvitationUncheckedUpdateWithoutSentByUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserInvitationWhereUniqueInputObjectSchema),
  data: z.union([z.lazy(() => UserInvitationUpdateWithoutSentByUserInputObjectSchema), z.lazy(() => UserInvitationUncheckedUpdateWithoutSentByUserInputObjectSchema)])
}).strict();
export const UserInvitationUpdateWithWhereUniqueWithoutSentByUserInputObjectSchema: z.ZodType<Prisma.UserInvitationUpdateWithWhereUniqueWithoutSentByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationUpdateWithWhereUniqueWithoutSentByUserInput>;
export const UserInvitationUpdateWithWhereUniqueWithoutSentByUserInputObjectZodSchema = makeSchema();
