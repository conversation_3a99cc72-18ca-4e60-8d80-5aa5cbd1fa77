import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserRoleSchema } from '../enums/UserRole.schema';
import { SessionCreateNestedManyWithoutUserInputObjectSchema } from './SessionCreateNestedManyWithoutUserInput.schema';
import { AccountCreateNestedManyWithoutUserInputObjectSchema } from './AccountCreateNestedManyWithoutUserInput.schema';
import { LicenseCreateNestedManyWithoutCreatedByUserInputObjectSchema } from './LicenseCreateNestedManyWithoutCreatedByUserInput.schema';
import { UserInvitationCreateNestedManyWithoutSentByUserInputObjectSchema } from './UserInvitationCreateNestedManyWithoutSentByUserInput.schema';
import { UserInvitationCreateNestedManyWithoutAcceptedByUserInputObjectSchema } from './UserInvitationCreateNestedManyWithoutAcceptedByUserInput.schema';
import { AuditLogCreateNestedManyWithoutUserInputObjectSchema } from './AuditLogCreateNestedManyWithoutUserInput.schema';
import { AuditLogCreateNestedManyWithoutTargetInputObjectSchema } from './AuditLogCreateNestedManyWithoutTargetInput.schema';
import { SupportTicketCreateNestedManyWithoutAssignedToUserInputObjectSchema } from './SupportTicketCreateNestedManyWithoutAssignedToUserInput.schema';
import { SupportMessageCreateNestedManyWithoutAuthorUserInputObjectSchema } from './SupportMessageCreateNestedManyWithoutAuthorUserInput.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  name: z.string(),
  email: z.string(),
  emailVerified: z.boolean().optional(),
  image: z.string().optional().nullable(),
  role: UserRoleSchema.optional(),
  isActive: z.boolean().optional(),
  invitedBy: z.string().optional().nullable(),
  invitedAt: z.coerce.date().optional().nullable(),
  lastLoginAt: z.coerce.date().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  sessions: z.lazy(() => SessionCreateNestedManyWithoutUserInputObjectSchema).optional(),
  accounts: z.lazy(() => AccountCreateNestedManyWithoutUserInputObjectSchema).optional(),
  createdLicenses: z.lazy(() => LicenseCreateNestedManyWithoutCreatedByUserInputObjectSchema).optional(),
  sentInvitations: z.lazy(() => UserInvitationCreateNestedManyWithoutSentByUserInputObjectSchema).optional(),
  receivedInvitations: z.lazy(() => UserInvitationCreateNestedManyWithoutAcceptedByUserInputObjectSchema).optional(),
  auditLogsAsActor: z.lazy(() => AuditLogCreateNestedManyWithoutUserInputObjectSchema).optional(),
  auditLogsAsTarget: z.lazy(() => AuditLogCreateNestedManyWithoutTargetInputObjectSchema).optional(),
  assignedTickets: z.lazy(() => SupportTicketCreateNestedManyWithoutAssignedToUserInputObjectSchema).optional(),
  supportMessages: z.lazy(() => SupportMessageCreateNestedManyWithoutAuthorUserInputObjectSchema).optional()
}).strict();
export const UserCreateWithoutProcessedRefundsInputObjectSchema: z.ZodType<Prisma.UserCreateWithoutProcessedRefundsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateWithoutProcessedRefundsInput>;
export const UserCreateWithoutProcessedRefundsInputObjectZodSchema = makeSchema();
