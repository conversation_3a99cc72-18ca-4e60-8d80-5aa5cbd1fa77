import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceCreateWithoutLicenseInputObjectSchema } from './DeviceCreateWithoutLicenseInput.schema';
import { DeviceUncheckedCreateWithoutLicenseInputObjectSchema } from './DeviceUncheckedCreateWithoutLicenseInput.schema';
import { DeviceCreateOrConnectWithoutLicenseInputObjectSchema } from './DeviceCreateOrConnectWithoutLicenseInput.schema';
import { DeviceCreateManyLicenseInputEnvelopeObjectSchema } from './DeviceCreateManyLicenseInputEnvelope.schema';
import { DeviceWhereUniqueInputObjectSchema } from './DeviceWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => DeviceCreateWithoutLicenseInputObjectSchema), z.lazy(() => DeviceCreateWithoutLicenseInputObjectSchema).array(), z.lazy(() => DeviceUncheckedCreateWithoutLicenseInputObjectSchema), z.lazy(() => DeviceUncheckedCreateWithoutLicenseInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => DeviceCreateOrConnectWithoutLicenseInputObjectSchema), z.lazy(() => DeviceCreateOrConnectWithoutLicenseInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => DeviceCreateManyLicenseInputEnvelopeObjectSchema).optional(),
  connect: z.union([z.lazy(() => DeviceWhereUniqueInputObjectSchema), z.lazy(() => DeviceWhereUniqueInputObjectSchema).array()]).optional()
}).strict();
export const DeviceUncheckedCreateNestedManyWithoutLicenseInputObjectSchema: z.ZodType<Prisma.DeviceUncheckedCreateNestedManyWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceUncheckedCreateNestedManyWithoutLicenseInput>;
export const DeviceUncheckedCreateNestedManyWithoutLicenseInputObjectZodSchema = makeSchema();
