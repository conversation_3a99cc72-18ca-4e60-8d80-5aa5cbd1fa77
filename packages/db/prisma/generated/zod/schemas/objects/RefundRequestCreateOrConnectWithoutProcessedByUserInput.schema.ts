import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestWhereUniqueInputObjectSchema } from './RefundRequestWhereUniqueInput.schema';
import { RefundRequestCreateWithoutProcessedByUserInputObjectSchema } from './RefundRequestCreateWithoutProcessedByUserInput.schema';
import { RefundRequestUncheckedCreateWithoutProcessedByUserInputObjectSchema } from './RefundRequestUncheckedCreateWithoutProcessedByUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => RefundRequestWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => RefundRequestCreateWithoutProcessedByUserInputObjectSchema), z.lazy(() => RefundRequestUncheckedCreateWithoutProcessedByUserInputObjectSchema)])
}).strict();
export const RefundRequestCreateOrConnectWithoutProcessedByUserInputObjectSchema: z.ZodType<Prisma.RefundRequestCreateOrConnectWithoutProcessedByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestCreateOrConnectWithoutProcessedByUserInput>;
export const RefundRequestCreateOrConnectWithoutProcessedByUserInputObjectZodSchema = makeSchema();
