import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserArgsObjectSchema } from './UserArgs.schema'

const makeSchema = () => z.object({
  sentByUser: z.union([z.boolean(), z.lazy(() => UserArgsObjectSchema)]).optional(),
  acceptedByUser: z.union([z.boolean(), z.lazy(() => UserArgsObjectSchema)]).optional()
}).strict();
export const UserInvitationIncludeObjectSchema: z.ZodType<Prisma.UserInvitationInclude> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationInclude>;
export const UserInvitationIncludeObjectZodSchema = makeSchema();
