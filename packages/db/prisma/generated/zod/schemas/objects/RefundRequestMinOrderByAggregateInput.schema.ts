import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  licenseId: SortOrderSchema.optional(),
  requestedBy: SortOrderSchema.optional(),
  reason: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  requestedAmount: SortOrderSchema.optional(),
  approvedAmount: SortOrderSchema.optional(),
  adminNotes: SortOrderSchema.optional(),
  processedBy: SortOrderSchema.optional(),
  createdAt: SortOrderSchema.optional(),
  updatedAt: SortOrderSchema.optional(),
  processedAt: SortOrderSchema.optional()
}).strict();
export const RefundRequestMinOrderByAggregateInputObjectSchema: z.ZodType<Prisma.RefundRequestMinOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestMinOrderByAggregateInput>;
export const RefundRequestMinOrderByAggregateInputObjectZodSchema = makeSchema();
