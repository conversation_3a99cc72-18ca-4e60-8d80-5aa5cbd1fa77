import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { SupportTicketCountOrderByAggregateInputObjectSchema } from './SupportTicketCountOrderByAggregateInput.schema';
import { SupportTicketMaxOrderByAggregateInputObjectSchema } from './SupportTicketMaxOrderByAggregateInput.schema';
import { SupportTicketMinOrderByAggregateInputObjectSchema } from './SupportTicketMinOrderByAggregateInput.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  ticketId: SortOrderSchema.optional(),
  subject: SortOrderSchema.optional(),
  description: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  priority: SortOrderSchema.optional(),
  category: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  customerEmail: SortOrderSchema.optional(),
  customerName: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  licenseKey: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  assignedTo: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  createdAt: SortOrderSchema.optional(),
  updatedAt: SortOrderSchema.optional(),
  resolvedAt: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  _count: z.lazy(() => SupportTicketCountOrderByAggregateInputObjectSchema).optional(),
  _max: z.lazy(() => SupportTicketMaxOrderByAggregateInputObjectSchema).optional(),
  _min: z.lazy(() => SupportTicketMinOrderByAggregateInputObjectSchema).optional()
}).strict();
export const SupportTicketOrderByWithAggregationInputObjectSchema: z.ZodType<Prisma.SupportTicketOrderByWithAggregationInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketOrderByWithAggregationInput>;
export const SupportTicketOrderByWithAggregationInputObjectZodSchema = makeSchema();
