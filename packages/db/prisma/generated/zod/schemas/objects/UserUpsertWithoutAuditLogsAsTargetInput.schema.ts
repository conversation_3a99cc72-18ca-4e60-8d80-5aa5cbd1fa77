import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserUpdateWithoutAuditLogsAsTargetInputObjectSchema } from './UserUpdateWithoutAuditLogsAsTargetInput.schema';
import { UserUncheckedUpdateWithoutAuditLogsAsTargetInputObjectSchema } from './UserUncheckedUpdateWithoutAuditLogsAsTargetInput.schema';
import { UserCreateWithoutAuditLogsAsTargetInputObjectSchema } from './UserCreateWithoutAuditLogsAsTargetInput.schema';
import { UserUncheckedCreateWithoutAuditLogsAsTargetInputObjectSchema } from './UserUncheckedCreateWithoutAuditLogsAsTargetInput.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema'

const makeSchema = () => z.object({
  update: z.union([z.lazy(() => UserUpdateWithoutAuditLogsAsTargetInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutAuditLogsAsTargetInputObjectSchema)]),
  create: z.union([z.lazy(() => UserCreateWithoutAuditLogsAsTargetInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutAuditLogsAsTargetInputObjectSchema)]),
  where: z.lazy(() => UserWhereInputObjectSchema).optional()
}).strict();
export const UserUpsertWithoutAuditLogsAsTargetInputObjectSchema: z.ZodType<Prisma.UserUpsertWithoutAuditLogsAsTargetInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpsertWithoutAuditLogsAsTargetInput>;
export const UserUpsertWithoutAuditLogsAsTargetInputObjectZodSchema = makeSchema();
