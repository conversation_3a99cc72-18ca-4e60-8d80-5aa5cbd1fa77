import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { BoolFieldUpdateOperationsInputObjectSchema } from './BoolFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { UserUpdateOneWithoutSupportMessagesNestedInputObjectSchema } from './UserUpdateOneWithoutSupportMessagesNestedInput.schema'

const makeSchema = () => z.object({
  message: z.union([z.string(), z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(),
  isInternal: z.union([z.boolean(), z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(),
  authorEmail: z.union([z.string(), z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  authorUser: z.lazy(() => UserUpdateOneWithoutSupportMessagesNestedInputObjectSchema).optional()
}).strict();
export const SupportMessageUpdateWithoutTicketInputObjectSchema: z.ZodType<Prisma.SupportMessageUpdateWithoutTicketInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageUpdateWithoutTicketInput>;
export const SupportMessageUpdateWithoutTicketInputObjectZodSchema = makeSchema();
