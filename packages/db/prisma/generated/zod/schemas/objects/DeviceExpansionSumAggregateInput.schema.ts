import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  additionalDevices: z.literal(true).optional(),
  amount: z.literal(true).optional()
}).strict();
export const DeviceExpansionSumAggregateInputObjectSchema: z.ZodType<Prisma.DeviceExpansionSumAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionSumAggregateInputType>;
export const DeviceExpansionSumAggregateInputObjectZodSchema = makeSchema();
