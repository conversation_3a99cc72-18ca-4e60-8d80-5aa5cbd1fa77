import { z } from 'zod';
import type { Prisma } from '../../../client';
import { WebhookEventWhereUniqueInputObjectSchema } from './WebhookEventWhereUniqueInput.schema';
import { WebhookEventCreateWithoutPaymentIntentInputObjectSchema } from './WebhookEventCreateWithoutPaymentIntentInput.schema';
import { WebhookEventUncheckedCreateWithoutPaymentIntentInputObjectSchema } from './WebhookEventUncheckedCreateWithoutPaymentIntentInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => WebhookEventWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => WebhookEventCreateWithoutPaymentIntentInputObjectSchema), z.lazy(() => WebhookEventUncheckedCreateWithoutPaymentIntentInputObjectSchema)])
}).strict();
export const WebhookEventCreateOrConnectWithoutPaymentIntentInputObjectSchema: z.ZodType<Prisma.WebhookEventCreateOrConnectWithoutPaymentIntentInput> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventCreateOrConnectWithoutPaymentIntentInput>;
export const WebhookEventCreateOrConnectWithoutPaymentIntentInputObjectZodSchema = makeSchema();
