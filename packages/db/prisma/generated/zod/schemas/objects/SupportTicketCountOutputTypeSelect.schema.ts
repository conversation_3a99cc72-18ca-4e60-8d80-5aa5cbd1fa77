import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  messages: z.boolean().optional()
}).strict();
export const SupportTicketCountOutputTypeSelectObjectSchema: z.ZodType<Prisma.SupportTicketCountOutputTypeSelect> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketCountOutputTypeSelect>;
export const SupportTicketCountOutputTypeSelectObjectZodSchema = makeSchema();
