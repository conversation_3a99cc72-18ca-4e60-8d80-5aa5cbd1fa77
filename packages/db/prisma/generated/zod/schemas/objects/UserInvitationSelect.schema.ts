import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserArgsObjectSchema } from './UserArgs.schema'

const makeSchema = () => z.object({
  id: z.boolean().optional(),
  email: z.boolean().optional(),
  role: z.boolean().optional(),
  token: z.boolean().optional(),
  status: z.boolean().optional(),
  expiresAt: z.boolean().optional(),
  sentAt: z.boolean().optional(),
  acceptedAt: z.boolean().optional(),
  sentBy: z.boolean().optional(),
  sentByUser: z.union([z.boolean(), z.lazy(() => UserArgsObjectSchema)]).optional(),
  acceptedBy: z.boolean().optional(),
  acceptedByUser: z.union([z.boolean(), z.lazy(() => UserArgsObjectSchema)]).optional()
}).strict();
export const UserInvitationSelectObjectSchema: z.ZodType<Prisma.UserInvitationSelect> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationSelect>;
export const UserInvitationSelectObjectZodSchema = makeSchema();
