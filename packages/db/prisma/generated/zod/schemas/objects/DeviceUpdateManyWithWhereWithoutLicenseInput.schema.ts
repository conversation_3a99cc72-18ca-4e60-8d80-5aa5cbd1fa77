import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceScalarWhereInputObjectSchema } from './DeviceScalarWhereInput.schema';
import { DeviceUpdateManyMutationInputObjectSchema } from './DeviceUpdateManyMutationInput.schema';
import { DeviceUncheckedUpdateManyWithoutLicenseInputObjectSchema } from './DeviceUncheckedUpdateManyWithoutLicenseInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => DeviceScalarWhereInputObjectSchema),
  data: z.union([z.lazy(() => DeviceUpdateManyMutationInputObjectSchema), z.lazy(() => DeviceUncheckedUpdateManyWithoutLicenseInputObjectSchema)])
}).strict();
export const DeviceUpdateManyWithWhereWithoutLicenseInputObjectSchema: z.ZodType<Prisma.DeviceUpdateManyWithWhereWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceUpdateManyWithWhereWithoutLicenseInput>;
export const DeviceUpdateManyWithWhereWithoutLicenseInputObjectZodSchema = makeSchema();
