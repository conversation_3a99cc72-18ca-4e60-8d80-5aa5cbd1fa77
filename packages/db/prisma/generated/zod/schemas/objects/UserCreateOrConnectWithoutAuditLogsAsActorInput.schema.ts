import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserCreateWithoutAuditLogsAsActorInputObjectSchema } from './UserCreateWithoutAuditLogsAsActorInput.schema';
import { UserUncheckedCreateWithoutAuditLogsAsActorInputObjectSchema } from './UserUncheckedCreateWithoutAuditLogsAsActorInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => UserCreateWithoutAuditLogsAsActorInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutAuditLogsAsActorInputObjectSchema)])
}).strict();
export const UserCreateOrConnectWithoutAuditLogsAsActorInputObjectSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutAuditLogsAsActorInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateOrConnectWithoutAuditLogsAsActorInput>;
export const UserCreateOrConnectWithoutAuditLogsAsActorInputObjectZodSchema = makeSchema();
