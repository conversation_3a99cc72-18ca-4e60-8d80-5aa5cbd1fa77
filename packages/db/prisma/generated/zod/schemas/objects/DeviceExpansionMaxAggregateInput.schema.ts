import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.literal(true).optional(),
  licenseId: z.literal(true).optional(),
  paymentIntentId: z.literal(true).optional(),
  additionalDevices: z.literal(true).optional(),
  amount: z.literal(true).optional(),
  status: z.literal(true).optional(),
  createdAt: z.literal(true).optional(),
  processedAt: z.literal(true).optional()
}).strict();
export const DeviceExpansionMaxAggregateInputObjectSchema: z.ZodType<Prisma.DeviceExpansionMaxAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionMaxAggregateInputType>;
export const DeviceExpansionMaxAggregateInputObjectZodSchema = makeSchema();
