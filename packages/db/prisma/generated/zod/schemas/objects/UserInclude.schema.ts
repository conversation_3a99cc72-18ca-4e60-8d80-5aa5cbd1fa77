import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SessionFindManySchema } from '../findManySession.schema';
import { AccountFindManySchema } from '../findManyAccount.schema';
import { LicenseFindManySchema } from '../findManyLicense.schema';
import { UserInvitationFindManySchema } from '../findManyUserInvitation.schema';
import { AuditLogFindManySchema } from '../findManyAuditLog.schema';
import { RefundRequestFindManySchema } from '../findManyRefundRequest.schema';
import { SupportTicketFindManySchema } from '../findManySupportTicket.schema';
import { SupportMessageFindManySchema } from '../findManySupportMessage.schema';
import { UserCountOutputTypeArgsObjectSchema } from './UserCountOutputTypeArgs.schema'

const makeSchema = () => z.object({
  sessions: z.union([z.boolean(), z.lazy(() => SessionFindManySchema)]).optional(),
  accounts: z.union([z.boolean(), z.lazy(() => AccountFindManySchema)]).optional(),
  createdLicenses: z.union([z.boolean(), z.lazy(() => LicenseFindManySchema)]).optional(),
  sentInvitations: z.union([z.boolean(), z.lazy(() => UserInvitationFindManySchema)]).optional(),
  receivedInvitations: z.union([z.boolean(), z.lazy(() => UserInvitationFindManySchema)]).optional(),
  auditLogsAsActor: z.union([z.boolean(), z.lazy(() => AuditLogFindManySchema)]).optional(),
  auditLogsAsTarget: z.union([z.boolean(), z.lazy(() => AuditLogFindManySchema)]).optional(),
  processedRefunds: z.union([z.boolean(), z.lazy(() => RefundRequestFindManySchema)]).optional(),
  assignedTickets: z.union([z.boolean(), z.lazy(() => SupportTicketFindManySchema)]).optional(),
  supportMessages: z.union([z.boolean(), z.lazy(() => SupportMessageFindManySchema)]).optional(),
  _count: z.union([z.boolean(), z.lazy(() => UserCountOutputTypeArgsObjectSchema)]).optional()
}).strict();
export const UserIncludeObjectSchema: z.ZodType<Prisma.UserInclude> = makeSchema() as unknown as z.ZodType<Prisma.UserInclude>;
export const UserIncludeObjectZodSchema = makeSchema();
