import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { RefundStatusSchema } from '../enums/RefundStatus.schema';
import { EnumRefundStatusFieldUpdateOperationsInputObjectSchema } from './EnumRefundStatusFieldUpdateOperationsInput.schema';
import { NullableIntFieldUpdateOperationsInputObjectSchema } from './NullableIntFieldUpdateOperationsInput.schema';
import { RefundRequestUpdatestripeRefundIdsInputObjectSchema } from './RefundRequestUpdatestripeRefundIdsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { NullableDateTimeFieldUpdateOperationsInputObjectSchema } from './NullableDateTimeFieldUpdateOperationsInput.schema';
import { UserUpdateOneWithoutProcessedRefundsNestedInputObjectSchema } from './UserUpdateOneWithoutProcessedRefundsNestedInput.schema'

const makeSchema = () => z.object({
  requestedBy: z.union([z.string(), z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(),
  reason: z.union([z.string(), z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(),
  status: z.union([RefundStatusSchema, z.lazy(() => EnumRefundStatusFieldUpdateOperationsInputObjectSchema)]).optional(),
  requestedAmount: z.union([z.number().int(), z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  approvedAmount: z.union([z.number().int(), z.lazy(() => NullableIntFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  stripeRefundIds: z.union([z.lazy(() => RefundRequestUpdatestripeRefundIdsInputObjectSchema), z.string().array()]).optional(),
  adminNotes: z.union([z.string(), z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  processedAt: z.union([z.coerce.date(), z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  processedByUser: z.lazy(() => UserUpdateOneWithoutProcessedRefundsNestedInputObjectSchema).optional()
}).strict();
export const RefundRequestUpdateWithoutLicenseInputObjectSchema: z.ZodType<Prisma.RefundRequestUpdateWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestUpdateWithoutLicenseInput>;
export const RefundRequestUpdateWithoutLicenseInputObjectZodSchema = makeSchema();
