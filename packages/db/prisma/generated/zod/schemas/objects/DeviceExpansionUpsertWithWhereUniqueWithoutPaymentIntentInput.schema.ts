import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceExpansionWhereUniqueInputObjectSchema } from './DeviceExpansionWhereUniqueInput.schema';
import { DeviceExpansionUpdateWithoutPaymentIntentInputObjectSchema } from './DeviceExpansionUpdateWithoutPaymentIntentInput.schema';
import { DeviceExpansionUncheckedUpdateWithoutPaymentIntentInputObjectSchema } from './DeviceExpansionUncheckedUpdateWithoutPaymentIntentInput.schema';
import { DeviceExpansionCreateWithoutPaymentIntentInputObjectSchema } from './DeviceExpansionCreateWithoutPaymentIntentInput.schema';
import { DeviceExpansionUncheckedCreateWithoutPaymentIntentInputObjectSchema } from './DeviceExpansionUncheckedCreateWithoutPaymentIntentInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema),
  update: z.union([z.lazy(() => DeviceExpansionUpdateWithoutPaymentIntentInputObjectSchema), z.lazy(() => DeviceExpansionUncheckedUpdateWithoutPaymentIntentInputObjectSchema)]),
  create: z.union([z.lazy(() => DeviceExpansionCreateWithoutPaymentIntentInputObjectSchema), z.lazy(() => DeviceExpansionUncheckedCreateWithoutPaymentIntentInputObjectSchema)])
}).strict();
export const DeviceExpansionUpsertWithWhereUniqueWithoutPaymentIntentInputObjectSchema: z.ZodType<Prisma.DeviceExpansionUpsertWithWhereUniqueWithoutPaymentIntentInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionUpsertWithWhereUniqueWithoutPaymentIntentInput>;
export const DeviceExpansionUpsertWithWhereUniqueWithoutPaymentIntentInputObjectZodSchema = makeSchema();
