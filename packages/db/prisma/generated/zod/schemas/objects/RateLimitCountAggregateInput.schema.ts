import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.literal(true).optional(),
  identifier: z.literal(true).optional(),
  action: z.literal(true).optional(),
  count: z.literal(true).optional(),
  windowStart: z.literal(true).optional(),
  expiresAt: z.literal(true).optional(),
  _all: z.literal(true).optional()
}).strict();
export const RateLimitCountAggregateInputObjectSchema: z.ZodType<Prisma.RateLimitCountAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.RateLimitCountAggregateInputType>;
export const RateLimitCountAggregateInputObjectZodSchema = makeSchema();
