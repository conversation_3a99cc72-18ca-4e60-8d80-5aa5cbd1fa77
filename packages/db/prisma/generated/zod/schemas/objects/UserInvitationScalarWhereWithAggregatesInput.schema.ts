import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringWithAggregatesFilterObjectSchema } from './StringWithAggregatesFilter.schema';
import { EnumUserRoleWithAggregatesFilterObjectSchema } from './EnumUserRoleWithAggregatesFilter.schema';
import { UserRoleSchema } from '../enums/UserRole.schema';
import { EnumInvitationStatusWithAggregatesFilterObjectSchema } from './EnumInvitationStatusWithAggregatesFilter.schema';
import { InvitationStatusSchema } from '../enums/InvitationStatus.schema';
import { DateTimeWithAggregatesFilterObjectSchema } from './DateTimeWithAggregatesFilter.schema';
import { DateTimeNullableWithAggregatesFilterObjectSchema } from './DateTimeNullableWithAggregatesFilter.schema';
import { StringNullableWithAggregatesFilterObjectSchema } from './StringNullableWithAggregatesFilter.schema'

const userinvitationscalarwherewithaggregatesinputSchema = z.object({
  AND: z.union([z.lazy(() => UserInvitationScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => UserInvitationScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => UserInvitationScalarWhereWithAggregatesInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => UserInvitationScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => UserInvitationScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  email: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  role: z.union([z.lazy(() => EnumUserRoleWithAggregatesFilterObjectSchema), UserRoleSchema]).optional(),
  token: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  status: z.union([z.lazy(() => EnumInvitationStatusWithAggregatesFilterObjectSchema), InvitationStatusSchema]).optional(),
  expiresAt: z.union([z.lazy(() => DateTimeWithAggregatesFilterObjectSchema), z.coerce.date()]).optional(),
  sentAt: z.union([z.lazy(() => DateTimeWithAggregatesFilterObjectSchema), z.coerce.date()]).optional(),
  acceptedAt: z.union([z.lazy(() => DateTimeNullableWithAggregatesFilterObjectSchema), z.coerce.date()]).optional().nullable(),
  sentBy: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  acceptedBy: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable()
}).strict();
export const UserInvitationScalarWhereWithAggregatesInputObjectSchema: z.ZodType<Prisma.UserInvitationScalarWhereWithAggregatesInput> = userinvitationscalarwherewithaggregatesinputSchema as unknown as z.ZodType<Prisma.UserInvitationScalarWhereWithAggregatesInput>;
export const UserInvitationScalarWhereWithAggregatesInputObjectZodSchema = userinvitationscalarwherewithaggregatesinputSchema;
