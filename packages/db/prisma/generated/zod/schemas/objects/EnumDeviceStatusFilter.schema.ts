import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceStatusSchema } from '../enums/DeviceStatus.schema';
import { NestedEnumDeviceStatusFilterObjectSchema } from './NestedEnumDeviceStatusFilter.schema'

const makeSchema = () => z.object({
  equals: DeviceStatusSchema.optional(),
  in: DeviceStatusSchema.array().optional(),
  notIn: DeviceStatusSchema.array().optional(),
  not: z.union([DeviceStatusSchema, z.lazy(() => NestedEnumDeviceStatusFilterObjectSchema)]).optional()
}).strict();
export const EnumDeviceStatusFilterObjectSchema: z.ZodType<Prisma.EnumDeviceStatusFilter> = makeSchema() as unknown as z.ZodType<Prisma.EnumDeviceStatusFilter>;
export const EnumDeviceStatusFilterObjectZodSchema = makeSchema();
