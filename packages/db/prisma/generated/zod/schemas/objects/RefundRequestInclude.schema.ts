import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserArgsObjectSchema } from './UserArgs.schema';
import { LicenseArgsObjectSchema } from './LicenseArgs.schema'

const makeSchema = () => z.object({
  processedByUser: z.union([z.boolean(), z.lazy(() => UserArgsObjectSchema)]).optional(),
  license: z.union([z.boolean(), z.lazy(() => LicenseArgsObjectSchema)]).optional()
}).strict();
export const RefundRequestIncludeObjectSchema: z.ZodType<Prisma.RefundRequestInclude> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestInclude>;
export const RefundRequestIncludeObjectZodSchema = makeSchema();
