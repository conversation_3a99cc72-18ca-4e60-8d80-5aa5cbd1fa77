import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketWhereInputObjectSchema } from './SupportTicketWhereInput.schema'

const makeSchema = () => z.object({
  every: z.lazy(() => SupportTicketWhereInputObjectSchema).optional(),
  some: z.lazy(() => SupportTicketWhereInputObjectSchema).optional(),
  none: z.lazy(() => SupportTicketWhereInputObjectSchema).optional()
}).strict();
export const SupportTicketListRelationFilterObjectSchema: z.ZodType<Prisma.SupportTicketListRelationFilter> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketListRelationFilter>;
export const SupportTicketListRelationFilterObjectZodSchema = makeSchema();
