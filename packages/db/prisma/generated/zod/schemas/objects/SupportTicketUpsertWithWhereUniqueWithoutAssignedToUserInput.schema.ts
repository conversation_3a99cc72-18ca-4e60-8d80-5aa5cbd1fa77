import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketWhereUniqueInputObjectSchema } from './SupportTicketWhereUniqueInput.schema';
import { SupportTicketUpdateWithoutAssignedToUserInputObjectSchema } from './SupportTicketUpdateWithoutAssignedToUserInput.schema';
import { SupportTicketUncheckedUpdateWithoutAssignedToUserInputObjectSchema } from './SupportTicketUncheckedUpdateWithoutAssignedToUserInput.schema';
import { SupportTicketCreateWithoutAssignedToUserInputObjectSchema } from './SupportTicketCreateWithoutAssignedToUserInput.schema';
import { SupportTicketUncheckedCreateWithoutAssignedToUserInputObjectSchema } from './SupportTicketUncheckedCreateWithoutAssignedToUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => SupportTicketWhereUniqueInputObjectSchema),
  update: z.union([z.lazy(() => SupportTicketUpdateWithoutAssignedToUserInputObjectSchema), z.lazy(() => SupportTicketUncheckedUpdateWithoutAssignedToUserInputObjectSchema)]),
  create: z.union([z.lazy(() => SupportTicketCreateWithoutAssignedToUserInputObjectSchema), z.lazy(() => SupportTicketUncheckedCreateWithoutAssignedToUserInputObjectSchema)])
}).strict();
export const SupportTicketUpsertWithWhereUniqueWithoutAssignedToUserInputObjectSchema: z.ZodType<Prisma.SupportTicketUpsertWithWhereUniqueWithoutAssignedToUserInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketUpsertWithWhereUniqueWithoutAssignedToUserInput>;
export const SupportTicketUpsertWithWhereUniqueWithoutAssignedToUserInputObjectZodSchema = makeSchema();
