import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { PaymentStatusSchema } from '../enums/PaymentStatus.schema';
import { EnumPaymentStatusFieldUpdateOperationsInputObjectSchema } from './EnumPaymentStatusFieldUpdateOperationsInput.schema';
import { PaymentTypeSchema } from '../enums/PaymentType.schema';
import { EnumPaymentTypeFieldUpdateOperationsInputObjectSchema } from './EnumPaymentTypeFieldUpdateOperationsInput.schema';
import { NullableDateTimeFieldUpdateOperationsInputObjectSchema } from './NullableDateTimeFieldUpdateOperationsInput.schema';
import { LicenseUpdateManyWithoutPaymentIntentNestedInputObjectSchema } from './LicenseUpdateManyWithoutPaymentIntentNestedInput.schema';
import { DeviceExpansionUpdateManyWithoutPaymentIntentNestedInputObjectSchema } from './DeviceExpansionUpdateManyWithoutPaymentIntentNestedInput.schema'

const makeSchema = () => z.object({
  stripePaymentIntentId: z.union([z.string(), z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(),
  stripeCheckoutSessionId: z.union([z.string(), z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  amount: z.union([z.number().int(), z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(),
  currency: z.union([z.string(), z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(),
  status: z.union([PaymentStatusSchema, z.lazy(() => EnumPaymentStatusFieldUpdateOperationsInputObjectSchema)]).optional(),
  paymentType: z.union([PaymentTypeSchema, z.lazy(() => EnumPaymentTypeFieldUpdateOperationsInputObjectSchema)]).optional(),
  customerEmail: z.union([z.string(), z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(),
  customerName: z.union([z.string(), z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  processedAt: z.union([z.coerce.date(), z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  licenses: z.lazy(() => LicenseUpdateManyWithoutPaymentIntentNestedInputObjectSchema).optional(),
  deviceExpansions: z.lazy(() => DeviceExpansionUpdateManyWithoutPaymentIntentNestedInputObjectSchema).optional()
}).strict();
export const PaymentIntentUpdateWithoutWebhookEventsInputObjectSchema: z.ZodType<Prisma.PaymentIntentUpdateWithoutWebhookEventsInput> = makeSchema() as unknown as z.ZodType<Prisma.PaymentIntentUpdateWithoutWebhookEventsInput>;
export const PaymentIntentUpdateWithoutWebhookEventsInputObjectZodSchema = makeSchema();
