import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationCreateWithoutSentByUserInputObjectSchema } from './UserInvitationCreateWithoutSentByUserInput.schema';
import { UserInvitationUncheckedCreateWithoutSentByUserInputObjectSchema } from './UserInvitationUncheckedCreateWithoutSentByUserInput.schema';
import { UserInvitationCreateOrConnectWithoutSentByUserInputObjectSchema } from './UserInvitationCreateOrConnectWithoutSentByUserInput.schema';
import { UserInvitationUpsertWithWhereUniqueWithoutSentByUserInputObjectSchema } from './UserInvitationUpsertWithWhereUniqueWithoutSentByUserInput.schema';
import { UserInvitationCreateManySentByUserInputEnvelopeObjectSchema } from './UserInvitationCreateManySentByUserInputEnvelope.schema';
import { UserInvitationWhereUniqueInputObjectSchema } from './UserInvitationWhereUniqueInput.schema';
import { UserInvitationUpdateWithWhereUniqueWithoutSentByUserInputObjectSchema } from './UserInvitationUpdateWithWhereUniqueWithoutSentByUserInput.schema';
import { UserInvitationUpdateManyWithWhereWithoutSentByUserInputObjectSchema } from './UserInvitationUpdateManyWithWhereWithoutSentByUserInput.schema';
import { UserInvitationScalarWhereInputObjectSchema } from './UserInvitationScalarWhereInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserInvitationCreateWithoutSentByUserInputObjectSchema), z.lazy(() => UserInvitationCreateWithoutSentByUserInputObjectSchema).array(), z.lazy(() => UserInvitationUncheckedCreateWithoutSentByUserInputObjectSchema), z.lazy(() => UserInvitationUncheckedCreateWithoutSentByUserInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => UserInvitationCreateOrConnectWithoutSentByUserInputObjectSchema), z.lazy(() => UserInvitationCreateOrConnectWithoutSentByUserInputObjectSchema).array()]).optional(),
  upsert: z.union([z.lazy(() => UserInvitationUpsertWithWhereUniqueWithoutSentByUserInputObjectSchema), z.lazy(() => UserInvitationUpsertWithWhereUniqueWithoutSentByUserInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => UserInvitationCreateManySentByUserInputEnvelopeObjectSchema).optional(),
  set: z.union([z.lazy(() => UserInvitationWhereUniqueInputObjectSchema), z.lazy(() => UserInvitationWhereUniqueInputObjectSchema).array()]).optional(),
  disconnect: z.union([z.lazy(() => UserInvitationWhereUniqueInputObjectSchema), z.lazy(() => UserInvitationWhereUniqueInputObjectSchema).array()]).optional(),
  delete: z.union([z.lazy(() => UserInvitationWhereUniqueInputObjectSchema), z.lazy(() => UserInvitationWhereUniqueInputObjectSchema).array()]).optional(),
  connect: z.union([z.lazy(() => UserInvitationWhereUniqueInputObjectSchema), z.lazy(() => UserInvitationWhereUniqueInputObjectSchema).array()]).optional(),
  update: z.union([z.lazy(() => UserInvitationUpdateWithWhereUniqueWithoutSentByUserInputObjectSchema), z.lazy(() => UserInvitationUpdateWithWhereUniqueWithoutSentByUserInputObjectSchema).array()]).optional(),
  updateMany: z.union([z.lazy(() => UserInvitationUpdateManyWithWhereWithoutSentByUserInputObjectSchema), z.lazy(() => UserInvitationUpdateManyWithWhereWithoutSentByUserInputObjectSchema).array()]).optional(),
  deleteMany: z.union([z.lazy(() => UserInvitationScalarWhereInputObjectSchema), z.lazy(() => UserInvitationScalarWhereInputObjectSchema).array()]).optional()
}).strict();
export const UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInputObjectSchema: z.ZodType<Prisma.UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInput>;
export const UserInvitationUncheckedUpdateManyWithoutSentByUserNestedInputObjectZodSchema = makeSchema();
