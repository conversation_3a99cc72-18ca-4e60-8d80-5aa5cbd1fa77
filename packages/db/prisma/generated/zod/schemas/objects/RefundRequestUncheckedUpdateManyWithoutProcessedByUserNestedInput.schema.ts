import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestCreateWithoutProcessedByUserInputObjectSchema } from './RefundRequestCreateWithoutProcessedByUserInput.schema';
import { RefundRequestUncheckedCreateWithoutProcessedByUserInputObjectSchema } from './RefundRequestUncheckedCreateWithoutProcessedByUserInput.schema';
import { RefundRequestCreateOrConnectWithoutProcessedByUserInputObjectSchema } from './RefundRequestCreateOrConnectWithoutProcessedByUserInput.schema';
import { RefundRequestUpsertWithWhereUniqueWithoutProcessedByUserInputObjectSchema } from './RefundRequestUpsertWithWhereUniqueWithoutProcessedByUserInput.schema';
import { RefundRequestCreateManyProcessedByUserInputEnvelopeObjectSchema } from './RefundRequestCreateManyProcessedByUserInputEnvelope.schema';
import { RefundRequestWhereUniqueInputObjectSchema } from './RefundRequestWhereUniqueInput.schema';
import { RefundRequestUpdateWithWhereUniqueWithoutProcessedByUserInputObjectSchema } from './RefundRequestUpdateWithWhereUniqueWithoutProcessedByUserInput.schema';
import { RefundRequestUpdateManyWithWhereWithoutProcessedByUserInputObjectSchema } from './RefundRequestUpdateManyWithWhereWithoutProcessedByUserInput.schema';
import { RefundRequestScalarWhereInputObjectSchema } from './RefundRequestScalarWhereInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => RefundRequestCreateWithoutProcessedByUserInputObjectSchema), z.lazy(() => RefundRequestCreateWithoutProcessedByUserInputObjectSchema).array(), z.lazy(() => RefundRequestUncheckedCreateWithoutProcessedByUserInputObjectSchema), z.lazy(() => RefundRequestUncheckedCreateWithoutProcessedByUserInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => RefundRequestCreateOrConnectWithoutProcessedByUserInputObjectSchema), z.lazy(() => RefundRequestCreateOrConnectWithoutProcessedByUserInputObjectSchema).array()]).optional(),
  upsert: z.union([z.lazy(() => RefundRequestUpsertWithWhereUniqueWithoutProcessedByUserInputObjectSchema), z.lazy(() => RefundRequestUpsertWithWhereUniqueWithoutProcessedByUserInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => RefundRequestCreateManyProcessedByUserInputEnvelopeObjectSchema).optional(),
  set: z.union([z.lazy(() => RefundRequestWhereUniqueInputObjectSchema), z.lazy(() => RefundRequestWhereUniqueInputObjectSchema).array()]).optional(),
  disconnect: z.union([z.lazy(() => RefundRequestWhereUniqueInputObjectSchema), z.lazy(() => RefundRequestWhereUniqueInputObjectSchema).array()]).optional(),
  delete: z.union([z.lazy(() => RefundRequestWhereUniqueInputObjectSchema), z.lazy(() => RefundRequestWhereUniqueInputObjectSchema).array()]).optional(),
  connect: z.union([z.lazy(() => RefundRequestWhereUniqueInputObjectSchema), z.lazy(() => RefundRequestWhereUniqueInputObjectSchema).array()]).optional(),
  update: z.union([z.lazy(() => RefundRequestUpdateWithWhereUniqueWithoutProcessedByUserInputObjectSchema), z.lazy(() => RefundRequestUpdateWithWhereUniqueWithoutProcessedByUserInputObjectSchema).array()]).optional(),
  updateMany: z.union([z.lazy(() => RefundRequestUpdateManyWithWhereWithoutProcessedByUserInputObjectSchema), z.lazy(() => RefundRequestUpdateManyWithWhereWithoutProcessedByUserInputObjectSchema).array()]).optional(),
  deleteMany: z.union([z.lazy(() => RefundRequestScalarWhereInputObjectSchema), z.lazy(() => RefundRequestScalarWhereInputObjectSchema).array()]).optional()
}).strict();
export const RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInputObjectSchema: z.ZodType<Prisma.RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInput>;
export const RefundRequestUncheckedUpdateManyWithoutProcessedByUserNestedInputObjectZodSchema = makeSchema();
