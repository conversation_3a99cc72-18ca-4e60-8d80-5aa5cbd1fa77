import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageWhereUniqueInputObjectSchema } from './SupportMessageWhereUniqueInput.schema';
import { SupportMessageCreateWithoutTicketInputObjectSchema } from './SupportMessageCreateWithoutTicketInput.schema';
import { SupportMessageUncheckedCreateWithoutTicketInputObjectSchema } from './SupportMessageUncheckedCreateWithoutTicketInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => SupportMessageWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => SupportMessageCreateWithoutTicketInputObjectSchema), z.lazy(() => SupportMessageUncheckedCreateWithoutTicketInputObjectSchema)])
}).strict();
export const SupportMessageCreateOrConnectWithoutTicketInputObjectSchema: z.ZodType<Prisma.SupportMessageCreateOrConnectWithoutTicketInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageCreateOrConnectWithoutTicketInput>;
export const SupportMessageCreateOrConnectWithoutTicketInputObjectZodSchema = makeSchema();
