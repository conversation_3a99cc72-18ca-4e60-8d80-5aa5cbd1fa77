import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { RefundRequestCountOrderByAggregateInputObjectSchema } from './RefundRequestCountOrderByAggregateInput.schema';
import { RefundRequestAvgOrderByAggregateInputObjectSchema } from './RefundRequestAvgOrderByAggregateInput.schema';
import { RefundRequestMaxOrderByAggregateInputObjectSchema } from './RefundRequestMaxOrderByAggregateInput.schema';
import { RefundRequestMinOrderByAggregateInputObjectSchema } from './RefundRequestMinOrderByAggregateInput.schema';
import { RefundRequestSumOrderByAggregateInputObjectSchema } from './RefundRequestSumOrderByAggregateInput.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  licenseId: SortOrderSchema.optional(),
  requestedBy: SortOrderSchema.optional(),
  reason: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  requestedAmount: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  approvedAmount: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  stripeRefundIds: SortOrderSchema.optional(),
  adminNotes: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  processedBy: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  createdAt: SortOrderSchema.optional(),
  updatedAt: SortOrderSchema.optional(),
  processedAt: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  _count: z.lazy(() => RefundRequestCountOrderByAggregateInputObjectSchema).optional(),
  _avg: z.lazy(() => RefundRequestAvgOrderByAggregateInputObjectSchema).optional(),
  _max: z.lazy(() => RefundRequestMaxOrderByAggregateInputObjectSchema).optional(),
  _min: z.lazy(() => RefundRequestMinOrderByAggregateInputObjectSchema).optional(),
  _sum: z.lazy(() => RefundRequestSumOrderByAggregateInputObjectSchema).optional()
}).strict();
export const RefundRequestOrderByWithAggregationInputObjectSchema: z.ZodType<Prisma.RefundRequestOrderByWithAggregationInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestOrderByWithAggregationInput>;
export const RefundRequestOrderByWithAggregationInputObjectZodSchema = makeSchema();
