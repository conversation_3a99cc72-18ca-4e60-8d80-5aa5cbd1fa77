import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { BoolFilterObjectSchema } from './BoolFilter.schema';
import { DateTimeNullableFilterObjectSchema } from './DateTimeNullableFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { IntFilterObjectSchema } from './IntFilter.schema'

const webhookeventscalarwhereinputSchema = z.object({
  AND: z.union([z.lazy(() => WebhookEventScalarWhereInputObjectSchema), z.lazy(() => WebhookEventScalarWhereInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => WebhookEventScalarWhereInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => WebhookEventScalarWhereInputObjectSchema), z.lazy(() => WebhookEventScalarWhereInputObjectSchema).array()]).optional(),
  stripeEventId: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  eventType: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  processed: z.union([z.lazy(() => BoolFilterObjectSchema), z.boolean()]).optional(),
  processedAt: z.union([z.lazy(() => DateTimeNullableFilterObjectSchema), z.coerce.date()]).optional().nullable(),
  errorMessage: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  retryCount: z.union([z.lazy(() => IntFilterObjectSchema), z.number().int()]).optional(),
  paymentIntentId: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable()
}).strict();
export const WebhookEventScalarWhereInputObjectSchema: z.ZodType<Prisma.WebhookEventScalarWhereInput> = webhookeventscalarwhereinputSchema as unknown as z.ZodType<Prisma.WebhookEventScalarWhereInput>;
export const WebhookEventScalarWhereInputObjectZodSchema = webhookeventscalarwhereinputSchema;
