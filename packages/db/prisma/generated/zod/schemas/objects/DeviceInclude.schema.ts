import { z } from 'zod';
import type { Prisma } from '../../../client';
import { LicenseArgsObjectSchema } from './LicenseArgs.schema'

const makeSchema = () => z.object({
  license: z.union([z.boolean(), z.lazy(() => LicenseArgsObjectSchema)]).optional()
}).strict();
export const DeviceIncludeObjectSchema: z.ZodType<Prisma.DeviceInclude> = makeSchema() as unknown as z.ZodType<Prisma.DeviceInclude>;
export const DeviceIncludeObjectZodSchema = makeSchema();
