import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserUpdateWithoutReceivedInvitationsInputObjectSchema } from './UserUpdateWithoutReceivedInvitationsInput.schema';
import { UserUncheckedUpdateWithoutReceivedInvitationsInputObjectSchema } from './UserUncheckedUpdateWithoutReceivedInvitationsInput.schema';
import { UserCreateWithoutReceivedInvitationsInputObjectSchema } from './UserCreateWithoutReceivedInvitationsInput.schema';
import { UserUncheckedCreateWithoutReceivedInvitationsInputObjectSchema } from './UserUncheckedCreateWithoutReceivedInvitationsInput.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema'

const makeSchema = () => z.object({
  update: z.union([z.lazy(() => UserUpdateWithoutReceivedInvitationsInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutReceivedInvitationsInputObjectSchema)]),
  create: z.union([z.lazy(() => UserCreateWithoutReceivedInvitationsInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutReceivedInvitationsInputObjectSchema)]),
  where: z.lazy(() => UserWhereInputObjectSchema).optional()
}).strict();
export const UserUpsertWithoutReceivedInvitationsInputObjectSchema: z.ZodType<Prisma.UserUpsertWithoutReceivedInvitationsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpsertWithoutReceivedInvitationsInput>;
export const UserUpsertWithoutReceivedInvitationsInputObjectZodSchema = makeSchema();
