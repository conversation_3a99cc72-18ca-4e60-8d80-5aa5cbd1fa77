import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  licenseId: SortOrderSchema.optional(),
  deviceHash: SortOrderSchema.optional(),
  salt: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  firstSeen: SortOrderSchema.optional(),
  lastSeen: SortOrderSchema.optional(),
  removedAt: SortOrderSchema.optional(),
  appVersion: SortOrderSchema.optional(),
  deviceName: SortOrderSchema.optional(),
  deviceType: SortOrderSchema.optional(),
  deviceModel: SortOrderSchema.optional(),
  operatingSystem: SortOrderSchema.optional(),
  architecture: SortOrderSchema.optional(),
  screenResolution: SortOrderSchema.optional(),
  totalMemory: SortOrderSchema.optional(),
  userNickname: SortOrderSchema.optional(),
  location: SortOrderSchema.optional(),
  notes: SortOrderSchema.optional()
}).strict();
export const DeviceMaxOrderByAggregateInputObjectSchema: z.ZodType<Prisma.DeviceMaxOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceMaxOrderByAggregateInput>;
export const DeviceMaxOrderByAggregateInputObjectZodSchema = makeSchema();
