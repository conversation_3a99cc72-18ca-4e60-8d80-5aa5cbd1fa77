import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestCreateManyProcessedByUserInputObjectSchema } from './RefundRequestCreateManyProcessedByUserInput.schema'

const makeSchema = () => z.object({
  data: z.union([z.lazy(() => RefundRequestCreateManyProcessedByUserInputObjectSchema), z.lazy(() => RefundRequestCreateManyProcessedByUserInputObjectSchema).array()]),
  skipDuplicates: z.boolean().optional()
}).strict();
export const RefundRequestCreateManyProcessedByUserInputEnvelopeObjectSchema: z.ZodType<Prisma.RefundRequestCreateManyProcessedByUserInputEnvelope> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestCreateManyProcessedByUserInputEnvelope>;
export const RefundRequestCreateManyProcessedByUserInputEnvelopeObjectZodSchema = makeSchema();
