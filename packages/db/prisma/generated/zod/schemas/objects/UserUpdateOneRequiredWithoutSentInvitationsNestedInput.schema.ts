import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutSentInvitationsInputObjectSchema } from './UserCreateWithoutSentInvitationsInput.schema';
import { UserUncheckedCreateWithoutSentInvitationsInputObjectSchema } from './UserUncheckedCreateWithoutSentInvitationsInput.schema';
import { UserCreateOrConnectWithoutSentInvitationsInputObjectSchema } from './UserCreateOrConnectWithoutSentInvitationsInput.schema';
import { UserUpsertWithoutSentInvitationsInputObjectSchema } from './UserUpsertWithoutSentInvitationsInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserUpdateToOneWithWhereWithoutSentInvitationsInputObjectSchema } from './UserUpdateToOneWithWhereWithoutSentInvitationsInput.schema';
import { UserUpdateWithoutSentInvitationsInputObjectSchema } from './UserUpdateWithoutSentInvitationsInput.schema';
import { UserUncheckedUpdateWithoutSentInvitationsInputObjectSchema } from './UserUncheckedUpdateWithoutSentInvitationsInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutSentInvitationsInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutSentInvitationsInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutSentInvitationsInputObjectSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutSentInvitationsInputObjectSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional(),
  update: z.union([z.lazy(() => UserUpdateToOneWithWhereWithoutSentInvitationsInputObjectSchema), z.lazy(() => UserUpdateWithoutSentInvitationsInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutSentInvitationsInputObjectSchema)]).optional()
}).strict();
export const UserUpdateOneRequiredWithoutSentInvitationsNestedInputObjectSchema: z.ZodType<Prisma.UserUpdateOneRequiredWithoutSentInvitationsNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateOneRequiredWithoutSentInvitationsNestedInput>;
export const UserUpdateOneRequiredWithoutSentInvitationsNestedInputObjectZodSchema = makeSchema();
