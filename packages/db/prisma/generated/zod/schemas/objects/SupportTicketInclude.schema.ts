import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserArgsObjectSchema } from './UserArgs.schema';
import { SupportMessageFindManySchema } from '../findManySupportMessage.schema';
import { SupportTicketCountOutputTypeArgsObjectSchema } from './SupportTicketCountOutputTypeArgs.schema'

const makeSchema = () => z.object({
  assignedToUser: z.union([z.boolean(), z.lazy(() => UserArgsObjectSchema)]).optional(),
  messages: z.union([z.boolean(), z.lazy(() => SupportMessageFindManySchema)]).optional(),
  _count: z.union([z.boolean(), z.lazy(() => SupportTicketCountOutputTypeArgsObjectSchema)]).optional()
}).strict();
export const SupportTicketIncludeObjectSchema: z.ZodType<Prisma.SupportTicketInclude> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketInclude>;
export const SupportTicketIncludeObjectZodSchema = makeSchema();
