import { z } from 'zod';
import type { Prisma } from '../../../client';
import { LicenseTypeSchema } from '../enums/LicenseType.schema';
import { NestedEnumLicenseTypeFilterObjectSchema } from './NestedEnumLicenseTypeFilter.schema'

const makeSchema = () => z.object({
  equals: LicenseTypeSchema.optional(),
  in: LicenseTypeSchema.array().optional(),
  notIn: LicenseTypeSchema.array().optional(),
  not: z.union([LicenseTypeSchema, z.lazy(() => NestedEnumLicenseTypeFilterObjectSchema)]).optional()
}).strict();
export const EnumLicenseTypeFilterObjectSchema: z.ZodType<Prisma.EnumLicenseTypeFilter> = makeSchema() as unknown as z.ZodType<Prisma.EnumLicenseTypeFilter>;
export const EnumLicenseTypeFilterObjectZodSchema = makeSchema();
