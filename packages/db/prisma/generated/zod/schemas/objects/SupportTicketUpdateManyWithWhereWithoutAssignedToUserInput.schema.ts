import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketScalarWhereInputObjectSchema } from './SupportTicketScalarWhereInput.schema';
import { SupportTicketUpdateManyMutationInputObjectSchema } from './SupportTicketUpdateManyMutationInput.schema';
import { SupportTicketUncheckedUpdateManyWithoutAssignedToUserInputObjectSchema } from './SupportTicketUncheckedUpdateManyWithoutAssignedToUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => SupportTicketScalarWhereInputObjectSchema),
  data: z.union([z.lazy(() => SupportTicketUpdateManyMutationInputObjectSchema), z.lazy(() => SupportTicketUncheckedUpdateManyWithoutAssignedToUserInputObjectSchema)])
}).strict();
export const SupportTicketUpdateManyWithWhereWithoutAssignedToUserInputObjectSchema: z.ZodType<Prisma.SupportTicketUpdateManyWithWhereWithoutAssignedToUserInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketUpdateManyWithWhereWithoutAssignedToUserInput>;
export const SupportTicketUpdateManyWithWhereWithoutAssignedToUserInputObjectZodSchema = makeSchema();
