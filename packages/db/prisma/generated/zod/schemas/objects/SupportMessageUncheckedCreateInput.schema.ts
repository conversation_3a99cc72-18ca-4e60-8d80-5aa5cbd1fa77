import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.string().optional(),
  ticketId: z.string(),
  message: z.string(),
  isInternal: z.boolean().optional(),
  authorEmail: z.string().optional().nullable(),
  authorId: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional()
}).strict();
export const SupportMessageUncheckedCreateInputObjectSchema: z.ZodType<Prisma.SupportMessageUncheckedCreateInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageUncheckedCreateInput>;
export const SupportMessageUncheckedCreateInputObjectZodSchema = makeSchema();
