import { z } from 'zod';
import type { Prisma } from '../../../client';
import { TicketStatusSchema } from '../enums/TicketStatus.schema';
import { TicketPrioritySchema } from '../enums/TicketPriority.schema';
import { TicketCategorySchema } from '../enums/TicketCategory.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  ticketId: z.string(),
  subject: z.string(),
  description: z.string(),
  status: TicketStatusSchema.optional(),
  priority: TicketPrioritySchema.optional(),
  category: TicketCategorySchema.optional().nullable(),
  customerEmail: z.string(),
  customerName: z.string().optional().nullable(),
  licenseKey: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  resolvedAt: z.coerce.date().optional().nullable()
}).strict();
export const SupportTicketCreateManyAssignedToUserInputObjectSchema: z.ZodType<Prisma.SupportTicketCreateManyAssignedToUserInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketCreateManyAssignedToUserInput>;
export const SupportTicketCreateManyAssignedToUserInputObjectZodSchema = makeSchema();
