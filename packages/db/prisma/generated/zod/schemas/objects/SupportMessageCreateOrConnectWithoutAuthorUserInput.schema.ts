import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageWhereUniqueInputObjectSchema } from './SupportMessageWhereUniqueInput.schema';
import { SupportMessageCreateWithoutAuthorUserInputObjectSchema } from './SupportMessageCreateWithoutAuthorUserInput.schema';
import { SupportMessageUncheckedCreateWithoutAuthorUserInputObjectSchema } from './SupportMessageUncheckedCreateWithoutAuthorUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => SupportMessageWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => SupportMessageCreateWithoutAuthorUserInputObjectSchema), z.lazy(() => SupportMessageUncheckedCreateWithoutAuthorUserInputObjectSchema)])
}).strict();
export const SupportMessageCreateOrConnectWithoutAuthorUserInputObjectSchema: z.ZodType<Prisma.SupportMessageCreateOrConnectWithoutAuthorUserInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageCreateOrConnectWithoutAuthorUserInput>;
export const SupportMessageCreateOrConnectWithoutAuthorUserInputObjectZodSchema = makeSchema();
