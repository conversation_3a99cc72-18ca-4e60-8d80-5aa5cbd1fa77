import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserCreateWithoutAuditLogsAsTargetInputObjectSchema } from './UserCreateWithoutAuditLogsAsTargetInput.schema';
import { UserUncheckedCreateWithoutAuditLogsAsTargetInputObjectSchema } from './UserUncheckedCreateWithoutAuditLogsAsTargetInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => UserCreateWithoutAuditLogsAsTargetInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutAuditLogsAsTargetInputObjectSchema)])
}).strict();
export const UserCreateOrConnectWithoutAuditLogsAsTargetInputObjectSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutAuditLogsAsTargetInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateOrConnectWithoutAuditLogsAsTargetInput>;
export const UserCreateOrConnectWithoutAuditLogsAsTargetInputObjectZodSchema = makeSchema();
