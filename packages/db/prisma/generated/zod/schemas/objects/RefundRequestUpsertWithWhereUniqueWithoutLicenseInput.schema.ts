import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestWhereUniqueInputObjectSchema } from './RefundRequestWhereUniqueInput.schema';
import { RefundRequestUpdateWithoutLicenseInputObjectSchema } from './RefundRequestUpdateWithoutLicenseInput.schema';
import { RefundRequestUncheckedUpdateWithoutLicenseInputObjectSchema } from './RefundRequestUncheckedUpdateWithoutLicenseInput.schema';
import { RefundRequestCreateWithoutLicenseInputObjectSchema } from './RefundRequestCreateWithoutLicenseInput.schema';
import { RefundRequestUncheckedCreateWithoutLicenseInputObjectSchema } from './RefundRequestUncheckedCreateWithoutLicenseInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => RefundRequestWhereUniqueInputObjectSchema),
  update: z.union([z.lazy(() => RefundRequestUpdateWithoutLicenseInputObjectSchema), z.lazy(() => RefundRequestUncheckedUpdateWithoutLicenseInputObjectSchema)]),
  create: z.union([z.lazy(() => RefundRequestCreateWithoutLicenseInputObjectSchema), z.lazy(() => RefundRequestUncheckedCreateWithoutLicenseInputObjectSchema)])
}).strict();
export const RefundRequestUpsertWithWhereUniqueWithoutLicenseInputObjectSchema: z.ZodType<Prisma.RefundRequestUpsertWithWhereUniqueWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestUpsertWithWhereUniqueWithoutLicenseInput>;
export const RefundRequestUpsertWithWhereUniqueWithoutLicenseInputObjectZodSchema = makeSchema();
