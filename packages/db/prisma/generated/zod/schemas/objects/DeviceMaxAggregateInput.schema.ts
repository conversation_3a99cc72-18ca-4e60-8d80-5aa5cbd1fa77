import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.literal(true).optional(),
  licenseId: z.literal(true).optional(),
  deviceHash: z.literal(true).optional(),
  salt: z.literal(true).optional(),
  status: z.literal(true).optional(),
  firstSeen: z.literal(true).optional(),
  lastSeen: z.literal(true).optional(),
  removedAt: z.literal(true).optional(),
  appVersion: z.literal(true).optional(),
  deviceName: z.literal(true).optional(),
  deviceType: z.literal(true).optional(),
  deviceModel: z.literal(true).optional(),
  operatingSystem: z.literal(true).optional(),
  architecture: z.literal(true).optional(),
  screenResolution: z.literal(true).optional(),
  totalMemory: z.literal(true).optional(),
  userNickname: z.literal(true).optional(),
  location: z.literal(true).optional(),
  notes: z.literal(true).optional()
}).strict();
export const DeviceMaxAggregateInputObjectSchema: z.ZodType<Prisma.DeviceMaxAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.DeviceMaxAggregateInputType>;
export const DeviceMaxAggregateInputObjectZodSchema = makeSchema();
