import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageWhereInputObjectSchema } from './SupportMessageWhereInput.schema'

const makeSchema = () => z.object({
  every: z.lazy(() => SupportMessageWhereInputObjectSchema).optional(),
  some: z.lazy(() => SupportMessageWhereInputObjectSchema).optional(),
  none: z.lazy(() => SupportMessageWhereInputObjectSchema).optional()
}).strict();
export const SupportMessageListRelationFilterObjectSchema: z.ZodType<Prisma.SupportMessageListRelationFilter> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageListRelationFilter>;
export const SupportMessageListRelationFilterObjectZodSchema = makeSchema();
