import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  identifier: z.string(),
  action: z.string()
}).strict();
export const RateLimitIdentifierActionCompoundUniqueInputObjectSchema: z.ZodType<Prisma.RateLimitIdentifierActionCompoundUniqueInput> = makeSchema() as unknown as z.ZodType<Prisma.RateLimitIdentifierActionCompoundUniqueInput>;
export const RateLimitIdentifierActionCompoundUniqueInputObjectZodSchema = makeSchema();
