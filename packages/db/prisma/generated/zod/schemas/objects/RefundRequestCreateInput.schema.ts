import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundStatusSchema } from '../enums/RefundStatus.schema';
import { RefundRequestCreatestripeRefundIdsInputObjectSchema } from './RefundRequestCreatestripeRefundIdsInput.schema';
import { UserCreateNestedOneWithoutProcessedRefundsInputObjectSchema } from './UserCreateNestedOneWithoutProcessedRefundsInput.schema';
import { LicenseCreateNestedOneWithoutRefundRequestsInputObjectSchema } from './LicenseCreateNestedOneWithoutRefundRequestsInput.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  requestedBy: z.string(),
  reason: z.string(),
  status: RefundStatusSchema.optional(),
  requestedAmount: z.number().int().optional().nullable(),
  approvedAmount: z.number().int().optional().nullable(),
  stripeRefundIds: z.union([z.lazy(() => RefundRequestCreatestripeRefundIdsInputObjectSchema), z.string().array()]).optional(),
  adminNotes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  processedAt: z.coerce.date().optional().nullable(),
  processedByUser: z.lazy(() => UserCreateNestedOneWithoutProcessedRefundsInputObjectSchema).optional(),
  license: z.lazy(() => LicenseCreateNestedOneWithoutRefundRequestsInputObjectSchema)
}).strict();
export const RefundRequestCreateInputObjectSchema: z.ZodType<Prisma.RefundRequestCreateInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestCreateInput>;
export const RefundRequestCreateInputObjectZodSchema = makeSchema();
