import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  retryCount: z.literal(true).optional()
}).strict();
export const WebhookEventAvgAggregateInputObjectSchema: z.ZodType<Prisma.WebhookEventAvgAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventAvgAggregateInputType>;
export const WebhookEventAvgAggregateInputObjectZodSchema = makeSchema();
