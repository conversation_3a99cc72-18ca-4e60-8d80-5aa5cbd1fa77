import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserUpdateWithoutReceivedInvitationsInputObjectSchema } from './UserUpdateWithoutReceivedInvitationsInput.schema';
import { UserUncheckedUpdateWithoutReceivedInvitationsInputObjectSchema } from './UserUncheckedUpdateWithoutReceivedInvitationsInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserWhereInputObjectSchema).optional(),
  data: z.union([z.lazy(() => UserUpdateWithoutReceivedInvitationsInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutReceivedInvitationsInputObjectSchema)])
}).strict();
export const UserUpdateToOneWithWhereWithoutReceivedInvitationsInputObjectSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutReceivedInvitationsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutReceivedInvitationsInput>;
export const UserUpdateToOneWithWhereWithoutReceivedInvitationsInputObjectZodSchema = makeSchema();
