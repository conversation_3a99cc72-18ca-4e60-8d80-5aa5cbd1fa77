import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserCreateWithoutCreatedLicensesInputObjectSchema } from './UserCreateWithoutCreatedLicensesInput.schema';
import { UserUncheckedCreateWithoutCreatedLicensesInputObjectSchema } from './UserUncheckedCreateWithoutCreatedLicensesInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => UserCreateWithoutCreatedLicensesInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutCreatedLicensesInputObjectSchema)])
}).strict();
export const UserCreateOrConnectWithoutCreatedLicensesInputObjectSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutCreatedLicensesInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateOrConnectWithoutCreatedLicensesInput>;
export const UserCreateOrConnectWithoutCreatedLicensesInputObjectZodSchema = makeSchema();
