import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketCreateWithoutAssignedToUserInputObjectSchema } from './SupportTicketCreateWithoutAssignedToUserInput.schema';
import { SupportTicketUncheckedCreateWithoutAssignedToUserInputObjectSchema } from './SupportTicketUncheckedCreateWithoutAssignedToUserInput.schema';
import { SupportTicketCreateOrConnectWithoutAssignedToUserInputObjectSchema } from './SupportTicketCreateOrConnectWithoutAssignedToUserInput.schema';
import { SupportTicketCreateManyAssignedToUserInputEnvelopeObjectSchema } from './SupportTicketCreateManyAssignedToUserInputEnvelope.schema';
import { SupportTicketWhereUniqueInputObjectSchema } from './SupportTicketWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => SupportTicketCreateWithoutAssignedToUserInputObjectSchema), z.lazy(() => SupportTicketCreateWithoutAssignedToUserInputObjectSchema).array(), z.lazy(() => SupportTicketUncheckedCreateWithoutAssignedToUserInputObjectSchema), z.lazy(() => SupportTicketUncheckedCreateWithoutAssignedToUserInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => SupportTicketCreateOrConnectWithoutAssignedToUserInputObjectSchema), z.lazy(() => SupportTicketCreateOrConnectWithoutAssignedToUserInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => SupportTicketCreateManyAssignedToUserInputEnvelopeObjectSchema).optional(),
  connect: z.union([z.lazy(() => SupportTicketWhereUniqueInputObjectSchema), z.lazy(() => SupportTicketWhereUniqueInputObjectSchema).array()]).optional()
}).strict();
export const SupportTicketCreateNestedManyWithoutAssignedToUserInputObjectSchema: z.ZodType<Prisma.SupportTicketCreateNestedManyWithoutAssignedToUserInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketCreateNestedManyWithoutAssignedToUserInput>;
export const SupportTicketCreateNestedManyWithoutAssignedToUserInputObjectZodSchema = makeSchema();
