import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageScalarWhereInputObjectSchema } from './SupportMessageScalarWhereInput.schema';
import { SupportMessageUpdateManyMutationInputObjectSchema } from './SupportMessageUpdateManyMutationInput.schema';
import { SupportMessageUncheckedUpdateManyWithoutTicketInputObjectSchema } from './SupportMessageUncheckedUpdateManyWithoutTicketInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => SupportMessageScalarWhereInputObjectSchema),
  data: z.union([z.lazy(() => SupportMessageUpdateManyMutationInputObjectSchema), z.lazy(() => SupportMessageUncheckedUpdateManyWithoutTicketInputObjectSchema)])
}).strict();
export const SupportMessageUpdateManyWithWhereWithoutTicketInputObjectSchema: z.ZodType<Prisma.SupportMessageUpdateManyWithWhereWithoutTicketInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageUpdateManyWithWhereWithoutTicketInput>;
export const SupportMessageUpdateManyWithWhereWithoutTicketInputObjectZodSchema = makeSchema();
