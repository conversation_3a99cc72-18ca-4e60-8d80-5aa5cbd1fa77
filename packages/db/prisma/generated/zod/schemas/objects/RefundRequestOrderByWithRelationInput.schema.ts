import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { UserOrderByWithRelationInputObjectSchema } from './UserOrderByWithRelationInput.schema';
import { LicenseOrderByWithRelationInputObjectSchema } from './LicenseOrderByWithRelationInput.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  licenseId: SortOrderSchema.optional(),
  requestedBy: SortOrderSchema.optional(),
  reason: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  requestedAmount: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  approvedAmount: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  stripeRefundIds: SortOrderSchema.optional(),
  adminNotes: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  processedBy: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  createdAt: SortOrderSchema.optional(),
  updatedAt: SortOrderSchema.optional(),
  processedAt: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  processedByUser: z.lazy(() => UserOrderByWithRelationInputObjectSchema).optional(),
  license: z.lazy(() => LicenseOrderByWithRelationInputObjectSchema).optional()
}).strict();
export const RefundRequestOrderByWithRelationInputObjectSchema: z.ZodType<Prisma.RefundRequestOrderByWithRelationInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestOrderByWithRelationInput>;
export const RefundRequestOrderByWithRelationInputObjectZodSchema = makeSchema();
