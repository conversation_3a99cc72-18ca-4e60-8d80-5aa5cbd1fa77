import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketWhereInputObjectSchema } from './SupportTicketWhereInput.schema'

const makeSchema = () => z.object({
  is: z.lazy(() => SupportTicketWhereInputObjectSchema).optional(),
  isNot: z.lazy(() => SupportTicketWhereInputObjectSchema).optional()
}).strict();
export const SupportTicketScalarRelationFilterObjectSchema: z.ZodType<Prisma.SupportTicketScalarRelationFilter> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketScalarRelationFilter>;
export const SupportTicketScalarRelationFilterObjectZodSchema = makeSchema();
