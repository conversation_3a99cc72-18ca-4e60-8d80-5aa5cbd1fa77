import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  requestedAmount: SortOrderSchema.optional(),
  approvedAmount: SortOrderSchema.optional()
}).strict();
export const RefundRequestSumOrderByAggregateInputObjectSchema: z.ZodType<Prisma.RefundRequestSumOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestSumOrderByAggregateInput>;
export const RefundRequestSumOrderByAggregateInputObjectZodSchema = makeSchema();
