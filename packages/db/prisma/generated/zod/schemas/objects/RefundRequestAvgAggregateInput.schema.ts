import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  requestedAmount: z.literal(true).optional(),
  approvedAmount: z.literal(true).optional()
}).strict();
export const RefundRequestAvgAggregateInputObjectSchema: z.ZodType<Prisma.RefundRequestAvgAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestAvgAggregateInputType>;
export const RefundRequestAvgAggregateInputObjectZodSchema = makeSchema();
