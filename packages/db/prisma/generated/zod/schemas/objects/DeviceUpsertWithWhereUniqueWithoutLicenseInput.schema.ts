import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceWhereUniqueInputObjectSchema } from './DeviceWhereUniqueInput.schema';
import { DeviceUpdateWithoutLicenseInputObjectSchema } from './DeviceUpdateWithoutLicenseInput.schema';
import { DeviceUncheckedUpdateWithoutLicenseInputObjectSchema } from './DeviceUncheckedUpdateWithoutLicenseInput.schema';
import { DeviceCreateWithoutLicenseInputObjectSchema } from './DeviceCreateWithoutLicenseInput.schema';
import { DeviceUncheckedCreateWithoutLicenseInputObjectSchema } from './DeviceUncheckedCreateWithoutLicenseInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => DeviceWhereUniqueInputObjectSchema),
  update: z.union([z.lazy(() => DeviceUpdateWithoutLicenseInputObjectSchema), z.lazy(() => DeviceUncheckedUpdateWithoutLicenseInputObjectSchema)]),
  create: z.union([z.lazy(() => DeviceCreateWithoutLicenseInputObjectSchema), z.lazy(() => DeviceUncheckedCreateWithoutLicenseInputObjectSchema)])
}).strict();
export const DeviceUpsertWithWhereUniqueWithoutLicenseInputObjectSchema: z.ZodType<Prisma.DeviceUpsertWithWhereUniqueWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceUpsertWithWhereUniqueWithoutLicenseInput>;
export const DeviceUpsertWithWhereUniqueWithoutLicenseInputObjectZodSchema = makeSchema();
