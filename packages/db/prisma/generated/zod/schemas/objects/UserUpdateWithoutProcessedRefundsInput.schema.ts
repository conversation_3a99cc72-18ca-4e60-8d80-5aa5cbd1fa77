import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { BoolFieldUpdateOperationsInputObjectSchema } from './BoolFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { UserRoleSchema } from '../enums/UserRole.schema';
import { EnumUserRoleFieldUpdateOperationsInputObjectSchema } from './EnumUserRoleFieldUpdateOperationsInput.schema';
import { NullableDateTimeFieldUpdateOperationsInputObjectSchema } from './NullableDateTimeFieldUpdateOperationsInput.schema';
import { SessionUpdateManyWithoutUserNestedInputObjectSchema } from './SessionUpdateManyWithoutUserNestedInput.schema';
import { AccountUpdateManyWithoutUserNestedInputObjectSchema } from './AccountUpdateManyWithoutUserNestedInput.schema';
import { LicenseUpdateManyWithoutCreatedByUserNestedInputObjectSchema } from './LicenseUpdateManyWithoutCreatedByUserNestedInput.schema';
import { UserInvitationUpdateManyWithoutSentByUserNestedInputObjectSchema } from './UserInvitationUpdateManyWithoutSentByUserNestedInput.schema';
import { UserInvitationUpdateManyWithoutAcceptedByUserNestedInputObjectSchema } from './UserInvitationUpdateManyWithoutAcceptedByUserNestedInput.schema';
import { AuditLogUpdateManyWithoutUserNestedInputObjectSchema } from './AuditLogUpdateManyWithoutUserNestedInput.schema';
import { AuditLogUpdateManyWithoutTargetNestedInputObjectSchema } from './AuditLogUpdateManyWithoutTargetNestedInput.schema';
import { SupportTicketUpdateManyWithoutAssignedToUserNestedInputObjectSchema } from './SupportTicketUpdateManyWithoutAssignedToUserNestedInput.schema';
import { SupportMessageUpdateManyWithoutAuthorUserNestedInputObjectSchema } from './SupportMessageUpdateManyWithoutAuthorUserNestedInput.schema'

const makeSchema = () => z.object({
  name: z.union([z.string(), z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(),
  email: z.union([z.string(), z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(),
  emailVerified: z.union([z.boolean(), z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(),
  image: z.union([z.string(), z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  role: z.union([UserRoleSchema, z.lazy(() => EnumUserRoleFieldUpdateOperationsInputObjectSchema)]).optional(),
  isActive: z.union([z.boolean(), z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(),
  invitedBy: z.union([z.string(), z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  invitedAt: z.union([z.coerce.date(), z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  lastLoginAt: z.union([z.coerce.date(), z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  sessions: z.lazy(() => SessionUpdateManyWithoutUserNestedInputObjectSchema).optional(),
  accounts: z.lazy(() => AccountUpdateManyWithoutUserNestedInputObjectSchema).optional(),
  createdLicenses: z.lazy(() => LicenseUpdateManyWithoutCreatedByUserNestedInputObjectSchema).optional(),
  sentInvitations: z.lazy(() => UserInvitationUpdateManyWithoutSentByUserNestedInputObjectSchema).optional(),
  receivedInvitations: z.lazy(() => UserInvitationUpdateManyWithoutAcceptedByUserNestedInputObjectSchema).optional(),
  auditLogsAsActor: z.lazy(() => AuditLogUpdateManyWithoutUserNestedInputObjectSchema).optional(),
  auditLogsAsTarget: z.lazy(() => AuditLogUpdateManyWithoutTargetNestedInputObjectSchema).optional(),
  assignedTickets: z.lazy(() => SupportTicketUpdateManyWithoutAssignedToUserNestedInputObjectSchema).optional(),
  supportMessages: z.lazy(() => SupportMessageUpdateManyWithoutAuthorUserNestedInputObjectSchema).optional()
}).strict();
export const UserUpdateWithoutProcessedRefundsInputObjectSchema: z.ZodType<Prisma.UserUpdateWithoutProcessedRefundsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateWithoutProcessedRefundsInput>;
export const UserUpdateWithoutProcessedRefundsInputObjectZodSchema = makeSchema();
