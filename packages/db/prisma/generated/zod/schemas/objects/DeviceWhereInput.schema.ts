import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { EnumDeviceStatusFilterObjectSchema } from './EnumDeviceStatusFilter.schema';
import { DeviceStatusSchema } from '../enums/DeviceStatus.schema';
import { DateTimeFilterObjectSchema } from './DateTimeFilter.schema';
import { DateTimeNullableFilterObjectSchema } from './DateTimeNullableFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { LicenseScalarRelationFilterObjectSchema } from './LicenseScalarRelationFilter.schema';
import { LicenseWhereInputObjectSchema } from './LicenseWhereInput.schema'

const devicewhereinputSchema = z.object({
  AND: z.union([z.lazy(() => DeviceWhereInputObjectSchema), z.lazy(() => DeviceWhereInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => DeviceWhereInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => DeviceWhereInputObjectSchema), z.lazy(() => DeviceWhereInputObjectSchema).array()]).optional(),
  licenseId: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  deviceHash: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  salt: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  status: z.union([z.lazy(() => EnumDeviceStatusFilterObjectSchema), DeviceStatusSchema]).optional(),
  firstSeen: z.union([z.lazy(() => DateTimeFilterObjectSchema), z.coerce.date()]).optional(),
  lastSeen: z.union([z.lazy(() => DateTimeFilterObjectSchema), z.coerce.date()]).optional(),
  removedAt: z.union([z.lazy(() => DateTimeNullableFilterObjectSchema), z.coerce.date()]).optional().nullable(),
  appVersion: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  deviceName: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  deviceType: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  deviceModel: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  operatingSystem: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  architecture: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  screenResolution: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  totalMemory: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  userNickname: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  location: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  notes: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  license: z.union([z.lazy(() => LicenseScalarRelationFilterObjectSchema), z.lazy(() => LicenseWhereInputObjectSchema)]).optional()
}).strict();
export const DeviceWhereInputObjectSchema: z.ZodType<Prisma.DeviceWhereInput> = devicewhereinputSchema as unknown as z.ZodType<Prisma.DeviceWhereInput>;
export const DeviceWhereInputObjectZodSchema = devicewhereinputSchema;
