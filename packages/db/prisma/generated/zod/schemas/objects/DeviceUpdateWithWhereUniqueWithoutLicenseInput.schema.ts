import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceWhereUniqueInputObjectSchema } from './DeviceWhereUniqueInput.schema';
import { DeviceUpdateWithoutLicenseInputObjectSchema } from './DeviceUpdateWithoutLicenseInput.schema';
import { DeviceUncheckedUpdateWithoutLicenseInputObjectSchema } from './DeviceUncheckedUpdateWithoutLicenseInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => DeviceWhereUniqueInputObjectSchema),
  data: z.union([z.lazy(() => DeviceUpdateWithoutLicenseInputObjectSchema), z.lazy(() => DeviceUncheckedUpdateWithoutLicenseInputObjectSchema)])
}).strict();
export const DeviceUpdateWithWhereUniqueWithoutLicenseInputObjectSchema: z.ZodType<Prisma.DeviceUpdateWithWhereUniqueWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceUpdateWithWhereUniqueWithoutLicenseInput>;
export const DeviceUpdateWithWhereUniqueWithoutLicenseInputObjectZodSchema = makeSchema();
