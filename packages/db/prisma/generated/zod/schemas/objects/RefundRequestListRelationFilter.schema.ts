import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestWhereInputObjectSchema } from './RefundRequestWhereInput.schema'

const makeSchema = () => z.object({
  every: z.lazy(() => RefundRequestWhereInputObjectSchema).optional(),
  some: z.lazy(() => RefundRequestWhereInputObjectSchema).optional(),
  none: z.lazy(() => RefundRequestWhereInputObjectSchema).optional()
}).strict();
export const RefundRequestListRelationFilterObjectSchema: z.ZodType<Prisma.RefundRequestListRelationFilter> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestListRelationFilter>;
export const RefundRequestListRelationFilterObjectZodSchema = makeSchema();
