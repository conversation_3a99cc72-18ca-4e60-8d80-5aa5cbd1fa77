import { z } from 'zod';
import type { Prisma } from '../../../client';
import { LicenseCountOutputTypeSelectObjectSchema } from './LicenseCountOutputTypeSelect.schema'

const makeSchema = () => z.object({
  select: z.lazy(() => LicenseCountOutputTypeSelectObjectSchema).optional()
}).strict();
export const LicenseCountOutputTypeArgsObjectSchema = makeSchema();
export const LicenseCountOutputTypeArgsObjectZodSchema = makeSchema();
