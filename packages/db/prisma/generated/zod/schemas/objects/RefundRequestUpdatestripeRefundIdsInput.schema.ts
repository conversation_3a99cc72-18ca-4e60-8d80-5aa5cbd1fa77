import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  set: z.string().array().optional(),
  push: z.union([z.string(), z.string().array()]).optional()
}).strict();
export const RefundRequestUpdatestripeRefundIdsInputObjectSchema: z.ZodType<Prisma.RefundRequestUpdatestripeRefundIdsInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestUpdatestripeRefundIdsInput>;
export const RefundRequestUpdatestripeRefundIdsInputObjectZodSchema = makeSchema();
