import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.literal(true).optional(),
  email: z.literal(true).optional(),
  role: z.literal(true).optional(),
  token: z.literal(true).optional(),
  status: z.literal(true).optional(),
  expiresAt: z.literal(true).optional(),
  sentAt: z.literal(true).optional(),
  acceptedAt: z.literal(true).optional(),
  sentBy: z.literal(true).optional(),
  acceptedBy: z.literal(true).optional()
}).strict();
export const UserInvitationMaxAggregateInputObjectSchema: z.ZodType<Prisma.UserInvitationMaxAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationMaxAggregateInputType>;
export const UserInvitationMaxAggregateInputObjectZodSchema = makeSchema();
