import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceExpansionScalarWhereInputObjectSchema } from './DeviceExpansionScalarWhereInput.schema';
import { DeviceExpansionUpdateManyMutationInputObjectSchema } from './DeviceExpansionUpdateManyMutationInput.schema';
import { DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentInputObjectSchema } from './DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => DeviceExpansionScalarWhereInputObjectSchema),
  data: z.union([z.lazy(() => DeviceExpansionUpdateManyMutationInputObjectSchema), z.lazy(() => DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentInputObjectSchema)])
}).strict();
export const DeviceExpansionUpdateManyWithWhereWithoutPaymentIntentInputObjectSchema: z.ZodType<Prisma.DeviceExpansionUpdateManyWithWhereWithoutPaymentIntentInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionUpdateManyWithWhereWithoutPaymentIntentInput>;
export const DeviceExpansionUpdateManyWithWhereWithoutPaymentIntentInputObjectZodSchema = makeSchema();
