import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutCreatedLicensesInputObjectSchema } from './UserCreateWithoutCreatedLicensesInput.schema';
import { UserUncheckedCreateWithoutCreatedLicensesInputObjectSchema } from './UserUncheckedCreateWithoutCreatedLicensesInput.schema';
import { UserCreateOrConnectWithoutCreatedLicensesInputObjectSchema } from './UserCreateOrConnectWithoutCreatedLicensesInput.schema';
import { UserUpsertWithoutCreatedLicensesInputObjectSchema } from './UserUpsertWithoutCreatedLicensesInput.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserUpdateToOneWithWhereWithoutCreatedLicensesInputObjectSchema } from './UserUpdateToOneWithWhereWithoutCreatedLicensesInput.schema';
import { UserUpdateWithoutCreatedLicensesInputObjectSchema } from './UserUpdateWithoutCreatedLicensesInput.schema';
import { UserUncheckedUpdateWithoutCreatedLicensesInputObjectSchema } from './UserUncheckedUpdateWithoutCreatedLicensesInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutCreatedLicensesInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutCreatedLicensesInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutCreatedLicensesInputObjectSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutCreatedLicensesInputObjectSchema).optional(),
  disconnect: z.union([z.boolean(), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  delete: z.union([z.boolean(), z.lazy(() => UserWhereInputObjectSchema)]).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional(),
  update: z.union([z.lazy(() => UserUpdateToOneWithWhereWithoutCreatedLicensesInputObjectSchema), z.lazy(() => UserUpdateWithoutCreatedLicensesInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutCreatedLicensesInputObjectSchema)]).optional()
}).strict();
export const UserUpdateOneWithoutCreatedLicensesNestedInputObjectSchema: z.ZodType<Prisma.UserUpdateOneWithoutCreatedLicensesNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateOneWithoutCreatedLicensesNestedInput>;
export const UserUpdateOneWithoutCreatedLicensesNestedInputObjectZodSchema = makeSchema();
