import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserRoleSchema } from '../enums/UserRole.schema';
import { SessionUncheckedCreateNestedManyWithoutUserInputObjectSchema } from './SessionUncheckedCreateNestedManyWithoutUserInput.schema';
import { AccountUncheckedCreateNestedManyWithoutUserInputObjectSchema } from './AccountUncheckedCreateNestedManyWithoutUserInput.schema';
import { LicenseUncheckedCreateNestedManyWithoutCreatedByUserInputObjectSchema } from './LicenseUncheckedCreateNestedManyWithoutCreatedByUserInput.schema';
import { UserInvitationUncheckedCreateNestedManyWithoutSentByUserInputObjectSchema } from './UserInvitationUncheckedCreateNestedManyWithoutSentByUserInput.schema';
import { AuditLogUncheckedCreateNestedManyWithoutUserInputObjectSchema } from './AuditLogUncheckedCreateNestedManyWithoutUserInput.schema';
import { AuditLogUncheckedCreateNestedManyWithoutTargetInputObjectSchema } from './AuditLogUncheckedCreateNestedManyWithoutTargetInput.schema';
import { RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInputObjectSchema } from './RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput.schema';
import { SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInputObjectSchema } from './SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInput.schema';
import { SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInputObjectSchema } from './SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInput.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  name: z.string(),
  email: z.string(),
  emailVerified: z.boolean().optional(),
  image: z.string().optional().nullable(),
  role: UserRoleSchema.optional(),
  isActive: z.boolean().optional(),
  invitedBy: z.string().optional().nullable(),
  invitedAt: z.coerce.date().optional().nullable(),
  lastLoginAt: z.coerce.date().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  sessions: z.lazy(() => SessionUncheckedCreateNestedManyWithoutUserInputObjectSchema).optional(),
  accounts: z.lazy(() => AccountUncheckedCreateNestedManyWithoutUserInputObjectSchema).optional(),
  createdLicenses: z.lazy(() => LicenseUncheckedCreateNestedManyWithoutCreatedByUserInputObjectSchema).optional(),
  sentInvitations: z.lazy(() => UserInvitationUncheckedCreateNestedManyWithoutSentByUserInputObjectSchema).optional(),
  auditLogsAsActor: z.lazy(() => AuditLogUncheckedCreateNestedManyWithoutUserInputObjectSchema).optional(),
  auditLogsAsTarget: z.lazy(() => AuditLogUncheckedCreateNestedManyWithoutTargetInputObjectSchema).optional(),
  processedRefunds: z.lazy(() => RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInputObjectSchema).optional(),
  assignedTickets: z.lazy(() => SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInputObjectSchema).optional(),
  supportMessages: z.lazy(() => SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInputObjectSchema).optional()
}).strict();
export const UserUncheckedCreateWithoutReceivedInvitationsInputObjectSchema: z.ZodType<Prisma.UserUncheckedCreateWithoutReceivedInvitationsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUncheckedCreateWithoutReceivedInvitationsInput>;
export const UserUncheckedCreateWithoutReceivedInvitationsInputObjectZodSchema = makeSchema();
