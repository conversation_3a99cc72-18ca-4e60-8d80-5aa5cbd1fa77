import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketCreateNestedOneWithoutMessagesInputObjectSchema } from './SupportTicketCreateNestedOneWithoutMessagesInput.schema';
import { UserCreateNestedOneWithoutSupportMessagesInputObjectSchema } from './UserCreateNestedOneWithoutSupportMessagesInput.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  message: z.string(),
  isInternal: z.boolean().optional(),
  authorEmail: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  ticket: z.lazy(() => SupportTicketCreateNestedOneWithoutMessagesInputObjectSchema),
  authorUser: z.lazy(() => UserCreateNestedOneWithoutSupportMessagesInputObjectSchema).optional()
}).strict();
export const SupportMessageCreateInputObjectSchema: z.ZodType<Prisma.SupportMessageCreateInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageCreateInput>;
export const SupportMessageCreateInputObjectZodSchema = makeSchema();
