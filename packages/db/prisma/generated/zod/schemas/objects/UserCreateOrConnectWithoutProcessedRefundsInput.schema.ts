import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserCreateWithoutProcessedRefundsInputObjectSchema } from './UserCreateWithoutProcessedRefundsInput.schema';
import { UserUncheckedCreateWithoutProcessedRefundsInputObjectSchema } from './UserUncheckedCreateWithoutProcessedRefundsInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => UserCreateWithoutProcessedRefundsInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutProcessedRefundsInputObjectSchema)])
}).strict();
export const UserCreateOrConnectWithoutProcessedRefundsInputObjectSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutProcessedRefundsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateOrConnectWithoutProcessedRefundsInput>;
export const UserCreateOrConnectWithoutProcessedRefundsInputObjectZodSchema = makeSchema();
