import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserUpdateWithoutCreatedLicensesInputObjectSchema } from './UserUpdateWithoutCreatedLicensesInput.schema';
import { UserUncheckedUpdateWithoutCreatedLicensesInputObjectSchema } from './UserUncheckedUpdateWithoutCreatedLicensesInput.schema';
import { UserCreateWithoutCreatedLicensesInputObjectSchema } from './UserCreateWithoutCreatedLicensesInput.schema';
import { UserUncheckedCreateWithoutCreatedLicensesInputObjectSchema } from './UserUncheckedCreateWithoutCreatedLicensesInput.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema'

const makeSchema = () => z.object({
  update: z.union([z.lazy(() => UserUpdateWithoutCreatedLicensesInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutCreatedLicensesInputObjectSchema)]),
  create: z.union([z.lazy(() => UserCreateWithoutCreatedLicensesInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutCreatedLicensesInputObjectSchema)]),
  where: z.lazy(() => UserWhereInputObjectSchema).optional()
}).strict();
export const UserUpsertWithoutCreatedLicensesInputObjectSchema: z.ZodType<Prisma.UserUpsertWithoutCreatedLicensesInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpsertWithoutCreatedLicensesInput>;
export const UserUpsertWithoutCreatedLicensesInputObjectZodSchema = makeSchema();
