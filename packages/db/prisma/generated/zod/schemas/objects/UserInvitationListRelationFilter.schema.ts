import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationWhereInputObjectSchema } from './UserInvitationWhereInput.schema'

const makeSchema = () => z.object({
  every: z.lazy(() => UserInvitationWhereInputObjectSchema).optional(),
  some: z.lazy(() => UserInvitationWhereInputObjectSchema).optional(),
  none: z.lazy(() => UserInvitationWhereInputObjectSchema).optional()
}).strict();
export const UserInvitationListRelationFilterObjectSchema: z.ZodType<Prisma.UserInvitationListRelationFilter> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationListRelationFilter>;
export const UserInvitationListRelationFilterObjectZodSchema = makeSchema();
