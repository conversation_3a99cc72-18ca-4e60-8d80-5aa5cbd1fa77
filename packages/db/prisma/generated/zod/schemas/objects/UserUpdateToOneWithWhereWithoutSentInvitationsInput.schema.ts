import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserUpdateWithoutSentInvitationsInputObjectSchema } from './UserUpdateWithoutSentInvitationsInput.schema';
import { UserUncheckedUpdateWithoutSentInvitationsInputObjectSchema } from './UserUncheckedUpdateWithoutSentInvitationsInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserWhereInputObjectSchema).optional(),
  data: z.union([z.lazy(() => UserUpdateWithoutSentInvitationsInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutSentInvitationsInputObjectSchema)])
}).strict();
export const UserUpdateToOneWithWhereWithoutSentInvitationsInputObjectSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutSentInvitationsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutSentInvitationsInput>;
export const UserUpdateToOneWithWhereWithoutSentInvitationsInputObjectZodSchema = makeSchema();
