import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { BoolFilterObjectSchema } from './BoolFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { EnumUserRoleFilterObjectSchema } from './EnumUserRoleFilter.schema';
import { UserRoleSchema } from '../enums/UserRole.schema';
import { DateTimeNullableFilterObjectSchema } from './DateTimeNullableFilter.schema';
import { SessionListRelationFilterObjectSchema } from './SessionListRelationFilter.schema';
import { AccountListRelationFilterObjectSchema } from './AccountListRelationFilter.schema';
import { LicenseListRelationFilterObjectSchema } from './LicenseListRelationFilter.schema';
import { UserInvitationListRelationFilterObjectSchema } from './UserInvitationListRelationFilter.schema';
import { AuditLogListRelationFilterObjectSchema } from './AuditLogListRelationFilter.schema';
import { RefundRequestListRelationFilterObjectSchema } from './RefundRequestListRelationFilter.schema';
import { SupportTicketListRelationFilterObjectSchema } from './SupportTicketListRelationFilter.schema';
import { SupportMessageListRelationFilterObjectSchema } from './SupportMessageListRelationFilter.schema'

const userwhereinputSchema = z.object({
  AND: z.union([z.lazy(() => UserWhereInputObjectSchema), z.lazy(() => UserWhereInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => UserWhereInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => UserWhereInputObjectSchema), z.lazy(() => UserWhereInputObjectSchema).array()]).optional(),
  name: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  email: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  emailVerified: z.union([z.lazy(() => BoolFilterObjectSchema), z.boolean()]).optional(),
  image: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  role: z.union([z.lazy(() => EnumUserRoleFilterObjectSchema), UserRoleSchema]).optional(),
  isActive: z.union([z.lazy(() => BoolFilterObjectSchema), z.boolean()]).optional(),
  invitedBy: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  invitedAt: z.union([z.lazy(() => DateTimeNullableFilterObjectSchema), z.coerce.date()]).optional().nullable(),
  lastLoginAt: z.union([z.lazy(() => DateTimeNullableFilterObjectSchema), z.coerce.date()]).optional().nullable(),
  sessions: z.lazy(() => SessionListRelationFilterObjectSchema).optional(),
  accounts: z.lazy(() => AccountListRelationFilterObjectSchema).optional(),
  createdLicenses: z.lazy(() => LicenseListRelationFilterObjectSchema).optional(),
  sentInvitations: z.lazy(() => UserInvitationListRelationFilterObjectSchema).optional(),
  receivedInvitations: z.lazy(() => UserInvitationListRelationFilterObjectSchema).optional(),
  auditLogsAsActor: z.lazy(() => AuditLogListRelationFilterObjectSchema).optional(),
  auditLogsAsTarget: z.lazy(() => AuditLogListRelationFilterObjectSchema).optional(),
  processedRefunds: z.lazy(() => RefundRequestListRelationFilterObjectSchema).optional(),
  assignedTickets: z.lazy(() => SupportTicketListRelationFilterObjectSchema).optional(),
  supportMessages: z.lazy(() => SupportMessageListRelationFilterObjectSchema).optional()
}).strict();
export const UserWhereInputObjectSchema: z.ZodType<Prisma.UserWhereInput> = userwhereinputSchema as unknown as z.ZodType<Prisma.UserWhereInput>;
export const UserWhereInputObjectZodSchema = userwhereinputSchema;
