import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.string().optional(),
  identifier: z.string(),
  action: z.string(),
  count: z.number().int().optional(),
  windowStart: z.coerce.date().optional(),
  expiresAt: z.coerce.date()
}).strict();
export const RateLimitCreateInputObjectSchema: z.ZodType<Prisma.RateLimitCreateInput> = makeSchema() as unknown as z.ZodType<Prisma.RateLimitCreateInput>;
export const RateLimitCreateInputObjectZodSchema = makeSchema();
