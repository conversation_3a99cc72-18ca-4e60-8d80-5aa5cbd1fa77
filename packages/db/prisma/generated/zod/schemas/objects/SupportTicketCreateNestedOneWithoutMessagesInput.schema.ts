import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketCreateWithoutMessagesInputObjectSchema } from './SupportTicketCreateWithoutMessagesInput.schema';
import { SupportTicketUncheckedCreateWithoutMessagesInputObjectSchema } from './SupportTicketUncheckedCreateWithoutMessagesInput.schema';
import { SupportTicketCreateOrConnectWithoutMessagesInputObjectSchema } from './SupportTicketCreateOrConnectWithoutMessagesInput.schema';
import { SupportTicketWhereUniqueInputObjectSchema } from './SupportTicketWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => SupportTicketCreateWithoutMessagesInputObjectSchema), z.lazy(() => SupportTicketUncheckedCreateWithoutMessagesInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => SupportTicketCreateOrConnectWithoutMessagesInputObjectSchema).optional(),
  connect: z.lazy(() => SupportTicketWhereUniqueInputObjectSchema).optional()
}).strict();
export const SupportTicketCreateNestedOneWithoutMessagesInputObjectSchema: z.ZodType<Prisma.SupportTicketCreateNestedOneWithoutMessagesInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketCreateNestedOneWithoutMessagesInput>;
export const SupportTicketCreateNestedOneWithoutMessagesInputObjectZodSchema = makeSchema();
