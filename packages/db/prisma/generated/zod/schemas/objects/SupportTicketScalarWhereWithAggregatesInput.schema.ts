import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringWithAggregatesFilterObjectSchema } from './StringWithAggregatesFilter.schema';
import { EnumTicketStatusWithAggregatesFilterObjectSchema } from './EnumTicketStatusWithAggregatesFilter.schema';
import { TicketStatusSchema } from '../enums/TicketStatus.schema';
import { EnumTicketPriorityWithAggregatesFilterObjectSchema } from './EnumTicketPriorityWithAggregatesFilter.schema';
import { TicketPrioritySchema } from '../enums/TicketPriority.schema';
import { EnumTicketCategoryNullableWithAggregatesFilterObjectSchema } from './EnumTicketCategoryNullableWithAggregatesFilter.schema';
import { TicketCategorySchema } from '../enums/TicketCategory.schema';
import { StringNullableWithAggregatesFilterObjectSchema } from './StringNullableWithAggregatesFilter.schema';
import { DateTimeNullableWithAggregatesFilterObjectSchema } from './DateTimeNullableWithAggregatesFilter.schema'

const supportticketscalarwherewithaggregatesinputSchema = z.object({
  AND: z.union([z.lazy(() => SupportTicketScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => SupportTicketScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => SupportTicketScalarWhereWithAggregatesInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => SupportTicketScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => SupportTicketScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  ticketId: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  subject: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  description: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  status: z.union([z.lazy(() => EnumTicketStatusWithAggregatesFilterObjectSchema), TicketStatusSchema]).optional(),
  priority: z.union([z.lazy(() => EnumTicketPriorityWithAggregatesFilterObjectSchema), TicketPrioritySchema]).optional(),
  category: z.union([z.lazy(() => EnumTicketCategoryNullableWithAggregatesFilterObjectSchema), TicketCategorySchema]).optional().nullable(),
  customerEmail: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  customerName: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  licenseKey: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  assignedTo: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  resolvedAt: z.union([z.lazy(() => DateTimeNullableWithAggregatesFilterObjectSchema), z.coerce.date()]).optional().nullable()
}).strict();
export const SupportTicketScalarWhereWithAggregatesInputObjectSchema: z.ZodType<Prisma.SupportTicketScalarWhereWithAggregatesInput> = supportticketscalarwherewithaggregatesinputSchema as unknown as z.ZodType<Prisma.SupportTicketScalarWhereWithAggregatesInput>;
export const SupportTicketScalarWhereWithAggregatesInputObjectZodSchema = supportticketscalarwherewithaggregatesinputSchema;
