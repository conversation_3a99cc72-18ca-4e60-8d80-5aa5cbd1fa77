import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationCreateManyAcceptedByUserInputObjectSchema } from './UserInvitationCreateManyAcceptedByUserInput.schema'

const makeSchema = () => z.object({
  data: z.union([z.lazy(() => UserInvitationCreateManyAcceptedByUserInputObjectSchema), z.lazy(() => UserInvitationCreateManyAcceptedByUserInputObjectSchema).array()]),
  skipDuplicates: z.boolean().optional()
}).strict();
export const UserInvitationCreateManyAcceptedByUserInputEnvelopeObjectSchema: z.ZodType<Prisma.UserInvitationCreateManyAcceptedByUserInputEnvelope> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationCreateManyAcceptedByUserInputEnvelope>;
export const UserInvitationCreateManyAcceptedByUserInputEnvelopeObjectZodSchema = makeSchema();
