import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageCreateWithoutTicketInputObjectSchema } from './SupportMessageCreateWithoutTicketInput.schema';
import { SupportMessageUncheckedCreateWithoutTicketInputObjectSchema } from './SupportMessageUncheckedCreateWithoutTicketInput.schema';
import { SupportMessageCreateOrConnectWithoutTicketInputObjectSchema } from './SupportMessageCreateOrConnectWithoutTicketInput.schema';
import { SupportMessageCreateManyTicketInputEnvelopeObjectSchema } from './SupportMessageCreateManyTicketInputEnvelope.schema';
import { SupportMessageWhereUniqueInputObjectSchema } from './SupportMessageWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => SupportMessageCreateWithoutTicketInputObjectSchema), z.lazy(() => SupportMessageCreateWithoutTicketInputObjectSchema).array(), z.lazy(() => SupportMessageUncheckedCreateWithoutTicketInputObjectSchema), z.lazy(() => SupportMessageUncheckedCreateWithoutTicketInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => SupportMessageCreateOrConnectWithoutTicketInputObjectSchema), z.lazy(() => SupportMessageCreateOrConnectWithoutTicketInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => SupportMessageCreateManyTicketInputEnvelopeObjectSchema).optional(),
  connect: z.union([z.lazy(() => SupportMessageWhereUniqueInputObjectSchema), z.lazy(() => SupportMessageWhereUniqueInputObjectSchema).array()]).optional()
}).strict();
export const SupportMessageUncheckedCreateNestedManyWithoutTicketInputObjectSchema: z.ZodType<Prisma.SupportMessageUncheckedCreateNestedManyWithoutTicketInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageUncheckedCreateNestedManyWithoutTicketInput>;
export const SupportMessageUncheckedCreateNestedManyWithoutTicketInputObjectZodSchema = makeSchema();
