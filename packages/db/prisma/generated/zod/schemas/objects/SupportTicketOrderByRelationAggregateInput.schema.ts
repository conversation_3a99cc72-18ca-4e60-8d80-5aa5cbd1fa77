import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  _count: SortOrderSchema.optional()
}).strict();
export const SupportTicketOrderByRelationAggregateInputObjectSchema: z.ZodType<Prisma.SupportTicketOrderByRelationAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketOrderByRelationAggregateInput>;
export const SupportTicketOrderByRelationAggregateInputObjectZodSchema = makeSchema();
