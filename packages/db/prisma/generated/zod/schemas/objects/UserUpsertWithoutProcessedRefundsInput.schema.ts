import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserUpdateWithoutProcessedRefundsInputObjectSchema } from './UserUpdateWithoutProcessedRefundsInput.schema';
import { UserUncheckedUpdateWithoutProcessedRefundsInputObjectSchema } from './UserUncheckedUpdateWithoutProcessedRefundsInput.schema';
import { UserCreateWithoutProcessedRefundsInputObjectSchema } from './UserCreateWithoutProcessedRefundsInput.schema';
import { UserUncheckedCreateWithoutProcessedRefundsInputObjectSchema } from './UserUncheckedCreateWithoutProcessedRefundsInput.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema'

const makeSchema = () => z.object({
  update: z.union([z.lazy(() => UserUpdateWithoutProcessedRefundsInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutProcessedRefundsInputObjectSchema)]),
  create: z.union([z.lazy(() => UserCreateWithoutProcessedRefundsInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutProcessedRefundsInputObjectSchema)]),
  where: z.lazy(() => UserWhereInputObjectSchema).optional()
}).strict();
export const UserUpsertWithoutProcessedRefundsInputObjectSchema: z.ZodType<Prisma.UserUpsertWithoutProcessedRefundsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpsertWithoutProcessedRefundsInput>;
export const UserUpsertWithoutProcessedRefundsInputObjectZodSchema = makeSchema();
