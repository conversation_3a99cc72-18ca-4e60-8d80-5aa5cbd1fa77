import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageCreateWithoutTicketInputObjectSchema } from './SupportMessageCreateWithoutTicketInput.schema';
import { SupportMessageUncheckedCreateWithoutTicketInputObjectSchema } from './SupportMessageUncheckedCreateWithoutTicketInput.schema';
import { SupportMessageCreateOrConnectWithoutTicketInputObjectSchema } from './SupportMessageCreateOrConnectWithoutTicketInput.schema';
import { SupportMessageUpsertWithWhereUniqueWithoutTicketInputObjectSchema } from './SupportMessageUpsertWithWhereUniqueWithoutTicketInput.schema';
import { SupportMessageCreateManyTicketInputEnvelopeObjectSchema } from './SupportMessageCreateManyTicketInputEnvelope.schema';
import { SupportMessageWhereUniqueInputObjectSchema } from './SupportMessageWhereUniqueInput.schema';
import { SupportMessageUpdateWithWhereUniqueWithoutTicketInputObjectSchema } from './SupportMessageUpdateWithWhereUniqueWithoutTicketInput.schema';
import { SupportMessageUpdateManyWithWhereWithoutTicketInputObjectSchema } from './SupportMessageUpdateManyWithWhereWithoutTicketInput.schema';
import { SupportMessageScalarWhereInputObjectSchema } from './SupportMessageScalarWhereInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => SupportMessageCreateWithoutTicketInputObjectSchema), z.lazy(() => SupportMessageCreateWithoutTicketInputObjectSchema).array(), z.lazy(() => SupportMessageUncheckedCreateWithoutTicketInputObjectSchema), z.lazy(() => SupportMessageUncheckedCreateWithoutTicketInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => SupportMessageCreateOrConnectWithoutTicketInputObjectSchema), z.lazy(() => SupportMessageCreateOrConnectWithoutTicketInputObjectSchema).array()]).optional(),
  upsert: z.union([z.lazy(() => SupportMessageUpsertWithWhereUniqueWithoutTicketInputObjectSchema), z.lazy(() => SupportMessageUpsertWithWhereUniqueWithoutTicketInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => SupportMessageCreateManyTicketInputEnvelopeObjectSchema).optional(),
  set: z.union([z.lazy(() => SupportMessageWhereUniqueInputObjectSchema), z.lazy(() => SupportMessageWhereUniqueInputObjectSchema).array()]).optional(),
  disconnect: z.union([z.lazy(() => SupportMessageWhereUniqueInputObjectSchema), z.lazy(() => SupportMessageWhereUniqueInputObjectSchema).array()]).optional(),
  delete: z.union([z.lazy(() => SupportMessageWhereUniqueInputObjectSchema), z.lazy(() => SupportMessageWhereUniqueInputObjectSchema).array()]).optional(),
  connect: z.union([z.lazy(() => SupportMessageWhereUniqueInputObjectSchema), z.lazy(() => SupportMessageWhereUniqueInputObjectSchema).array()]).optional(),
  update: z.union([z.lazy(() => SupportMessageUpdateWithWhereUniqueWithoutTicketInputObjectSchema), z.lazy(() => SupportMessageUpdateWithWhereUniqueWithoutTicketInputObjectSchema).array()]).optional(),
  updateMany: z.union([z.lazy(() => SupportMessageUpdateManyWithWhereWithoutTicketInputObjectSchema), z.lazy(() => SupportMessageUpdateManyWithWhereWithoutTicketInputObjectSchema).array()]).optional(),
  deleteMany: z.union([z.lazy(() => SupportMessageScalarWhereInputObjectSchema), z.lazy(() => SupportMessageScalarWhereInputObjectSchema).array()]).optional()
}).strict();
export const SupportMessageUncheckedUpdateManyWithoutTicketNestedInputObjectSchema: z.ZodType<Prisma.SupportMessageUncheckedUpdateManyWithoutTicketNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageUncheckedUpdateManyWithoutTicketNestedInput>;
export const SupportMessageUncheckedUpdateManyWithoutTicketNestedInputObjectZodSchema = makeSchema();
