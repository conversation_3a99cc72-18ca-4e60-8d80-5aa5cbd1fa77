import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { BoolFilterObjectSchema } from './BoolFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { SupportTicketScalarRelationFilterObjectSchema } from './SupportTicketScalarRelationFilter.schema';
import { SupportTicketWhereInputObjectSchema } from './SupportTicketWhereInput.schema';
import { UserNullableScalarRelationFilterObjectSchema } from './UserNullableScalarRelationFilter.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema'

const supportmessagewhereinputSchema = z.object({
  AND: z.union([z.lazy(() => SupportMessageWhereInputObjectSchema), z.lazy(() => SupportMessageWhereInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => SupportMessageWhereInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => SupportMessageWhereInputObjectSchema), z.lazy(() => SupportMessageWhereInputObjectSchema).array()]).optional(),
  ticketId: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  message: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  isInternal: z.union([z.lazy(() => BoolFilterObjectSchema), z.boolean()]).optional(),
  authorEmail: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  authorId: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  ticket: z.union([z.lazy(() => SupportTicketScalarRelationFilterObjectSchema), z.lazy(() => SupportTicketWhereInputObjectSchema)]).optional(),
  authorUser: z.union([z.lazy(() => UserNullableScalarRelationFilterObjectSchema), z.lazy(() => UserWhereInputObjectSchema)]).optional()
}).strict();
export const SupportMessageWhereInputObjectSchema: z.ZodType<Prisma.SupportMessageWhereInput> = supportmessagewhereinputSchema as unknown as z.ZodType<Prisma.SupportMessageWhereInput>;
export const SupportMessageWhereInputObjectZodSchema = supportmessagewhereinputSchema;
