import { z } from 'zod';
import type { <PERSON>risma } from '../../../client';
import { StringWithAggregatesFilterObjectSchema } from './StringWithAggregatesFilter.schema';
import { EnumDeviceStatusWithAggregatesFilterObjectSchema } from './EnumDeviceStatusWithAggregatesFilter.schema';
import { DeviceStatusSchema } from '../enums/DeviceStatus.schema';
import { DateTimeWithAggregatesFilterObjectSchema } from './DateTimeWithAggregatesFilter.schema';
import { DateTimeNullableWithAggregatesFilterObjectSchema } from './DateTimeNullableWithAggregatesFilter.schema';
import { StringNullableWithAggregatesFilterObjectSchema } from './StringNullableWithAggregatesFilter.schema'

const devicescalarwherewithaggregatesinputSchema = z.object({
  AND: z.union([z.lazy(() => DeviceScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => DeviceScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => DeviceScalarWhereWithAggregatesInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => DeviceScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => DeviceScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  licenseId: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  deviceHash: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  salt: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  status: z.union([z.lazy(() => EnumDeviceStatusWithAggregatesFilterObjectSchema), DeviceStatusSchema]).optional(),
  firstSeen: z.union([z.lazy(() => DateTimeWithAggregatesFilterObjectSchema), z.coerce.date()]).optional(),
  lastSeen: z.union([z.lazy(() => DateTimeWithAggregatesFilterObjectSchema), z.coerce.date()]).optional(),
  removedAt: z.union([z.lazy(() => DateTimeNullableWithAggregatesFilterObjectSchema), z.coerce.date()]).optional().nullable(),
  appVersion: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  deviceName: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  deviceType: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  deviceModel: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  operatingSystem: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  architecture: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  screenResolution: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  totalMemory: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  userNickname: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  location: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  notes: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable()
}).strict();
export const DeviceScalarWhereWithAggregatesInputObjectSchema: z.ZodType<Prisma.DeviceScalarWhereWithAggregatesInput> = devicescalarwherewithaggregatesinputSchema as unknown as z.ZodType<Prisma.DeviceScalarWhereWithAggregatesInput>;
export const DeviceScalarWhereWithAggregatesInputObjectZodSchema = devicescalarwherewithaggregatesinputSchema;
