import { z } from 'zod';
import type { Prisma } from '../../../client';
import { WebhookEventWhereUniqueInputObjectSchema } from './WebhookEventWhereUniqueInput.schema';
import { WebhookEventUpdateWithoutPaymentIntentInputObjectSchema } from './WebhookEventUpdateWithoutPaymentIntentInput.schema';
import { WebhookEventUncheckedUpdateWithoutPaymentIntentInputObjectSchema } from './WebhookEventUncheckedUpdateWithoutPaymentIntentInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => WebhookEventWhereUniqueInputObjectSchema),
  data: z.union([z.lazy(() => WebhookEventUpdateWithoutPaymentIntentInputObjectSchema), z.lazy(() => WebhookEventUncheckedUpdateWithoutPaymentIntentInputObjectSchema)])
}).strict();
export const WebhookEventUpdateWithWhereUniqueWithoutPaymentIntentInputObjectSchema: z.ZodType<Prisma.WebhookEventUpdateWithWhereUniqueWithoutPaymentIntentInput> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventUpdateWithWhereUniqueWithoutPaymentIntentInput>;
export const WebhookEventUpdateWithWhereUniqueWithoutPaymentIntentInputObjectZodSchema = makeSchema();
