import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  devices: z.boolean().optional(),
  deviceExpansions: z.boolean().optional(),
  refundRequests: z.boolean().optional()
}).strict();
export const LicenseCountOutputTypeSelectObjectSchema: z.ZodType<Prisma.LicenseCountOutputTypeSelect> = makeSchema() as unknown as z.ZodType<Prisma.LicenseCountOutputTypeSelect>;
export const LicenseCountOutputTypeSelectObjectZodSchema = makeSchema();
