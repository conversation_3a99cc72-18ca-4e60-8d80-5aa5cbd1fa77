import { z } from 'zod';
import type { Prisma } from '../../../client';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { DeviceExpansionStatusSchema } from '../enums/DeviceExpansionStatus.schema';
import { EnumDeviceExpansionStatusFieldUpdateOperationsInputObjectSchema } from './EnumDeviceExpansionStatusFieldUpdateOperationsInput.schema';
import { NullableDateTimeFieldUpdateOperationsInputObjectSchema } from './NullableDateTimeFieldUpdateOperationsInput.schema';
import { LicenseUpdateOneRequiredWithoutDeviceExpansionsNestedInputObjectSchema } from './LicenseUpdateOneRequiredWithoutDeviceExpansionsNestedInput.schema';
import { PaymentIntentUpdateOneRequiredWithoutDeviceExpansionsNestedInputObjectSchema } from './PaymentIntentUpdateOneRequiredWithoutDeviceExpansionsNestedInput.schema'

const makeSchema = () => z.object({
  additionalDevices: z.union([z.number().int(), z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(),
  amount: z.union([z.number().int(), z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(),
  status: z.union([DeviceExpansionStatusSchema, z.lazy(() => EnumDeviceExpansionStatusFieldUpdateOperationsInputObjectSchema)]).optional(),
  processedAt: z.union([z.coerce.date(), z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  license: z.lazy(() => LicenseUpdateOneRequiredWithoutDeviceExpansionsNestedInputObjectSchema).optional(),
  paymentIntent: z.lazy(() => PaymentIntentUpdateOneRequiredWithoutDeviceExpansionsNestedInputObjectSchema).optional()
}).strict();
export const DeviceExpansionUpdateInputObjectSchema: z.ZodType<Prisma.DeviceExpansionUpdateInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionUpdateInput>;
export const DeviceExpansionUpdateInputObjectZodSchema = makeSchema();
