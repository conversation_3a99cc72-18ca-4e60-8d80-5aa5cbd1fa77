import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserUpdateWithoutSupportMessagesInputObjectSchema } from './UserUpdateWithoutSupportMessagesInput.schema';
import { UserUncheckedUpdateWithoutSupportMessagesInputObjectSchema } from './UserUncheckedUpdateWithoutSupportMessagesInput.schema';
import { UserCreateWithoutSupportMessagesInputObjectSchema } from './UserCreateWithoutSupportMessagesInput.schema';
import { UserUncheckedCreateWithoutSupportMessagesInputObjectSchema } from './UserUncheckedCreateWithoutSupportMessagesInput.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema'

const makeSchema = () => z.object({
  update: z.union([z.lazy(() => UserUpdateWithoutSupportMessagesInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutSupportMessagesInputObjectSchema)]),
  create: z.union([z.lazy(() => UserCreateWithoutSupportMessagesInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutSupportMessagesInputObjectSchema)]),
  where: z.lazy(() => UserWhereInputObjectSchema).optional()
}).strict();
export const UserUpsertWithoutSupportMessagesInputObjectSchema: z.ZodType<Prisma.UserUpsertWithoutSupportMessagesInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpsertWithoutSupportMessagesInput>;
export const UserUpsertWithoutSupportMessagesInputObjectZodSchema = makeSchema();
