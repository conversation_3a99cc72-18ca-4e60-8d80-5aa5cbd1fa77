import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketUpdateWithoutMessagesInputObjectSchema } from './SupportTicketUpdateWithoutMessagesInput.schema';
import { SupportTicketUncheckedUpdateWithoutMessagesInputObjectSchema } from './SupportTicketUncheckedUpdateWithoutMessagesInput.schema';
import { SupportTicketCreateWithoutMessagesInputObjectSchema } from './SupportTicketCreateWithoutMessagesInput.schema';
import { SupportTicketUncheckedCreateWithoutMessagesInputObjectSchema } from './SupportTicketUncheckedCreateWithoutMessagesInput.schema';
import { SupportTicketWhereInputObjectSchema } from './SupportTicketWhereInput.schema'

const makeSchema = () => z.object({
  update: z.union([z.lazy(() => SupportTicketUpdateWithoutMessagesInputObjectSchema), z.lazy(() => SupportTicketUncheckedUpdateWithoutMessagesInputObjectSchema)]),
  create: z.union([z.lazy(() => SupportTicketCreateWithoutMessagesInputObjectSchema), z.lazy(() => SupportTicketUncheckedCreateWithoutMessagesInputObjectSchema)]),
  where: z.lazy(() => SupportTicketWhereInputObjectSchema).optional()
}).strict();
export const SupportTicketUpsertWithoutMessagesInputObjectSchema: z.ZodType<Prisma.SupportTicketUpsertWithoutMessagesInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketUpsertWithoutMessagesInput>;
export const SupportTicketUpsertWithoutMessagesInputObjectZodSchema = makeSchema();
