import { z } from 'zod';
import type { Prisma } from '../../../client';
import { PaymentIntentUpdateWithoutDeviceExpansionsInputObjectSchema } from './PaymentIntentUpdateWithoutDeviceExpansionsInput.schema';
import { PaymentIntentUncheckedUpdateWithoutDeviceExpansionsInputObjectSchema } from './PaymentIntentUncheckedUpdateWithoutDeviceExpansionsInput.schema';
import { PaymentIntentCreateWithoutDeviceExpansionsInputObjectSchema } from './PaymentIntentCreateWithoutDeviceExpansionsInput.schema';
import { PaymentIntentUncheckedCreateWithoutDeviceExpansionsInputObjectSchema } from './PaymentIntentUncheckedCreateWithoutDeviceExpansionsInput.schema';
import { PaymentIntentWhereInputObjectSchema } from './PaymentIntentWhereInput.schema'

const makeSchema = () => z.object({
  update: z.union([z.lazy(() => PaymentIntentUpdateWithoutDeviceExpansionsInputObjectSchema), z.lazy(() => PaymentIntentUncheckedUpdateWithoutDeviceExpansionsInputObjectSchema)]),
  create: z.union([z.lazy(() => PaymentIntentCreateWithoutDeviceExpansionsInputObjectSchema), z.lazy(() => PaymentIntentUncheckedCreateWithoutDeviceExpansionsInputObjectSchema)]),
  where: z.lazy(() => PaymentIntentWhereInputObjectSchema).optional()
}).strict();
export const PaymentIntentUpsertWithoutDeviceExpansionsInputObjectSchema: z.ZodType<Prisma.PaymentIntentUpsertWithoutDeviceExpansionsInput> = makeSchema() as unknown as z.ZodType<Prisma.PaymentIntentUpsertWithoutDeviceExpansionsInput>;
export const PaymentIntentUpsertWithoutDeviceExpansionsInputObjectZodSchema = makeSchema();
