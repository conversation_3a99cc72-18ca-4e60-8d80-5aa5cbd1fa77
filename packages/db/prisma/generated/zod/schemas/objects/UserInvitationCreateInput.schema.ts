import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserRoleSchema } from '../enums/UserRole.schema';
import { InvitationStatusSchema } from '../enums/InvitationStatus.schema';
import { UserCreateNestedOneWithoutSentInvitationsInputObjectSchema } from './UserCreateNestedOneWithoutSentInvitationsInput.schema';
import { UserCreateNestedOneWithoutReceivedInvitationsInputObjectSchema } from './UserCreateNestedOneWithoutReceivedInvitationsInput.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  email: z.string(),
  role: UserRoleSchema,
  token: z.string(),
  status: InvitationStatusSchema.optional(),
  expiresAt: z.coerce.date(),
  sentAt: z.coerce.date().optional(),
  acceptedAt: z.coerce.date().optional().nullable(),
  sentByUser: z.lazy(() => UserCreateNestedOneWithoutSentInvitationsInputObjectSchema),
  acceptedByUser: z.lazy(() => UserCreateNestedOneWithoutReceivedInvitationsInputObjectSchema).optional()
}).strict();
export const UserInvitationCreateInputObjectSchema: z.ZodType<Prisma.UserInvitationCreateInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationCreateInput>;
export const UserInvitationCreateInputObjectZodSchema = makeSchema();
