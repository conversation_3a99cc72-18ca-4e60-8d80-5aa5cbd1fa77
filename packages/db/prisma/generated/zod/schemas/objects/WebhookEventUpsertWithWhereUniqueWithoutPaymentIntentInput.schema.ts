import { z } from 'zod';
import type { Prisma } from '../../../client';
import { WebhookEventWhereUniqueInputObjectSchema } from './WebhookEventWhereUniqueInput.schema';
import { WebhookEventUpdateWithoutPaymentIntentInputObjectSchema } from './WebhookEventUpdateWithoutPaymentIntentInput.schema';
import { WebhookEventUncheckedUpdateWithoutPaymentIntentInputObjectSchema } from './WebhookEventUncheckedUpdateWithoutPaymentIntentInput.schema';
import { WebhookEventCreateWithoutPaymentIntentInputObjectSchema } from './WebhookEventCreateWithoutPaymentIntentInput.schema';
import { WebhookEventUncheckedCreateWithoutPaymentIntentInputObjectSchema } from './WebhookEventUncheckedCreateWithoutPaymentIntentInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => WebhookEventWhereUniqueInputObjectSchema),
  update: z.union([z.lazy(() => WebhookEventUpdateWithoutPaymentIntentInputObjectSchema), z.lazy(() => WebhookEventUncheckedUpdateWithoutPaymentIntentInputObjectSchema)]),
  create: z.union([z.lazy(() => WebhookEventCreateWithoutPaymentIntentInputObjectSchema), z.lazy(() => WebhookEventUncheckedCreateWithoutPaymentIntentInputObjectSchema)])
}).strict();
export const WebhookEventUpsertWithWhereUniqueWithoutPaymentIntentInputObjectSchema: z.ZodType<Prisma.WebhookEventUpsertWithWhereUniqueWithoutPaymentIntentInput> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventUpsertWithWhereUniqueWithoutPaymentIntentInput>;
export const WebhookEventUpsertWithWhereUniqueWithoutPaymentIntentInputObjectZodSchema = makeSchema();
