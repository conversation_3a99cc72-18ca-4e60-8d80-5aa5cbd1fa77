import { z } from 'zod';
import type { Prisma } from '../../../client';
import { LicenseTypeSchema } from '../enums/LicenseType.schema';
import { LicenseStatusSchema } from '../enums/LicenseStatus.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  licenseKey: z.string(),
  licenseType: LicenseTypeSchema,
  status: LicenseStatusSchema.optional(),
  maxDevices: z.number().int().optional(),
  usedDevices: z.number().int().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  expiresAt: z.coerce.date().optional().nullable(),
  activatedAt: z.coerce.date().optional().nullable(),
  customerEmail: z.string(),
  customerName: z.string().optional().nullable(),
  createdBy: z.string().optional().nullable(),
  totalPaidAmount: z.number().int().optional(),
  refundedAt: z.coerce.date().optional().nullable(),
  refundReason: z.string().optional().nullable(),
  refundAmount: z.number().int().optional().nullable(),
  emailSentAt: z.coerce.date().optional().nullable(),
  emailDeliveryStatus: z.string().optional().nullable()
}).strict();
export const LicenseCreateManyPaymentIntentInputObjectSchema: z.ZodType<Prisma.LicenseCreateManyPaymentIntentInput> = makeSchema() as unknown as z.ZodType<Prisma.LicenseCreateManyPaymentIntentInput>;
export const LicenseCreateManyPaymentIntentInputObjectZodSchema = makeSchema();
