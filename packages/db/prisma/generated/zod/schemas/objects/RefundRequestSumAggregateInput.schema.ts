import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  requestedAmount: z.literal(true).optional(),
  approvedAmount: z.literal(true).optional()
}).strict();
export const RefundRequestSumAggregateInputObjectSchema: z.ZodType<Prisma.RefundRequestSumAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestSumAggregateInputType>;
export const RefundRequestSumAggregateInputObjectZodSchema = makeSchema();
