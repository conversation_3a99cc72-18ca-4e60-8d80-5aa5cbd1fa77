import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceExpansionWhereUniqueInputObjectSchema } from './DeviceExpansionWhereUniqueInput.schema';
import { DeviceExpansionUpdateWithoutPaymentIntentInputObjectSchema } from './DeviceExpansionUpdateWithoutPaymentIntentInput.schema';
import { DeviceExpansionUncheckedUpdateWithoutPaymentIntentInputObjectSchema } from './DeviceExpansionUncheckedUpdateWithoutPaymentIntentInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema),
  data: z.union([z.lazy(() => DeviceExpansionUpdateWithoutPaymentIntentInputObjectSchema), z.lazy(() => DeviceExpansionUncheckedUpdateWithoutPaymentIntentInputObjectSchema)])
}).strict();
export const DeviceExpansionUpdateWithWhereUniqueWithoutPaymentIntentInputObjectSchema: z.ZodType<Prisma.DeviceExpansionUpdateWithWhereUniqueWithoutPaymentIntentInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionUpdateWithWhereUniqueWithoutPaymentIntentInput>;
export const DeviceExpansionUpdateWithWhereUniqueWithoutPaymentIntentInputObjectZodSchema = makeSchema();
