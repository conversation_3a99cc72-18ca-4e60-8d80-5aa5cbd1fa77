import { z } from 'zod';
import type { <PERSON>risma } from '../../../client';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { IntFieldUpdateOperationsInputObjectSchema } from './IntFieldUpdateOperationsInput.schema';
import { DeviceExpansionStatusSchema } from '../enums/DeviceExpansionStatus.schema';
import { EnumDeviceExpansionStatusFieldUpdateOperationsInputObjectSchema } from './EnumDeviceExpansionStatusFieldUpdateOperationsInput.schema';
import { NullableDateTimeFieldUpdateOperationsInputObjectSchema } from './NullableDateTimeFieldUpdateOperationsInput.schema'

const makeSchema = () => z.object({
  licenseId: z.union([z.string(), z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(),
  paymentIntentId: z.union([z.string(), z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(),
  additionalDevices: z.union([z.number().int(), z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(),
  amount: z.union([z.number().int(), z.lazy(() => IntFieldUpdateOperationsInputObjectSchema)]).optional(),
  status: z.union([DeviceExpansionStatusSchema, z.lazy(() => EnumDeviceExpansionStatusFieldUpdateOperationsInputObjectSchema)]).optional(),
  processedAt: z.union([z.coerce.date(), z.lazy(() => NullableDateTimeFieldUpdateOperationsInputObjectSchema)]).optional().nullable()
}).strict();
export const DeviceExpansionUncheckedUpdateInputObjectSchema: z.ZodType<Prisma.DeviceExpansionUncheckedUpdateInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionUncheckedUpdateInput>;
export const DeviceExpansionUncheckedUpdateInputObjectZodSchema = makeSchema();
