import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceExpansionCreateWithoutLicenseInputObjectSchema } from './DeviceExpansionCreateWithoutLicenseInput.schema';
import { DeviceExpansionUncheckedCreateWithoutLicenseInputObjectSchema } from './DeviceExpansionUncheckedCreateWithoutLicenseInput.schema';
import { DeviceExpansionCreateOrConnectWithoutLicenseInputObjectSchema } from './DeviceExpansionCreateOrConnectWithoutLicenseInput.schema';
import { DeviceExpansionUpsertWithWhereUniqueWithoutLicenseInputObjectSchema } from './DeviceExpansionUpsertWithWhereUniqueWithoutLicenseInput.schema';
import { DeviceExpansionCreateManyLicenseInputEnvelopeObjectSchema } from './DeviceExpansionCreateManyLicenseInputEnvelope.schema';
import { DeviceExpansionWhereUniqueInputObjectSchema } from './DeviceExpansionWhereUniqueInput.schema';
import { DeviceExpansionUpdateWithWhereUniqueWithoutLicenseInputObjectSchema } from './DeviceExpansionUpdateWithWhereUniqueWithoutLicenseInput.schema';
import { DeviceExpansionUpdateManyWithWhereWithoutLicenseInputObjectSchema } from './DeviceExpansionUpdateManyWithWhereWithoutLicenseInput.schema';
import { DeviceExpansionScalarWhereInputObjectSchema } from './DeviceExpansionScalarWhereInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => DeviceExpansionCreateWithoutLicenseInputObjectSchema), z.lazy(() => DeviceExpansionCreateWithoutLicenseInputObjectSchema).array(), z.lazy(() => DeviceExpansionUncheckedCreateWithoutLicenseInputObjectSchema), z.lazy(() => DeviceExpansionUncheckedCreateWithoutLicenseInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => DeviceExpansionCreateOrConnectWithoutLicenseInputObjectSchema), z.lazy(() => DeviceExpansionCreateOrConnectWithoutLicenseInputObjectSchema).array()]).optional(),
  upsert: z.union([z.lazy(() => DeviceExpansionUpsertWithWhereUniqueWithoutLicenseInputObjectSchema), z.lazy(() => DeviceExpansionUpsertWithWhereUniqueWithoutLicenseInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => DeviceExpansionCreateManyLicenseInputEnvelopeObjectSchema).optional(),
  set: z.union([z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema), z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema).array()]).optional(),
  disconnect: z.union([z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema), z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema).array()]).optional(),
  delete: z.union([z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema), z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema).array()]).optional(),
  connect: z.union([z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema), z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema).array()]).optional(),
  update: z.union([z.lazy(() => DeviceExpansionUpdateWithWhereUniqueWithoutLicenseInputObjectSchema), z.lazy(() => DeviceExpansionUpdateWithWhereUniqueWithoutLicenseInputObjectSchema).array()]).optional(),
  updateMany: z.union([z.lazy(() => DeviceExpansionUpdateManyWithWhereWithoutLicenseInputObjectSchema), z.lazy(() => DeviceExpansionUpdateManyWithWhereWithoutLicenseInputObjectSchema).array()]).optional(),
  deleteMany: z.union([z.lazy(() => DeviceExpansionScalarWhereInputObjectSchema), z.lazy(() => DeviceExpansionScalarWhereInputObjectSchema).array()]).optional()
}).strict();
export const DeviceExpansionUpdateManyWithoutLicenseNestedInputObjectSchema: z.ZodType<Prisma.DeviceExpansionUpdateManyWithoutLicenseNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionUpdateManyWithoutLicenseNestedInput>;
export const DeviceExpansionUpdateManyWithoutLicenseNestedInputObjectZodSchema = makeSchema();
