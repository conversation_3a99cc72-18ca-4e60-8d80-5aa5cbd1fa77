import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserCreateWithoutSupportMessagesInputObjectSchema } from './UserCreateWithoutSupportMessagesInput.schema';
import { UserUncheckedCreateWithoutSupportMessagesInputObjectSchema } from './UserUncheckedCreateWithoutSupportMessagesInput.schema';
import { UserCreateOrConnectWithoutSupportMessagesInputObjectSchema } from './UserCreateOrConnectWithoutSupportMessagesInput.schema';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => UserCreateWithoutSupportMessagesInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutSupportMessagesInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutSupportMessagesInputObjectSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputObjectSchema).optional()
}).strict();
export const UserCreateNestedOneWithoutSupportMessagesInputObjectSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutSupportMessagesInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateNestedOneWithoutSupportMessagesInput>;
export const UserCreateNestedOneWithoutSupportMessagesInputObjectZodSchema = makeSchema();
