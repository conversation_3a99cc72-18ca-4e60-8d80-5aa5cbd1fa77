import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { EnumUserRoleFilterObjectSchema } from './EnumUserRoleFilter.schema';
import { UserRoleSchema } from '../enums/UserRole.schema';
import { EnumInvitationStatusFilterObjectSchema } from './EnumInvitationStatusFilter.schema';
import { InvitationStatusSchema } from '../enums/InvitationStatus.schema';
import { DateTimeFilterObjectSchema } from './DateTimeFilter.schema';
import { DateTimeNullableFilterObjectSchema } from './DateTimeNullableFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema'

const userinvitationscalarwhereinputSchema = z.object({
  AND: z.union([z.lazy(() => UserInvitationScalarWhereInputObjectSchema), z.lazy(() => UserInvitationScalarWhereInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => UserInvitationScalarWhereInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => UserInvitationScalarWhereInputObjectSchema), z.lazy(() => UserInvitationScalarWhereInputObjectSchema).array()]).optional(),
  email: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  role: z.union([z.lazy(() => EnumUserRoleFilterObjectSchema), UserRoleSchema]).optional(),
  token: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  status: z.union([z.lazy(() => EnumInvitationStatusFilterObjectSchema), InvitationStatusSchema]).optional(),
  expiresAt: z.union([z.lazy(() => DateTimeFilterObjectSchema), z.coerce.date()]).optional(),
  sentAt: z.union([z.lazy(() => DateTimeFilterObjectSchema), z.coerce.date()]).optional(),
  acceptedAt: z.union([z.lazy(() => DateTimeNullableFilterObjectSchema), z.coerce.date()]).optional().nullable(),
  sentBy: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  acceptedBy: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable()
}).strict();
export const UserInvitationScalarWhereInputObjectSchema: z.ZodType<Prisma.UserInvitationScalarWhereInput> = userinvitationscalarwhereinputSchema as unknown as z.ZodType<Prisma.UserInvitationScalarWhereInput>;
export const UserInvitationScalarWhereInputObjectZodSchema = userinvitationscalarwhereinputSchema;
