import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationWhereUniqueInputObjectSchema } from './UserInvitationWhereUniqueInput.schema';
import { UserInvitationUpdateWithoutAcceptedByUserInputObjectSchema } from './UserInvitationUpdateWithoutAcceptedByUserInput.schema';
import { UserInvitationUncheckedUpdateWithoutAcceptedByUserInputObjectSchema } from './UserInvitationUncheckedUpdateWithoutAcceptedByUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserInvitationWhereUniqueInputObjectSchema),
  data: z.union([z.lazy(() => UserInvitationUpdateWithoutAcceptedByUserInputObjectSchema), z.lazy(() => UserInvitationUncheckedUpdateWithoutAcceptedByUserInputObjectSchema)])
}).strict();
export const UserInvitationUpdateWithWhereUniqueWithoutAcceptedByUserInputObjectSchema: z.ZodType<Prisma.UserInvitationUpdateWithWhereUniqueWithoutAcceptedByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationUpdateWithWhereUniqueWithoutAcceptedByUserInput>;
export const UserInvitationUpdateWithWhereUniqueWithoutAcceptedByUserInputObjectZodSchema = makeSchema();
