import { z } from 'zod';
import type { Prisma } from '../../../client';
import { LicenseStatusSchema } from '../enums/LicenseStatus.schema'

const makeSchema = () => z.object({
  set: LicenseStatusSchema.optional()
}).strict();
export const EnumLicenseStatusFieldUpdateOperationsInputObjectSchema: z.ZodType<Prisma.EnumLicenseStatusFieldUpdateOperationsInput> = makeSchema() as unknown as z.ZodType<Prisma.EnumLicenseStatusFieldUpdateOperationsInput>;
export const EnumLicenseStatusFieldUpdateOperationsInputObjectZodSchema = makeSchema();
