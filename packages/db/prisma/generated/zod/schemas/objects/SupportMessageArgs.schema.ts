import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageSelectObjectSchema } from './SupportMessageSelect.schema';
import { SupportMessageIncludeObjectSchema } from './SupportMessageInclude.schema'

const makeSchema = () => z.object({
  select: z.lazy(() => SupportMessageSelectObjectSchema).optional(),
  include: z.lazy(() => SupportMessageIncludeObjectSchema).optional()
}).strict();
export const SupportMessageArgsObjectSchema = makeSchema();
export const SupportMessageArgsObjectZodSchema = makeSchema();
