import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.string().optional(),
  stripeEventId: z.string(),
  eventType: z.string(),
  processed: z.boolean().optional(),
  processedAt: z.coerce.date().optional().nullable(),
  errorMessage: z.string().optional().nullable(),
  retryCount: z.number().int().optional(),
  createdAt: z.coerce.date().optional(),
  paymentIntentId: z.string().optional().nullable()
}).strict();
export const WebhookEventCreateManyInputObjectSchema: z.ZodType<Prisma.WebhookEventCreateManyInput> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventCreateManyInput>;
export const WebhookEventCreateManyInputObjectZodSchema = makeSchema();
