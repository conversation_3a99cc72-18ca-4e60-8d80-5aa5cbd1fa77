import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserUpdateWithoutSentInvitationsInputObjectSchema } from './UserUpdateWithoutSentInvitationsInput.schema';
import { UserUncheckedUpdateWithoutSentInvitationsInputObjectSchema } from './UserUncheckedUpdateWithoutSentInvitationsInput.schema';
import { UserCreateWithoutSentInvitationsInputObjectSchema } from './UserCreateWithoutSentInvitationsInput.schema';
import { UserUncheckedCreateWithoutSentInvitationsInputObjectSchema } from './UserUncheckedCreateWithoutSentInvitationsInput.schema';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema'

const makeSchema = () => z.object({
  update: z.union([z.lazy(() => UserUpdateWithoutSentInvitationsInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutSentInvitationsInputObjectSchema)]),
  create: z.union([z.lazy(() => UserCreateWithoutSentInvitationsInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutSentInvitationsInputObjectSchema)]),
  where: z.lazy(() => UserWhereInputObjectSchema).optional()
}).strict();
export const UserUpsertWithoutSentInvitationsInputObjectSchema: z.ZodType<Prisma.UserUpsertWithoutSentInvitationsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpsertWithoutSentInvitationsInput>;
export const UserUpsertWithoutSentInvitationsInputObjectZodSchema = makeSchema();
