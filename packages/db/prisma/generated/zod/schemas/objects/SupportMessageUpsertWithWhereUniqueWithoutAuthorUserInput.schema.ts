import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageWhereUniqueInputObjectSchema } from './SupportMessageWhereUniqueInput.schema';
import { SupportMessageUpdateWithoutAuthorUserInputObjectSchema } from './SupportMessageUpdateWithoutAuthorUserInput.schema';
import { SupportMessageUncheckedUpdateWithoutAuthorUserInputObjectSchema } from './SupportMessageUncheckedUpdateWithoutAuthorUserInput.schema';
import { SupportMessageCreateWithoutAuthorUserInputObjectSchema } from './SupportMessageCreateWithoutAuthorUserInput.schema';
import { SupportMessageUncheckedCreateWithoutAuthorUserInputObjectSchema } from './SupportMessageUncheckedCreateWithoutAuthorUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => SupportMessageWhereUniqueInputObjectSchema),
  update: z.union([z.lazy(() => SupportMessageUpdateWithoutAuthorUserInputObjectSchema), z.lazy(() => SupportMessageUncheckedUpdateWithoutAuthorUserInputObjectSchema)]),
  create: z.union([z.lazy(() => SupportMessageCreateWithoutAuthorUserInputObjectSchema), z.lazy(() => SupportMessageUncheckedCreateWithoutAuthorUserInputObjectSchema)])
}).strict();
export const SupportMessageUpsertWithWhereUniqueWithoutAuthorUserInputObjectSchema: z.ZodType<Prisma.SupportMessageUpsertWithWhereUniqueWithoutAuthorUserInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageUpsertWithWhereUniqueWithoutAuthorUserInput>;
export const SupportMessageUpsertWithWhereUniqueWithoutAuthorUserInputObjectZodSchema = makeSchema();
