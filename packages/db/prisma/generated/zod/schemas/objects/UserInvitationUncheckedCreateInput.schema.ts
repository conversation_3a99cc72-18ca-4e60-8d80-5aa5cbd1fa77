import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserRoleSchema } from '../enums/UserRole.schema';
import { InvitationStatusSchema } from '../enums/InvitationStatus.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  email: z.string(),
  role: UserRoleSchema,
  token: z.string(),
  status: InvitationStatusSchema.optional(),
  expiresAt: z.coerce.date(),
  sentAt: z.coerce.date().optional(),
  acceptedAt: z.coerce.date().optional().nullable(),
  sentBy: z.string(),
  acceptedBy: z.string().optional().nullable()
}).strict();
export const UserInvitationUncheckedCreateInputObjectSchema: z.ZodType<Prisma.UserInvitationUncheckedCreateInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationUncheckedCreateInput>;
export const UserInvitationUncheckedCreateInputObjectZodSchema = makeSchema();
