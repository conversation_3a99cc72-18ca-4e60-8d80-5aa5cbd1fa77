import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringWithAggregatesFilterObjectSchema } from './StringWithAggregatesFilter.schema';
import { BoolWithAggregatesFilterObjectSchema } from './BoolWithAggregatesFilter.schema';
import { DateTimeNullableWithAggregatesFilterObjectSchema } from './DateTimeNullableWithAggregatesFilter.schema';
import { StringNullableWithAggregatesFilterObjectSchema } from './StringNullableWithAggregatesFilter.schema';
import { IntWithAggregatesFilterObjectSchema } from './IntWithAggregatesFilter.schema'

const webhookeventscalarwherewithaggregatesinputSchema = z.object({
  AND: z.union([z.lazy(() => WebhookEventScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => WebhookEventScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => WebhookEventScalarWhereWithAggregatesInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => WebhookEventScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => WebhookEventScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  stripeEventId: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  eventType: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  processed: z.union([z.lazy(() => BoolWithAggregatesFilterObjectSchema), z.boolean()]).optional(),
  processedAt: z.union([z.lazy(() => DateTimeNullableWithAggregatesFilterObjectSchema), z.coerce.date()]).optional().nullable(),
  errorMessage: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  retryCount: z.union([z.lazy(() => IntWithAggregatesFilterObjectSchema), z.number().int()]).optional(),
  paymentIntentId: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable()
}).strict();
export const WebhookEventScalarWhereWithAggregatesInputObjectSchema: z.ZodType<Prisma.WebhookEventScalarWhereWithAggregatesInput> = webhookeventscalarwherewithaggregatesinputSchema as unknown as z.ZodType<Prisma.WebhookEventScalarWhereWithAggregatesInput>;
export const WebhookEventScalarWhereWithAggregatesInputObjectZodSchema = webhookeventscalarwherewithaggregatesinputSchema;
