import { z } from 'zod';
import type { <PERSON>risma } from '../../../client';
import { StringWithAggregatesFilterObjectSchema } from './StringWithAggregatesFilter.schema';
import { IntWithAggregatesFilterObjectSchema } from './IntWithAggregatesFilter.schema';
import { DateTimeWithAggregatesFilterObjectSchema } from './DateTimeWithAggregatesFilter.schema'

const ratelimitscalarwherewithaggregatesinputSchema = z.object({
  AND: z.union([z.lazy(() => RateLimitScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => RateLimitScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => RateLimitScalarWhereWithAggregatesInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => RateLimitScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => RateLimitScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  identifier: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  action: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  count: z.union([z.lazy(() => IntWithAggregatesFilterObjectSchema), z.number().int()]).optional(),
  windowStart: z.union([z.lazy(() => DateTimeWithAggregatesFilterObjectSchema), z.coerce.date()]).optional(),
  expiresAt: z.union([z.lazy(() => DateTimeWithAggregatesFilterObjectSchema), z.coerce.date()]).optional()
}).strict();
export const RateLimitScalarWhereWithAggregatesInputObjectSchema: z.ZodType<Prisma.RateLimitScalarWhereWithAggregatesInput> = ratelimitscalarwherewithaggregatesinputSchema as unknown as z.ZodType<Prisma.RateLimitScalarWhereWithAggregatesInput>;
export const RateLimitScalarWhereWithAggregatesInputObjectZodSchema = ratelimitscalarwherewithaggregatesinputSchema;
