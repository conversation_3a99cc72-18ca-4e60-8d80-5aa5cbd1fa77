import { z } from 'zod';
import type { Prisma } from '../../../client';
import { LicenseTypeSchema } from '../enums/LicenseType.schema';
import { LicenseStatusSchema } from '../enums/LicenseStatus.schema';
import { UserCreateNestedOneWithoutCreatedLicensesInputObjectSchema } from './UserCreateNestedOneWithoutCreatedLicensesInput.schema';
import { PaymentIntentCreateNestedOneWithoutLicensesInputObjectSchema } from './PaymentIntentCreateNestedOneWithoutLicensesInput.schema';
import { DeviceCreateNestedManyWithoutLicenseInputObjectSchema } from './DeviceCreateNestedManyWithoutLicenseInput.schema';
import { DeviceExpansionCreateNestedManyWithoutLicenseInputObjectSchema } from './DeviceExpansionCreateNestedManyWithoutLicenseInput.schema';
import { RefundRequestCreateNestedManyWithoutLicenseInputObjectSchema } from './RefundRequestCreateNestedManyWithoutLicenseInput.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  licenseKey: z.string(),
  licenseType: LicenseTypeSchema,
  status: LicenseStatusSchema.optional(),
  maxDevices: z.number().int().optional(),
  usedDevices: z.number().int().optional(),
  createdAt: z.coerce.date().optional(),
  expiresAt: z.coerce.date().optional().nullable(),
  activatedAt: z.coerce.date().optional().nullable(),
  customerEmail: z.string(),
  customerName: z.string().optional().nullable(),
  totalPaidAmount: z.number().int().optional(),
  refundedAt: z.coerce.date().optional().nullable(),
  refundReason: z.string().optional().nullable(),
  refundAmount: z.number().int().optional().nullable(),
  emailSentAt: z.coerce.date().optional().nullable(),
  emailDeliveryStatus: z.string().optional().nullable(),
  createdByUser: z.lazy(() => UserCreateNestedOneWithoutCreatedLicensesInputObjectSchema).optional(),
  paymentIntent: z.lazy(() => PaymentIntentCreateNestedOneWithoutLicensesInputObjectSchema).optional(),
  devices: z.lazy(() => DeviceCreateNestedManyWithoutLicenseInputObjectSchema),
  deviceExpansions: z.lazy(() => DeviceExpansionCreateNestedManyWithoutLicenseInputObjectSchema),
  refundRequests: z.lazy(() => RefundRequestCreateNestedManyWithoutLicenseInputObjectSchema)
}).strict();
export const LicenseCreateInputObjectSchema: z.ZodType<Prisma.LicenseCreateInput> = makeSchema() as unknown as z.ZodType<Prisma.LicenseCreateInput>;
export const LicenseCreateInputObjectZodSchema = makeSchema();
