import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketCreateWithoutMessagesInputObjectSchema } from './SupportTicketCreateWithoutMessagesInput.schema';
import { SupportTicketUncheckedCreateWithoutMessagesInputObjectSchema } from './SupportTicketUncheckedCreateWithoutMessagesInput.schema';
import { SupportTicketCreateOrConnectWithoutMessagesInputObjectSchema } from './SupportTicketCreateOrConnectWithoutMessagesInput.schema';
import { SupportTicketUpsertWithoutMessagesInputObjectSchema } from './SupportTicketUpsertWithoutMessagesInput.schema';
import { SupportTicketWhereUniqueInputObjectSchema } from './SupportTicketWhereUniqueInput.schema';
import { SupportTicketUpdateToOneWithWhereWithoutMessagesInputObjectSchema } from './SupportTicketUpdateToOneWithWhereWithoutMessagesInput.schema';
import { SupportTicketUpdateWithoutMessagesInputObjectSchema } from './SupportTicketUpdateWithoutMessagesInput.schema';
import { SupportTicketUncheckedUpdateWithoutMessagesInputObjectSchema } from './SupportTicketUncheckedUpdateWithoutMessagesInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => SupportTicketCreateWithoutMessagesInputObjectSchema), z.lazy(() => SupportTicketUncheckedCreateWithoutMessagesInputObjectSchema)]).optional(),
  connectOrCreate: z.lazy(() => SupportTicketCreateOrConnectWithoutMessagesInputObjectSchema).optional(),
  upsert: z.lazy(() => SupportTicketUpsertWithoutMessagesInputObjectSchema).optional(),
  connect: z.lazy(() => SupportTicketWhereUniqueInputObjectSchema).optional(),
  update: z.union([z.lazy(() => SupportTicketUpdateToOneWithWhereWithoutMessagesInputObjectSchema), z.lazy(() => SupportTicketUpdateWithoutMessagesInputObjectSchema), z.lazy(() => SupportTicketUncheckedUpdateWithoutMessagesInputObjectSchema)]).optional()
}).strict();
export const SupportTicketUpdateOneRequiredWithoutMessagesNestedInputObjectSchema: z.ZodType<Prisma.SupportTicketUpdateOneRequiredWithoutMessagesNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketUpdateOneRequiredWithoutMessagesNestedInput>;
export const SupportTicketUpdateOneRequiredWithoutMessagesNestedInputObjectZodSchema = makeSchema();
