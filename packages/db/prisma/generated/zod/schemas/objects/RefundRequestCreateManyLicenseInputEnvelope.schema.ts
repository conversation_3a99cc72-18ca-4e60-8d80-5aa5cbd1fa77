import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestCreateManyLicenseInputObjectSchema } from './RefundRequestCreateManyLicenseInput.schema'

const makeSchema = () => z.object({
  data: z.union([z.lazy(() => RefundRequestCreateManyLicenseInputObjectSchema), z.lazy(() => RefundRequestCreateManyLicenseInputObjectSchema).array()]),
  skipDuplicates: z.boolean().optional()
}).strict();
export const RefundRequestCreateManyLicenseInputEnvelopeObjectSchema: z.ZodType<Prisma.RefundRequestCreateManyLicenseInputEnvelope> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestCreateManyLicenseInputEnvelope>;
export const RefundRequestCreateManyLicenseInputEnvelopeObjectZodSchema = makeSchema();
