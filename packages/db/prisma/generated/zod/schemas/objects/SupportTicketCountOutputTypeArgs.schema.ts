import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketCountOutputTypeSelectObjectSchema } from './SupportTicketCountOutputTypeSelect.schema'

const makeSchema = () => z.object({
  select: z.lazy(() => SupportTicketCountOutputTypeSelectObjectSchema).optional()
}).strict();
export const SupportTicketCountOutputTypeArgsObjectSchema = makeSchema();
export const SupportTicketCountOutputTypeArgsObjectZodSchema = makeSchema();
