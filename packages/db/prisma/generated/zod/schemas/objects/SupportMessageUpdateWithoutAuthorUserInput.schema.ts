import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFieldUpdateOperationsInputObjectSchema } from './StringFieldUpdateOperationsInput.schema';
import { BoolFieldUpdateOperationsInputObjectSchema } from './BoolFieldUpdateOperationsInput.schema';
import { NullableStringFieldUpdateOperationsInputObjectSchema } from './NullableStringFieldUpdateOperationsInput.schema';
import { SupportTicketUpdateOneRequiredWithoutMessagesNestedInputObjectSchema } from './SupportTicketUpdateOneRequiredWithoutMessagesNestedInput.schema'

const makeSchema = () => z.object({
  message: z.union([z.string(), z.lazy(() => StringFieldUpdateOperationsInputObjectSchema)]).optional(),
  isInternal: z.union([z.boolean(), z.lazy(() => BoolFieldUpdateOperationsInputObjectSchema)]).optional(),
  authorEmail: z.union([z.string(), z.lazy(() => NullableStringFieldUpdateOperationsInputObjectSchema)]).optional().nullable(),
  ticket: z.lazy(() => SupportTicketUpdateOneRequiredWithoutMessagesNestedInputObjectSchema).optional()
}).strict();
export const SupportMessageUpdateWithoutAuthorUserInputObjectSchema: z.ZodType<Prisma.SupportMessageUpdateWithoutAuthorUserInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageUpdateWithoutAuthorUserInput>;
export const SupportMessageUpdateWithoutAuthorUserInputObjectZodSchema = makeSchema();
