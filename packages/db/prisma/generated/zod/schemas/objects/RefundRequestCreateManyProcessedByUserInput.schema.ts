import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundStatusSchema } from '../enums/RefundStatus.schema';
import { RefundRequestCreatestripeRefundIdsInputObjectSchema } from './RefundRequestCreatestripeRefundIdsInput.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  licenseId: z.string(),
  requestedBy: z.string(),
  reason: z.string(),
  status: RefundStatusSchema.optional(),
  requestedAmount: z.number().int().optional().nullable(),
  approvedAmount: z.number().int().optional().nullable(),
  stripeRefundIds: z.union([z.lazy(() => RefundRequestCreatestripeRefundIdsInputObjectSchema), z.string().array()]).optional(),
  adminNotes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  processedAt: z.coerce.date().optional().nullable()
}).strict();
export const RefundRequestCreateManyProcessedByUserInputObjectSchema: z.ZodType<Prisma.RefundRequestCreateManyProcessedByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestCreateManyProcessedByUserInput>;
export const RefundRequestCreateManyProcessedByUserInputObjectZodSchema = makeSchema();
