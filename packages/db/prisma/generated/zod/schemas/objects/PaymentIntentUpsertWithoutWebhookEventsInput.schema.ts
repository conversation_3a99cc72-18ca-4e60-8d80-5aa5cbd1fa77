import { z } from 'zod';
import type { Prisma } from '../../../client';
import { PaymentIntentUpdateWithoutWebhookEventsInputObjectSchema } from './PaymentIntentUpdateWithoutWebhookEventsInput.schema';
import { PaymentIntentUncheckedUpdateWithoutWebhookEventsInputObjectSchema } from './PaymentIntentUncheckedUpdateWithoutWebhookEventsInput.schema';
import { PaymentIntentCreateWithoutWebhookEventsInputObjectSchema } from './PaymentIntentCreateWithoutWebhookEventsInput.schema';
import { PaymentIntentUncheckedCreateWithoutWebhookEventsInputObjectSchema } from './PaymentIntentUncheckedCreateWithoutWebhookEventsInput.schema';
import { PaymentIntentWhereInputObjectSchema } from './PaymentIntentWhereInput.schema'

const makeSchema = () => z.object({
  update: z.union([z.lazy(() => PaymentIntentUpdateWithoutWebhookEventsInputObjectSchema), z.lazy(() => PaymentIntentUncheckedUpdateWithoutWebhookEventsInputObjectSchema)]),
  create: z.union([z.lazy(() => PaymentIntentCreateWithoutWebhookEventsInputObjectSchema), z.lazy(() => PaymentIntentUncheckedCreateWithoutWebhookEventsInputObjectSchema)]),
  where: z.lazy(() => PaymentIntentWhereInputObjectSchema).optional()
}).strict();
export const PaymentIntentUpsertWithoutWebhookEventsInputObjectSchema: z.ZodType<Prisma.PaymentIntentUpsertWithoutWebhookEventsInput> = makeSchema() as unknown as z.ZodType<Prisma.PaymentIntentUpsertWithoutWebhookEventsInput>;
export const PaymentIntentUpsertWithoutWebhookEventsInputObjectZodSchema = makeSchema();
