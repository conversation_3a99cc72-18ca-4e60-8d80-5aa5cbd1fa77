import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestSelectObjectSchema } from './RefundRequestSelect.schema';
import { RefundRequestIncludeObjectSchema } from './RefundRequestInclude.schema'

const makeSchema = () => z.object({
  select: z.lazy(() => RefundRequestSelectObjectSchema).optional(),
  include: z.lazy(() => RefundRequestIncludeObjectSchema).optional()
}).strict();
export const RefundRequestArgsObjectSchema = makeSchema();
export const RefundRequestArgsObjectZodSchema = makeSchema();
