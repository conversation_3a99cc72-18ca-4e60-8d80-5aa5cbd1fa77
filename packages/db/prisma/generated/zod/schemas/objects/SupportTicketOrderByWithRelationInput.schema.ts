import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema';
import { SortOrderInputObjectSchema } from './SortOrderInput.schema';
import { UserOrderByWithRelationInputObjectSchema } from './UserOrderByWithRelationInput.schema';
import { SupportMessageOrderByRelationAggregateInputObjectSchema } from './SupportMessageOrderByRelationAggregateInput.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  ticketId: SortOrderSchema.optional(),
  subject: SortOrderSchema.optional(),
  description: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  priority: SortOrderSchema.optional(),
  category: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  customerEmail: SortOrderSchema.optional(),
  customerName: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  licenseKey: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  assignedTo: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  createdAt: SortOrderSchema.optional(),
  updatedAt: SortOrderSchema.optional(),
  resolvedAt: z.union([SortOrderSchema, z.lazy(() => SortOrderInputObjectSchema)]).optional(),
  assignedToUser: z.lazy(() => UserOrderByWithRelationInputObjectSchema).optional(),
  messages: z.lazy(() => SupportMessageOrderByRelationAggregateInputObjectSchema).optional()
}).strict();
export const SupportTicketOrderByWithRelationInputObjectSchema: z.ZodType<Prisma.SupportTicketOrderByWithRelationInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketOrderByWithRelationInput>;
export const SupportTicketOrderByWithRelationInputObjectZodSchema = makeSchema();
