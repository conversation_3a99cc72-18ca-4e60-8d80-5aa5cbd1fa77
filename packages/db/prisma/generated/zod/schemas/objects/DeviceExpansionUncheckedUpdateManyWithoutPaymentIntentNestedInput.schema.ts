import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceExpansionCreateWithoutPaymentIntentInputObjectSchema } from './DeviceExpansionCreateWithoutPaymentIntentInput.schema';
import { DeviceExpansionUncheckedCreateWithoutPaymentIntentInputObjectSchema } from './DeviceExpansionUncheckedCreateWithoutPaymentIntentInput.schema';
import { DeviceExpansionCreateOrConnectWithoutPaymentIntentInputObjectSchema } from './DeviceExpansionCreateOrConnectWithoutPaymentIntentInput.schema';
import { DeviceExpansionUpsertWithWhereUniqueWithoutPaymentIntentInputObjectSchema } from './DeviceExpansionUpsertWithWhereUniqueWithoutPaymentIntentInput.schema';
import { DeviceExpansionCreateManyPaymentIntentInputEnvelopeObjectSchema } from './DeviceExpansionCreateManyPaymentIntentInputEnvelope.schema';
import { DeviceExpansionWhereUniqueInputObjectSchema } from './DeviceExpansionWhereUniqueInput.schema';
import { DeviceExpansionUpdateWithWhereUniqueWithoutPaymentIntentInputObjectSchema } from './DeviceExpansionUpdateWithWhereUniqueWithoutPaymentIntentInput.schema';
import { DeviceExpansionUpdateManyWithWhereWithoutPaymentIntentInputObjectSchema } from './DeviceExpansionUpdateManyWithWhereWithoutPaymentIntentInput.schema';
import { DeviceExpansionScalarWhereInputObjectSchema } from './DeviceExpansionScalarWhereInput.schema'

const makeSchema = () => z.object({
  create: z.union([z.lazy(() => DeviceExpansionCreateWithoutPaymentIntentInputObjectSchema), z.lazy(() => DeviceExpansionCreateWithoutPaymentIntentInputObjectSchema).array(), z.lazy(() => DeviceExpansionUncheckedCreateWithoutPaymentIntentInputObjectSchema), z.lazy(() => DeviceExpansionUncheckedCreateWithoutPaymentIntentInputObjectSchema).array()]).optional(),
  connectOrCreate: z.union([z.lazy(() => DeviceExpansionCreateOrConnectWithoutPaymentIntentInputObjectSchema), z.lazy(() => DeviceExpansionCreateOrConnectWithoutPaymentIntentInputObjectSchema).array()]).optional(),
  upsert: z.union([z.lazy(() => DeviceExpansionUpsertWithWhereUniqueWithoutPaymentIntentInputObjectSchema), z.lazy(() => DeviceExpansionUpsertWithWhereUniqueWithoutPaymentIntentInputObjectSchema).array()]).optional(),
  createMany: z.lazy(() => DeviceExpansionCreateManyPaymentIntentInputEnvelopeObjectSchema).optional(),
  set: z.union([z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema), z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema).array()]).optional(),
  disconnect: z.union([z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema), z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema).array()]).optional(),
  delete: z.union([z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema), z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema).array()]).optional(),
  connect: z.union([z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema), z.lazy(() => DeviceExpansionWhereUniqueInputObjectSchema).array()]).optional(),
  update: z.union([z.lazy(() => DeviceExpansionUpdateWithWhereUniqueWithoutPaymentIntentInputObjectSchema), z.lazy(() => DeviceExpansionUpdateWithWhereUniqueWithoutPaymentIntentInputObjectSchema).array()]).optional(),
  updateMany: z.union([z.lazy(() => DeviceExpansionUpdateManyWithWhereWithoutPaymentIntentInputObjectSchema), z.lazy(() => DeviceExpansionUpdateManyWithWhereWithoutPaymentIntentInputObjectSchema).array()]).optional(),
  deleteMany: z.union([z.lazy(() => DeviceExpansionScalarWhereInputObjectSchema), z.lazy(() => DeviceExpansionScalarWhereInputObjectSchema).array()]).optional()
}).strict();
export const DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentNestedInputObjectSchema: z.ZodType<Prisma.DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentNestedInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentNestedInput>;
export const DeviceExpansionUncheckedUpdateManyWithoutPaymentIntentNestedInputObjectZodSchema = makeSchema();
