import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketSelectObjectSchema } from './SupportTicketSelect.schema';
import { SupportTicketIncludeObjectSchema } from './SupportTicketInclude.schema'

const makeSchema = () => z.object({
  select: z.lazy(() => SupportTicketSelectObjectSchema).optional(),
  include: z.lazy(() => SupportTicketIncludeObjectSchema).optional()
}).strict();
export const SupportTicketArgsObjectSchema = makeSchema();
export const SupportTicketArgsObjectZodSchema = makeSchema();
