import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { IntFilterObjectSchema } from './IntFilter.schema';
import { DateTimeFilterObjectSchema } from './DateTimeFilter.schema'

const ratelimitwhereinputSchema = z.object({
  AND: z.union([z.lazy(() => RateLimitWhereInputObjectSchema), z.lazy(() => RateLimitWhereInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => RateLimitWhereInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => RateLimitWhereInputObjectSchema), z.lazy(() => RateLimitWhereInputObjectSchema).array()]).optional(),
  identifier: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  action: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  count: z.union([z.lazy(() => IntFilterObjectSchema), z.number().int()]).optional(),
  windowStart: z.union([z.lazy(() => DateTimeFilterObjectSchema), z.coerce.date()]).optional(),
  expiresAt: z.union([z.lazy(() => DateTimeFilterObjectSchema), z.coerce.date()]).optional()
}).strict();
export const RateLimitWhereInputObjectSchema: z.ZodType<Prisma.RateLimitWhereInput> = ratelimitwhereinputSchema as unknown as z.ZodType<Prisma.RateLimitWhereInput>;
export const RateLimitWhereInputObjectZodSchema = ratelimitwhereinputSchema;
