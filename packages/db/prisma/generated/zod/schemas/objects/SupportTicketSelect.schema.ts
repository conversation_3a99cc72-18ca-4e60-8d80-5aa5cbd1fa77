import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserArgsObjectSchema } from './UserArgs.schema';
import { SupportMessageFindManySchema } from '../findManySupportMessage.schema';
import { SupportTicketCountOutputTypeArgsObjectSchema } from './SupportTicketCountOutputTypeArgs.schema'

const makeSchema = () => z.object({
  id: z.boolean().optional(),
  ticketId: z.boolean().optional(),
  subject: z.boolean().optional(),
  description: z.boolean().optional(),
  status: z.boolean().optional(),
  priority: z.boolean().optional(),
  category: z.boolean().optional(),
  customerEmail: z.boolean().optional(),
  customerName: z.boolean().optional(),
  licenseKey: z.boolean().optional(),
  assignedTo: z.boolean().optional(),
  assignedToUser: z.union([z.boolean(), z.lazy(() => UserArgsObjectSchema)]).optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  resolvedAt: z.boolean().optional(),
  messages: z.union([z.boolean(), z.lazy(() => SupportMessageFindManySchema)]).optional(),
  _count: z.union([z.boolean(), z.lazy(() => SupportTicketCountOutputTypeArgsObjectSchema)]).optional()
}).strict();
export const SupportTicketSelectObjectSchema: z.ZodType<Prisma.SupportTicketSelect> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketSelect>;
export const SupportTicketSelectObjectZodSchema = makeSchema();
