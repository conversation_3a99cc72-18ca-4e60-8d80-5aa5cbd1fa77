import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  ticketId: SortOrderSchema.optional(),
  subject: SortOrderSchema.optional(),
  description: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  priority: SortOrderSchema.optional(),
  category: SortOrderSchema.optional(),
  customerEmail: SortOrderSchema.optional(),
  customerName: SortOrderSchema.optional(),
  licenseKey: SortOrderSchema.optional(),
  assignedTo: SortOrderSchema.optional(),
  createdAt: SortOrderSchema.optional(),
  updatedAt: SortOrderSchema.optional(),
  resolvedAt: SortOrderSchema.optional()
}).strict();
export const SupportTicketCountOrderByAggregateInputObjectSchema: z.ZodType<Prisma.SupportTicketCountOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketCountOrderByAggregateInput>;
export const SupportTicketCountOrderByAggregateInputObjectZodSchema = makeSchema();
