import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageWhereUniqueInputObjectSchema } from './SupportMessageWhereUniqueInput.schema';
import { SupportMessageUpdateWithoutTicketInputObjectSchema } from './SupportMessageUpdateWithoutTicketInput.schema';
import { SupportMessageUncheckedUpdateWithoutTicketInputObjectSchema } from './SupportMessageUncheckedUpdateWithoutTicketInput.schema';
import { SupportMessageCreateWithoutTicketInputObjectSchema } from './SupportMessageCreateWithoutTicketInput.schema';
import { SupportMessageUncheckedCreateWithoutTicketInputObjectSchema } from './SupportMessageUncheckedCreateWithoutTicketInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => SupportMessageWhereUniqueInputObjectSchema),
  update: z.union([z.lazy(() => SupportMessageUpdateWithoutTicketInputObjectSchema), z.lazy(() => SupportMessageUncheckedUpdateWithoutTicketInputObjectSchema)]),
  create: z.union([z.lazy(() => SupportMessageCreateWithoutTicketInputObjectSchema), z.lazy(() => SupportMessageUncheckedCreateWithoutTicketInputObjectSchema)])
}).strict();
export const SupportMessageUpsertWithWhereUniqueWithoutTicketInputObjectSchema: z.ZodType<Prisma.SupportMessageUpsertWithWhereUniqueWithoutTicketInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageUpsertWithWhereUniqueWithoutTicketInput>;
export const SupportMessageUpsertWithWhereUniqueWithoutTicketInputObjectZodSchema = makeSchema();
