import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketArgsObjectSchema } from './SupportTicketArgs.schema';
import { UserArgsObjectSchema } from './UserArgs.schema'

const makeSchema = () => z.object({
  ticket: z.union([z.boolean(), z.lazy(() => SupportTicketArgsObjectSchema)]).optional(),
  authorUser: z.union([z.boolean(), z.lazy(() => UserArgsObjectSchema)]).optional()
}).strict();
export const SupportMessageIncludeObjectSchema: z.ZodType<Prisma.SupportMessageInclude> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageInclude>;
export const SupportMessageIncludeObjectZodSchema = makeSchema();
