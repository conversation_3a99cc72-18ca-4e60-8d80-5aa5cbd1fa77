import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketCreateManyAssignedToUserInputObjectSchema } from './SupportTicketCreateManyAssignedToUserInput.schema'

const makeSchema = () => z.object({
  data: z.union([z.lazy(() => SupportTicketCreateManyAssignedToUserInputObjectSchema), z.lazy(() => SupportTicketCreateManyAssignedToUserInputObjectSchema).array()]),
  skipDuplicates: z.boolean().optional()
}).strict();
export const SupportTicketCreateManyAssignedToUserInputEnvelopeObjectSchema: z.ZodType<Prisma.SupportTicketCreateManyAssignedToUserInputEnvelope> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketCreateManyAssignedToUserInputEnvelope>;
export const SupportTicketCreateManyAssignedToUserInputEnvelopeObjectZodSchema = makeSchema();
