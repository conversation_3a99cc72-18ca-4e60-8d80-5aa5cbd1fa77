import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportMessageWhereUniqueInputObjectSchema } from './SupportMessageWhereUniqueInput.schema';
import { SupportMessageUpdateWithoutTicketInputObjectSchema } from './SupportMessageUpdateWithoutTicketInput.schema';
import { SupportMessageUncheckedUpdateWithoutTicketInputObjectSchema } from './SupportMessageUncheckedUpdateWithoutTicketInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => SupportMessageWhereUniqueInputObjectSchema),
  data: z.union([z.lazy(() => SupportMessageUpdateWithoutTicketInputObjectSchema), z.lazy(() => SupportMessageUncheckedUpdateWithoutTicketInputObjectSchema)])
}).strict();
export const SupportMessageUpdateWithWhereUniqueWithoutTicketInputObjectSchema: z.ZodType<Prisma.SupportMessageUpdateWithWhereUniqueWithoutTicketInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageUpdateWithWhereUniqueWithoutTicketInput>;
export const SupportMessageUpdateWithWhereUniqueWithoutTicketInputObjectZodSchema = makeSchema();
