import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  ticketId: SortOrderSchema.optional(),
  message: SortOrderSchema.optional(),
  isInternal: SortOrderSchema.optional(),
  authorEmail: SortOrderSchema.optional(),
  authorId: SortOrderSchema.optional(),
  createdAt: SortOrderSchema.optional()
}).strict();
export const SupportMessageCountOrderByAggregateInputObjectSchema: z.ZodType<Prisma.SupportMessageCountOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportMessageCountOrderByAggregateInput>;
export const SupportMessageCountOrderByAggregateInputObjectZodSchema = makeSchema();
