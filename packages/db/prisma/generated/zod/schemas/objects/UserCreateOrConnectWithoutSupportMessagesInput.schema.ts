import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserWhereUniqueInputObjectSchema } from './UserWhereUniqueInput.schema';
import { UserCreateWithoutSupportMessagesInputObjectSchema } from './UserCreateWithoutSupportMessagesInput.schema';
import { UserUncheckedCreateWithoutSupportMessagesInputObjectSchema } from './UserUncheckedCreateWithoutSupportMessagesInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => UserCreateWithoutSupportMessagesInputObjectSchema), z.lazy(() => UserUncheckedCreateWithoutSupportMessagesInputObjectSchema)])
}).strict();
export const UserCreateOrConnectWithoutSupportMessagesInputObjectSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutSupportMessagesInput> = makeSchema() as unknown as z.ZodType<Prisma.UserCreateOrConnectWithoutSupportMessagesInput>;
export const UserCreateOrConnectWithoutSupportMessagesInputObjectZodSchema = makeSchema();
