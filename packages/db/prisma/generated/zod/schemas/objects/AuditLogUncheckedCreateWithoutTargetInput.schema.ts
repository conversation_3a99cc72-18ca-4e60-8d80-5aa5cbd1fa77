import { z } from 'zod';
import type { <PERSON>risma } from '../../../client';
import { AuditActionSchema } from '../enums/AuditAction.schema';
import { NullableJsonNullValueInputSchema } from '../enums/NullableJsonNullValueInput.schema'

import { JsonValueSchema as jsonSchema } from '../../helpers/json-helpers';

const makeSchema = () => z.object({
  id: z.string().optional(),
  action: AuditActionSchema,
  licenseKey: z.string().optional().nullable(),
  deviceHash: z.string().optional().nullable(),
  licenseId: z.string().optional().nullable(),
  deviceId: z.string().optional().nullable(),
  userId: z.string().optional().nullable(),
  userEmail: z.string().optional().nullable(),
  customerEmail: z.string().optional().nullable(),
  ipAddress: z.string().optional().nullable(),
  userAgent: z.string().optional().nullable(),
  details: z.union([NullableJsonNullValueInputSchema, jsonSchema]).optional(),
  createdAt: z.coerce.date().optional()
}).strict();
export const AuditLogUncheckedCreateWithoutTargetInputObjectSchema: z.ZodType<Prisma.AuditLogUncheckedCreateWithoutTargetInput> = makeSchema() as unknown as z.ZodType<Prisma.AuditLogUncheckedCreateWithoutTargetInput>;
export const AuditLogUncheckedCreateWithoutTargetInputObjectZodSchema = makeSchema();
