import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceExpansionCreateManyLicenseInputObjectSchema } from './DeviceExpansionCreateManyLicenseInput.schema'

const makeSchema = () => z.object({
  data: z.union([z.lazy(() => DeviceExpansionCreateManyLicenseInputObjectSchema), z.lazy(() => DeviceExpansionCreateManyLicenseInputObjectSchema).array()]),
  skipDuplicates: z.boolean().optional()
}).strict();
export const DeviceExpansionCreateManyLicenseInputEnvelopeObjectSchema: z.ZodType<Prisma.DeviceExpansionCreateManyLicenseInputEnvelope> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionCreateManyLicenseInputEnvelope>;
export const DeviceExpansionCreateManyLicenseInputEnvelopeObjectZodSchema = makeSchema();
