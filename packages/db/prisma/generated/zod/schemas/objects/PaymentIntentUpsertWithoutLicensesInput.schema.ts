import { z } from 'zod';
import type { Prisma } from '../../../client';
import { PaymentIntentUpdateWithoutLicensesInputObjectSchema } from './PaymentIntentUpdateWithoutLicensesInput.schema';
import { PaymentIntentUncheckedUpdateWithoutLicensesInputObjectSchema } from './PaymentIntentUncheckedUpdateWithoutLicensesInput.schema';
import { PaymentIntentCreateWithoutLicensesInputObjectSchema } from './PaymentIntentCreateWithoutLicensesInput.schema';
import { PaymentIntentUncheckedCreateWithoutLicensesInputObjectSchema } from './PaymentIntentUncheckedCreateWithoutLicensesInput.schema';
import { PaymentIntentWhereInputObjectSchema } from './PaymentIntentWhereInput.schema'

const makeSchema = () => z.object({
  update: z.union([z.lazy(() => PaymentIntentUpdateWithoutLicensesInputObjectSchema), z.lazy(() => PaymentIntentUncheckedUpdateWithoutLicensesInputObjectSchema)]),
  create: z.union([z.lazy(() => PaymentIntentCreateWithoutLicensesInputObjectSchema), z.lazy(() => PaymentIntentUncheckedCreateWithoutLicensesInputObjectSchema)]),
  where: z.lazy(() => PaymentIntentWhereInputObjectSchema).optional()
}).strict();
export const PaymentIntentUpsertWithoutLicensesInputObjectSchema: z.ZodType<Prisma.PaymentIntentUpsertWithoutLicensesInput> = makeSchema() as unknown as z.ZodType<Prisma.PaymentIntentUpsertWithoutLicensesInput>;
export const PaymentIntentUpsertWithoutLicensesInputObjectZodSchema = makeSchema();
