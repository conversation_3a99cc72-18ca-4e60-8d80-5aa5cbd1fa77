import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  sessions: z.boolean().optional(),
  accounts: z.boolean().optional(),
  createdLicenses: z.boolean().optional(),
  sentInvitations: z.boolean().optional(),
  receivedInvitations: z.boolean().optional(),
  auditLogsAsActor: z.boolean().optional(),
  auditLogsAsTarget: z.boolean().optional(),
  processedRefunds: z.boolean().optional(),
  assignedTickets: z.boolean().optional(),
  supportMessages: z.boolean().optional()
}).strict();
export const UserCountOutputTypeSelectObjectSchema: z.ZodType<Prisma.UserCountOutputTypeSelect> = makeSchema() as unknown as z.ZodType<Prisma.UserCountOutputTypeSelect>;
export const UserCountOutputTypeSelectObjectZodSchema = makeSchema();
