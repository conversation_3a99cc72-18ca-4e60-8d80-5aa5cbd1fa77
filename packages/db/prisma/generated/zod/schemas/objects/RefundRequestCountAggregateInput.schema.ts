import { z } from 'zod';
import type { Prisma } from '../../../client';


const makeSchema = () => z.object({
  id: z.literal(true).optional(),
  licenseId: z.literal(true).optional(),
  requestedBy: z.literal(true).optional(),
  reason: z.literal(true).optional(),
  status: z.literal(true).optional(),
  requestedAmount: z.literal(true).optional(),
  approvedAmount: z.literal(true).optional(),
  stripeRefundIds: z.literal(true).optional(),
  adminNotes: z.literal(true).optional(),
  processedBy: z.literal(true).optional(),
  createdAt: z.literal(true).optional(),
  updatedAt: z.literal(true).optional(),
  processedAt: z.literal(true).optional(),
  _all: z.literal(true).optional()
}).strict();
export const RefundRequestCountAggregateInputObjectSchema: z.ZodType<Prisma.RefundRequestCountAggregateInputType> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestCountAggregateInputType>;
export const RefundRequestCountAggregateInputObjectZodSchema = makeSchema();
