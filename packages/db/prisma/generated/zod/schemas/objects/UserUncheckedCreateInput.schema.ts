import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserRoleSchema } from '../enums/UserRole.schema';
import { SessionUncheckedCreateNestedManyWithoutUserInputObjectSchema } from './SessionUncheckedCreateNestedManyWithoutUserInput.schema';
import { AccountUncheckedCreateNestedManyWithoutUserInputObjectSchema } from './AccountUncheckedCreateNestedManyWithoutUserInput.schema';
import { LicenseUncheckedCreateNestedManyWithoutCreatedByUserInputObjectSchema } from './LicenseUncheckedCreateNestedManyWithoutCreatedByUserInput.schema';
import { UserInvitationUncheckedCreateNestedManyWithoutSentByUserInputObjectSchema } from './UserInvitationUncheckedCreateNestedManyWithoutSentByUserInput.schema';
import { UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInputObjectSchema } from './UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInput.schema';
import { AuditLogUncheckedCreateNestedManyWithoutUserInputObjectSchema } from './AuditLogUncheckedCreateNestedManyWithoutUserInput.schema';
import { AuditLogUncheckedCreateNestedManyWithoutTargetInputObjectSchema } from './AuditLogUncheckedCreateNestedManyWithoutTargetInput.schema';
import { RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInputObjectSchema } from './RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInput.schema';
import { SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInputObjectSchema } from './SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInput.schema';
import { SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInputObjectSchema } from './SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInput.schema'

const makeSchema = () => z.object({
  id: z.string().optional(),
  name: z.string(),
  email: z.string(),
  emailVerified: z.boolean().optional(),
  image: z.string().optional().nullable(),
  role: UserRoleSchema.optional(),
  isActive: z.boolean().optional(),
  invitedBy: z.string().optional().nullable(),
  invitedAt: z.coerce.date().optional().nullable(),
  lastLoginAt: z.coerce.date().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  sessions: z.lazy(() => SessionUncheckedCreateNestedManyWithoutUserInputObjectSchema),
  accounts: z.lazy(() => AccountUncheckedCreateNestedManyWithoutUserInputObjectSchema),
  createdLicenses: z.lazy(() => LicenseUncheckedCreateNestedManyWithoutCreatedByUserInputObjectSchema),
  sentInvitations: z.lazy(() => UserInvitationUncheckedCreateNestedManyWithoutSentByUserInputObjectSchema),
  receivedInvitations: z.lazy(() => UserInvitationUncheckedCreateNestedManyWithoutAcceptedByUserInputObjectSchema),
  auditLogsAsActor: z.lazy(() => AuditLogUncheckedCreateNestedManyWithoutUserInputObjectSchema),
  auditLogsAsTarget: z.lazy(() => AuditLogUncheckedCreateNestedManyWithoutTargetInputObjectSchema),
  processedRefunds: z.lazy(() => RefundRequestUncheckedCreateNestedManyWithoutProcessedByUserInputObjectSchema),
  assignedTickets: z.lazy(() => SupportTicketUncheckedCreateNestedManyWithoutAssignedToUserInputObjectSchema),
  supportMessages: z.lazy(() => SupportMessageUncheckedCreateNestedManyWithoutAuthorUserInputObjectSchema)
}).strict();
export const UserUncheckedCreateInputObjectSchema: z.ZodType<Prisma.UserUncheckedCreateInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUncheckedCreateInput>;
export const UserUncheckedCreateInputObjectZodSchema = makeSchema();
