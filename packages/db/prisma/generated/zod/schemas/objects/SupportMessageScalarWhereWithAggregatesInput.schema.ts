import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringWithAggregatesFilterObjectSchema } from './StringWithAggregatesFilter.schema';
import { BoolWithAggregatesFilterObjectSchema } from './BoolWithAggregatesFilter.schema';
import { StringNullableWithAggregatesFilterObjectSchema } from './StringNullableWithAggregatesFilter.schema'

const supportmessagescalarwherewithaggregatesinputSchema = z.object({
  AND: z.union([z.lazy(() => SupportMessageScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => SupportMessageScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => SupportMessageScalarWhereWithAggregatesInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => SupportMessageScalarWhereWithAggregatesInputObjectSchema), z.lazy(() => SupportMessageScalarWhereWithAggregatesInputObjectSchema).array()]).optional(),
  ticketId: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  message: z.union([z.lazy(() => StringWithAggregatesFilterObjectSchema), z.string()]).optional(),
  isInternal: z.union([z.lazy(() => BoolWithAggregatesFilterObjectSchema), z.boolean()]).optional(),
  authorEmail: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable(),
  authorId: z.union([z.lazy(() => StringNullableWithAggregatesFilterObjectSchema), z.string()]).optional().nullable()
}).strict();
export const SupportMessageScalarWhereWithAggregatesInputObjectSchema: z.ZodType<Prisma.SupportMessageScalarWhereWithAggregatesInput> = supportmessagescalarwherewithaggregatesinputSchema as unknown as z.ZodType<Prisma.SupportMessageScalarWhereWithAggregatesInput>;
export const SupportMessageScalarWhereWithAggregatesInputObjectZodSchema = supportmessagescalarwherewithaggregatesinputSchema;
