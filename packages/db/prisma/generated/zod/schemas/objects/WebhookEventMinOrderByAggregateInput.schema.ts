import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  stripeEventId: SortOrderSchema.optional(),
  eventType: SortOrderSchema.optional(),
  processed: SortOrderSchema.optional(),
  processedAt: SortOrderSchema.optional(),
  errorMessage: SortOrderSchema.optional(),
  retryCount: SortOrderSchema.optional(),
  createdAt: SortOrderSchema.optional(),
  paymentIntentId: SortOrderSchema.optional()
}).strict();
export const WebhookEventMinOrderByAggregateInputObjectSchema: z.ZodType<Prisma.WebhookEventMinOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventMinOrderByAggregateInput>;
export const WebhookEventMinOrderByAggregateInputObjectZodSchema = makeSchema();
