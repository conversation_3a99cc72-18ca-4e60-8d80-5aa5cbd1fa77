import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationScalarWhereInputObjectSchema } from './UserInvitationScalarWhereInput.schema';
import { UserInvitationUpdateManyMutationInputObjectSchema } from './UserInvitationUpdateManyMutationInput.schema';
import { UserInvitationUncheckedUpdateManyWithoutSentByUserInputObjectSchema } from './UserInvitationUncheckedUpdateManyWithoutSentByUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserInvitationScalarWhereInputObjectSchema),
  data: z.union([z.lazy(() => UserInvitationUpdateManyMutationInputObjectSchema), z.lazy(() => UserInvitationUncheckedUpdateManyWithoutSentByUserInputObjectSchema)])
}).strict();
export const UserInvitationUpdateManyWithWhereWithoutSentByUserInputObjectSchema: z.ZodType<Prisma.UserInvitationUpdateManyWithWhereWithoutSentByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationUpdateManyWithWhereWithoutSentByUserInput>;
export const UserInvitationUpdateManyWithWhereWithoutSentByUserInputObjectZodSchema = makeSchema();
