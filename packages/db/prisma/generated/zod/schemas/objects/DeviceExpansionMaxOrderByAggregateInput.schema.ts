import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SortOrderSchema } from '../enums/SortOrder.schema'

const makeSchema = () => z.object({
  id: SortOrderSchema.optional(),
  licenseId: SortOrderSchema.optional(),
  paymentIntentId: SortOrderSchema.optional(),
  additionalDevices: SortOrderSchema.optional(),
  amount: SortOrderSchema.optional(),
  status: SortOrderSchema.optional(),
  createdAt: SortOrderSchema.optional(),
  processedAt: SortOrderSchema.optional()
}).strict();
export const DeviceExpansionMaxOrderByAggregateInputObjectSchema: z.ZodType<Prisma.DeviceExpansionMaxOrderByAggregateInput> = makeSchema() as unknown as z.ZodType<Prisma.DeviceExpansionMaxOrderByAggregateInput>;
export const DeviceExpansionMaxOrderByAggregateInputObjectZodSchema = makeSchema();
