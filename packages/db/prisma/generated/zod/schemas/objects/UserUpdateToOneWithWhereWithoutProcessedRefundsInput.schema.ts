import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserWhereInputObjectSchema } from './UserWhereInput.schema';
import { UserUpdateWithoutProcessedRefundsInputObjectSchema } from './UserUpdateWithoutProcessedRefundsInput.schema';
import { UserUncheckedUpdateWithoutProcessedRefundsInputObjectSchema } from './UserUncheckedUpdateWithoutProcessedRefundsInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserWhereInputObjectSchema).optional(),
  data: z.union([z.lazy(() => UserUpdateWithoutProcessedRefundsInputObjectSchema), z.lazy(() => UserUncheckedUpdateWithoutProcessedRefundsInputObjectSchema)])
}).strict();
export const UserUpdateToOneWithWhereWithoutProcessedRefundsInputObjectSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutProcessedRefundsInput> = makeSchema() as unknown as z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutProcessedRefundsInput>;
export const UserUpdateToOneWithWhereWithoutProcessedRefundsInputObjectZodSchema = makeSchema();
