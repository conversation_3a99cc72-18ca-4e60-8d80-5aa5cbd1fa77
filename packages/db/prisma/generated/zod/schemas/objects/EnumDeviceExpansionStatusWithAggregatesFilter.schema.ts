import { z } from 'zod';
import type { Prisma } from '../../../client';
import { DeviceExpansionStatusSchema } from '../enums/DeviceExpansionStatus.schema';
import { NestedEnumDeviceExpansionStatusWithAggregatesFilterObjectSchema } from './NestedEnumDeviceExpansionStatusWithAggregatesFilter.schema';
import { NestedIntFilterObjectSchema } from './NestedIntFilter.schema';
import { NestedEnumDeviceExpansionStatusFilterObjectSchema } from './NestedEnumDeviceExpansionStatusFilter.schema'

const makeSchema = () => z.object({
  equals: DeviceExpansionStatusSchema.optional(),
  in: DeviceExpansionStatusSchema.array().optional(),
  notIn: DeviceExpansionStatusSchema.array().optional(),
  not: z.union([DeviceExpansionStatusSchema, z.lazy(() => NestedEnumDeviceExpansionStatusWithAggregatesFilterObjectSchema)]).optional(),
  _count: z.lazy(() => NestedIntFilterObjectSchema).optional(),
  _min: z.lazy(() => NestedEnumDeviceExpansionStatusFilterObjectSchema).optional(),
  _max: z.lazy(() => NestedEnumDeviceExpansionStatusFilterObjectSchema).optional()
}).strict();
export const EnumDeviceExpansionStatusWithAggregatesFilterObjectSchema: z.ZodType<Prisma.EnumDeviceExpansionStatusWithAggregatesFilter> = makeSchema() as unknown as z.ZodType<Prisma.EnumDeviceExpansionStatusWithAggregatesFilter>;
export const EnumDeviceExpansionStatusWithAggregatesFilterObjectZodSchema = makeSchema();
