import { z } from 'zod';
import type { Prisma } from '../../../client';
import { LicenseStatusSchema } from '../enums/LicenseStatus.schema';
import { NestedEnumLicenseStatusFilterObjectSchema } from './NestedEnumLicenseStatusFilter.schema'

const makeSchema = () => z.object({
  equals: LicenseStatusSchema.optional(),
  in: LicenseStatusSchema.array().optional(),
  notIn: LicenseStatusSchema.array().optional(),
  not: z.union([LicenseStatusSchema, z.lazy(() => NestedEnumLicenseStatusFilterObjectSchema)]).optional()
}).strict();
export const EnumLicenseStatusFilterObjectSchema: z.ZodType<Prisma.EnumLicenseStatusFilter> = makeSchema() as unknown as z.ZodType<Prisma.EnumLicenseStatusFilter>;
export const EnumLicenseStatusFilterObjectZodSchema = makeSchema();
