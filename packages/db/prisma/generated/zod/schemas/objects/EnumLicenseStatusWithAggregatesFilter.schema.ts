import { z } from 'zod';
import type { Prisma } from '../../../client';
import { LicenseStatusSchema } from '../enums/LicenseStatus.schema';
import { NestedEnumLicenseStatusWithAggregatesFilterObjectSchema } from './NestedEnumLicenseStatusWithAggregatesFilter.schema';
import { NestedIntFilterObjectSchema } from './NestedIntFilter.schema';
import { NestedEnumLicenseStatusFilterObjectSchema } from './NestedEnumLicenseStatusFilter.schema'

const makeSchema = () => z.object({
  equals: LicenseStatusSchema.optional(),
  in: LicenseStatusSchema.array().optional(),
  notIn: LicenseStatusSchema.array().optional(),
  not: z.union([LicenseStatusSchema, z.lazy(() => NestedEnumLicenseStatusWithAggregatesFilterObjectSchema)]).optional(),
  _count: z.lazy(() => NestedIntFilterObjectSchema).optional(),
  _min: z.lazy(() => NestedEnumLicenseStatusFilterObjectSchema).optional(),
  _max: z.lazy(() => NestedEnumLicenseStatusFilterObjectSchema).optional()
}).strict();
export const EnumLicenseStatusWithAggregatesFilterObjectSchema: z.ZodType<Prisma.EnumLicenseStatusWithAggregatesFilter> = makeSchema() as unknown as z.ZodType<Prisma.EnumLicenseStatusWithAggregatesFilter>;
export const EnumLicenseStatusWithAggregatesFilterObjectZodSchema = makeSchema();
