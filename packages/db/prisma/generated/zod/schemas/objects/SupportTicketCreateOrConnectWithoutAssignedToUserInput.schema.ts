import { z } from 'zod';
import type { Prisma } from '../../../client';
import { SupportTicketWhereUniqueInputObjectSchema } from './SupportTicketWhereUniqueInput.schema';
import { SupportTicketCreateWithoutAssignedToUserInputObjectSchema } from './SupportTicketCreateWithoutAssignedToUserInput.schema';
import { SupportTicketUncheckedCreateWithoutAssignedToUserInputObjectSchema } from './SupportTicketUncheckedCreateWithoutAssignedToUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => SupportTicketWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => SupportTicketCreateWithoutAssignedToUserInputObjectSchema), z.lazy(() => SupportTicketUncheckedCreateWithoutAssignedToUserInputObjectSchema)])
}).strict();
export const SupportTicketCreateOrConnectWithoutAssignedToUserInputObjectSchema: z.ZodType<Prisma.SupportTicketCreateOrConnectWithoutAssignedToUserInput> = makeSchema() as unknown as z.ZodType<Prisma.SupportTicketCreateOrConnectWithoutAssignedToUserInput>;
export const SupportTicketCreateOrConnectWithoutAssignedToUserInputObjectZodSchema = makeSchema();
