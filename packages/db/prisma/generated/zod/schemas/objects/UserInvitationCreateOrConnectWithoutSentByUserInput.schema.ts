import { z } from 'zod';
import type { Prisma } from '../../../client';
import { UserInvitationWhereUniqueInputObjectSchema } from './UserInvitationWhereUniqueInput.schema';
import { UserInvitationCreateWithoutSentByUserInputObjectSchema } from './UserInvitationCreateWithoutSentByUserInput.schema';
import { UserInvitationUncheckedCreateWithoutSentByUserInputObjectSchema } from './UserInvitationUncheckedCreateWithoutSentByUserInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => UserInvitationWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => UserInvitationCreateWithoutSentByUserInputObjectSchema), z.lazy(() => UserInvitationUncheckedCreateWithoutSentByUserInputObjectSchema)])
}).strict();
export const UserInvitationCreateOrConnectWithoutSentByUserInputObjectSchema: z.ZodType<Prisma.UserInvitationCreateOrConnectWithoutSentByUserInput> = makeSchema() as unknown as z.ZodType<Prisma.UserInvitationCreateOrConnectWithoutSentByUserInput>;
export const UserInvitationCreateOrConnectWithoutSentByUserInputObjectZodSchema = makeSchema();
