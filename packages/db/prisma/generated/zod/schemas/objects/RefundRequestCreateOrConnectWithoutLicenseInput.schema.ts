import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestWhereUniqueInputObjectSchema } from './RefundRequestWhereUniqueInput.schema';
import { RefundRequestCreateWithoutLicenseInputObjectSchema } from './RefundRequestCreateWithoutLicenseInput.schema';
import { RefundRequestUncheckedCreateWithoutLicenseInputObjectSchema } from './RefundRequestUncheckedCreateWithoutLicenseInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => RefundRequestWhereUniqueInputObjectSchema),
  create: z.union([z.lazy(() => RefundRequestCreateWithoutLicenseInputObjectSchema), z.lazy(() => RefundRequestUncheckedCreateWithoutLicenseInputObjectSchema)])
}).strict();
export const RefundRequestCreateOrConnectWithoutLicenseInputObjectSchema: z.ZodType<Prisma.RefundRequestCreateOrConnectWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestCreateOrConnectWithoutLicenseInput>;
export const RefundRequestCreateOrConnectWithoutLicenseInputObjectZodSchema = makeSchema();
