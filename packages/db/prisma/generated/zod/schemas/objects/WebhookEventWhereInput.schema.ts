import { z } from 'zod';
import type { Prisma } from '../../../client';
import { StringFilterObjectSchema } from './StringFilter.schema';
import { BoolFilterObjectSchema } from './BoolFilter.schema';
import { DateTimeNullableFilterObjectSchema } from './DateTimeNullableFilter.schema';
import { StringNullableFilterObjectSchema } from './StringNullableFilter.schema';
import { IntFilterObjectSchema } from './IntFilter.schema';
import { PaymentIntentNullableScalarRelationFilterObjectSchema } from './PaymentIntentNullableScalarRelationFilter.schema';
import { PaymentIntentWhereInputObjectSchema } from './PaymentIntentWhereInput.schema'

const webhookeventwhereinputSchema = z.object({
  AND: z.union([z.lazy(() => WebhookEventWhereInputObjectSchema), z.lazy(() => WebhookEventWhereInputObjectSchema).array()]).optional(),
  OR: z.lazy(() => WebhookEventWhereInputObjectSchema).array().optional(),
  NOT: z.union([z.lazy(() => WebhookEventWhereInputObjectSchema), z.lazy(() => WebhookEventWhereInputObjectSchema).array()]).optional(),
  stripeEventId: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  eventType: z.union([z.lazy(() => StringFilterObjectSchema), z.string()]).optional(),
  processed: z.union([z.lazy(() => BoolFilterObjectSchema), z.boolean()]).optional(),
  processedAt: z.union([z.lazy(() => DateTimeNullableFilterObjectSchema), z.coerce.date()]).optional().nullable(),
  errorMessage: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  retryCount: z.union([z.lazy(() => IntFilterObjectSchema), z.number().int()]).optional(),
  paymentIntentId: z.union([z.lazy(() => StringNullableFilterObjectSchema), z.string()]).optional().nullable(),
  paymentIntent: z.union([z.lazy(() => PaymentIntentNullableScalarRelationFilterObjectSchema), z.lazy(() => PaymentIntentWhereInputObjectSchema)]).optional()
}).strict();
export const WebhookEventWhereInputObjectSchema: z.ZodType<Prisma.WebhookEventWhereInput> = webhookeventwhereinputSchema as unknown as z.ZodType<Prisma.WebhookEventWhereInput>;
export const WebhookEventWhereInputObjectZodSchema = webhookeventwhereinputSchema;
