import { z } from 'zod';
import type { Prisma } from '../../../client';
import { WebhookEventScalarWhereInputObjectSchema } from './WebhookEventScalarWhereInput.schema';
import { WebhookEventUpdateManyMutationInputObjectSchema } from './WebhookEventUpdateManyMutationInput.schema';
import { WebhookEventUncheckedUpdateManyWithoutPaymentIntentInputObjectSchema } from './WebhookEventUncheckedUpdateManyWithoutPaymentIntentInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => WebhookEventScalarWhereInputObjectSchema),
  data: z.union([z.lazy(() => WebhookEventUpdateManyMutationInputObjectSchema), z.lazy(() => WebhookEventUncheckedUpdateManyWithoutPaymentIntentInputObjectSchema)])
}).strict();
export const WebhookEventUpdateManyWithWhereWithoutPaymentIntentInputObjectSchema: z.ZodType<Prisma.WebhookEventUpdateManyWithWhereWithoutPaymentIntentInput> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventUpdateManyWithWhereWithoutPaymentIntentInput>;
export const WebhookEventUpdateManyWithWhereWithoutPaymentIntentInputObjectZodSchema = makeSchema();
