import { z } from 'zod';
import type { Prisma } from '../../../client';
import { PaymentIntentArgsObjectSchema } from './PaymentIntentArgs.schema'

const makeSchema = () => z.object({
  paymentIntent: z.union([z.boolean(), z.lazy(() => PaymentIntentArgsObjectSchema)]).optional()
}).strict();
export const WebhookEventIncludeObjectSchema: z.ZodType<Prisma.WebhookEventInclude> = makeSchema() as unknown as z.ZodType<Prisma.WebhookEventInclude>;
export const WebhookEventIncludeObjectZodSchema = makeSchema();
