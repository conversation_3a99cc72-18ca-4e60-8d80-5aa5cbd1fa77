import { z } from 'zod';
import type { Prisma } from '../../../client';
import { RefundRequestScalarWhereInputObjectSchema } from './RefundRequestScalarWhereInput.schema';
import { RefundRequestUpdateManyMutationInputObjectSchema } from './RefundRequestUpdateManyMutationInput.schema';
import { RefundRequestUncheckedUpdateManyWithoutLicenseInputObjectSchema } from './RefundRequestUncheckedUpdateManyWithoutLicenseInput.schema'

const makeSchema = () => z.object({
  where: z.lazy(() => RefundRequestScalarWhereInputObjectSchema),
  data: z.union([z.lazy(() => RefundRequestUpdateManyMutationInputObjectSchema), z.lazy(() => RefundRequestUncheckedUpdateManyWithoutLicenseInputObjectSchema)])
}).strict();
export const RefundRequestUpdateManyWithWhereWithoutLicenseInputObjectSchema: z.ZodType<Prisma.RefundRequestUpdateManyWithWhereWithoutLicenseInput> = makeSchema() as unknown as z.ZodType<Prisma.RefundRequestUpdateManyWithWhereWithoutLicenseInput>;
export const RefundRequestUpdateManyWithWhereWithoutLicenseInputObjectZodSchema = makeSchema();
