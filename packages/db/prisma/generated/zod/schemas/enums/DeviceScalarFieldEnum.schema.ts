import { z } from 'zod';

export const DeviceScalarFieldEnumSchema = z.enum(['id', 'licenseId', 'deviceHash', 'salt', 'status', 'firstSeen', 'lastSeen', 'removedAt', 'appVersion', 'deviceName', 'deviceType', 'deviceModel', 'operatingSystem', 'architecture', 'screenResolution', 'totalMemory', 'userNickname', 'location', 'notes'])

export type DeviceScalarFieldEnum = z.infer<typeof DeviceScalarFieldEnumSchema>;