import { z } from 'zod';

export const AuditActionSchema = z.enum(['LICENSE_CREATED', 'TRIAL_LICENSE_CREATED', 'LICENSE_ACTIVATED', 'LICENSE_VALIDATED', 'LICENSE_EXPIRED', 'LICENSE_SUSPENDED', 'LICENSE_REACTIVATED', 'LICENSE_DELETED', 'LICENSE_EXTENDED', 'LICENSE_CANCELLED', 'LICENSE_UPGRADED', 'DEVICE_REGISTERED', 'DEVICE_UPDATED', 'DEVICE_REMOVED', 'DEVICE_EXPANSION_PURCHASED', 'DEVICE_EXPANSION_PROCESSED', 'PAYMENT_INITIATED', 'PAYMENT_SUCCEEDED', 'PAYMENT_FAILED', 'PAYMENT_REFUNDED', 'REFUND_REQUESTED', 'REFUND_APPROVED', 'REFUND_REJECTED', 'REFUND_PROCESSED', 'REFUND_FAILED', 'REFUND_UPDATED', 'USER_CREATED', 'USER_UPDATED', 'USER_ROLE_CHANGED', 'USER_ACTIVATED', 'USER_DEACTIVATED', 'USER_SUSPENDED', 'USER_DELETED', 'USER_LOGIN', 'USER_LOGOUT', 'USER_EMAIL_VERIFIED', 'FIRST_USER_ADMIN_GRANTED', 'INVITATION_SENT', 'INVITATION_ACCEPTED', 'INVITATION_EXPIRED', 'INVITATION_REVOKED', 'INVITATION_RESENT', 'INVITATION_UPDATED', 'INVITATION_CANCELLED', 'USER_CREATED_FROM_INVITATION', 'SUSPICIOUS_ACTIVITY_DETECTED', 'RATE_LIMIT_EXCEEDED', 'UNAUTHORIZED_ACCESS_ATTEMPT', 'MULTIPLE_DEVICE_LIMIT_EXCEEDED', 'WEBHOOK_PROCESSED', 'WEBHOOK_FAILED', 'DATA_EXPORT_REQUESTED', 'DATA_DELETED', 'EMAIL_SENT', 'BULK_EMAIL_SENT', 'EMAIL_DELIVERY_FAILED', 'EMAIL_TEMPLATE_UPDATED', 'NOTIFICATION_CREATED', 'NOTIFICATIONS_MARKED_READ', 'NOTIFICATION_SETTINGS_UPDATED', 'SUPPORT_TICKET_CREATED', 'SUPPORT_TICKET_UPDATED', 'SUPPORT_TICKET_RESOLVED', 'SUPPORT_MESSAGE_SENT'])

export type AuditAction = z.infer<typeof AuditActionSchema>;