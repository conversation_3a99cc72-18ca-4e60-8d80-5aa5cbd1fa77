import { z } from 'zod';

export const LicenseScalarFieldEnumSchema = z.enum(['id', 'licenseKey', 'licenseType', 'status', 'maxDevices', 'usedDevices', 'createdAt', 'updatedAt', 'expiresAt', 'activatedAt', 'customerEmail', 'customerName', 'createdBy', 'paymentIntentId', 'totalPaidAmount', 'refundedAt', 'refundReason', 'refundAmount', 'emailSentAt', 'emailDeliveryStatus'])

export type LicenseScalarFieldEnum = z.infer<typeof LicenseScalarFieldEnumSchema>;