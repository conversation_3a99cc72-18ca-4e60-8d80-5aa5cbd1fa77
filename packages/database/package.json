{"name": "@repo/db", "version": "0.0.0", "scripts": {"db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:start": "docker compose up -d", "db:watch": "docker compose up", "db:stop": "docker compose stop", "db:down": "docker compose down", "db:seed": "bun run prisma/seed.ts"}, "devDependencies": {"prisma": "^6.16.2"}, "dependencies": {"@prisma/client": "^6.16.2", "prisma-zod-generator": "^1.21.3"}, "exports": {".": "./src/index.ts"}}